import { Recipe, CookingStep } from '../types';

export interface SmartDevice {
  id: string;
  name: string;
  type: DeviceType;
  brand: string;
  model: string;
  capabilities: DeviceCapability[];
  status: DeviceStatus;
  connectionType: ConnectionType;
  lastSeen: Date;
  firmwareVersion?: string;
  batteryLevel?: number;
}

export type DeviceType = 
  | 'oven' 
  | 'stovetop' 
  | 'microwave' 
  | 'air_fryer' 
  | 'slow_cooker' 
  | 'pressure_cooker' 
  | 'sous_vide' 
  | 'grill' 
  | 'thermometer' 
  | 'scale' 
  | 'timer' 
  | 'refrigerator' 
  | 'dishwasher'
  | 'coffee_maker'
  | 'blender'
  | 'food_processor';

export type DeviceCapability = 
  | 'temperature_control' 
  | 'timer_control' 
  | 'preheating' 
  | 'auto_shutoff' 
  | 'remote_monitoring' 
  | 'voice_control' 
  | 'recipe_sync' 
  | 'notification_alerts' 
  | 'energy_monitoring'
  | 'weight_measurement'
  | 'temperature_monitoring'
  | 'humidity_control'
  | 'pressure_control';

export type DeviceStatus = 'connected' | 'disconnected' | 'busy' | 'idle' | 'error';
export type ConnectionType = 'wifi' | 'bluetooth' | 'zigbee' | 'zwave' | 'matter' | 'homekit';

export interface CookingAutomation {
  id: string;
  name: string;
  recipeId: string;
  devices: string[];
  steps: AutomationStep[];
  triggers: AutomationTrigger[];
  conditions: AutomationCondition[];
  isActive: boolean;
  createdAt: Date;
  lastExecuted?: Date;
}

export interface AutomationStep {
  stepNumber: number;
  deviceId: string;
  action: DeviceAction;
  parameters: { [key: string]: any };
  delay?: number;
  condition?: string;
}

export interface AutomationTrigger {
  type: 'time' | 'temperature' | 'timer' | 'manual' | 'recipe_start';
  value: any;
  deviceId?: string;
}

export interface AutomationCondition {
  deviceId: string;
  property: string;
  operator: '=' | '>' | '<' | '>=' | '<=' | '!=';
  value: any;
}

export interface DeviceAction {
  type: 'set_temperature' | 'start_timer' | 'preheat' | 'start_cooking' | 'stop_cooking' | 'send_notification' | 'measure_weight' | 'check_temperature';
  parameters: { [key: string]: any };
}

export class SmartDeviceService {
  private static instance: SmartDeviceService;
  private devices: Map<string, SmartDevice> = new Map();
  private automations: Map<string, CookingAutomation> = new Map();
  private activeTimers: Map<string, NodeJS.Timeout> = new Map();
  private deviceListeners: Map<string, Function[]> = new Map();

  private constructor() {
    this.initializeService();
  }

  public static getInstance(): SmartDeviceService {
    if (!SmartDeviceService.instance) {
      SmartDeviceService.instance = new SmartDeviceService();
    }
    return SmartDeviceService.instance;
  }

  private async initializeService(): Promise<void> {
    // Load saved devices and automations
    await this.loadDevicesFromStorage();
    await this.loadAutomationsFromStorage();
    
    // Start device discovery
    this.startDeviceDiscovery();
    
    // Initialize demo devices for testing
    this.initializeDemoDevices();
  }

  private async loadDevicesFromStorage(): Promise<void> {
    try {
      const result = await chrome.storage.local.get(['smartDevices']);
      if (result.smartDevices) {
        const devices = JSON.parse(result.smartDevices);
        devices.forEach((device: SmartDevice) => {
          this.devices.set(device.id, device);
        });
      }
    } catch (error) {
      console.error('Failed to load devices from storage:', error);
    }
  }

  private async loadAutomationsFromStorage(): Promise<void> {
    try {
      const result = await chrome.storage.local.get(['cookingAutomations']);
      if (result.cookingAutomations) {
        const automations = JSON.parse(result.cookingAutomations);
        automations.forEach((automation: CookingAutomation) => {
          this.automations.set(automation.id, automation);
        });
      }
    } catch (error) {
      console.error('Failed to load automations from storage:', error);
    }
  }

  private async saveDevicesToStorage(): Promise<void> {
    try {
      const devices = Array.from(this.devices.values());
      await chrome.storage.local.set({
        smartDevices: JSON.stringify(devices)
      });
    } catch (error) {
      console.error('Failed to save devices to storage:', error);
    }
  }

  private async saveAutomationsToStorage(): Promise<void> {
    try {
      const automations = Array.from(this.automations.values());
      await chrome.storage.local.set({
        cookingAutomations: JSON.stringify(automations)
      });
    } catch (error) {
      console.error('Failed to save automations to storage:', error);
    }
  }

  private startDeviceDiscovery(): void {
    // Simulate device discovery for demo purposes
    // In a real implementation, this would use actual device discovery protocols
    console.log('Starting smart device discovery...');
    
    // Check for Web Bluetooth support
    if ('bluetooth' in navigator) {
      this.discoverBluetoothDevices();
    }
    
    // Check for WebUSB support
    if ('usb' in navigator) {
      this.discoverUSBDevices();
    }
    
    // Simulate network device discovery
    this.discoverNetworkDevices();
  }

  private async discoverBluetoothDevices(): Promise<void> {
    try {
      // This would implement actual Bluetooth device discovery
      console.log('Discovering Bluetooth devices...');
    } catch (error) {
      console.error('Bluetooth discovery failed:', error);
    }
  }

  private async discoverUSBDevices(): Promise<void> {
    try {
      // This would implement actual USB device discovery
      console.log('Discovering USB devices...');
    } catch (error) {
      console.error('USB discovery failed:', error);
    }
  }

  private async discoverNetworkDevices(): Promise<void> {
    try {
      // This would implement actual network device discovery (mDNS, UPnP, etc.)
      console.log('Discovering network devices...');
    } catch (error) {
      console.error('Network discovery failed:', error);
    }
  }

  private initializeDemoDevices(): void {
    const demoDevices: SmartDevice[] = [
      {
        id: 'smart-oven-001',
        name: 'Smart Convection Oven',
        type: 'oven',
        brand: 'ChefTech',
        model: 'CT-2000',
        capabilities: ['temperature_control', 'timer_control', 'preheating', 'remote_monitoring', 'recipe_sync'],
        status: 'connected',
        connectionType: 'wifi',
        lastSeen: new Date(),
        firmwareVersion: '2.1.4'
      },
      {
        id: 'smart-thermometer-001',
        name: 'Wireless Meat Thermometer',
        type: 'thermometer',
        brand: 'TempMaster',
        model: 'TM-Pro',
        capabilities: ['temperature_monitoring', 'notification_alerts', 'remote_monitoring'],
        status: 'connected',
        connectionType: 'bluetooth',
        lastSeen: new Date(),
        batteryLevel: 85
      },
      {
        id: 'smart-scale-001',
        name: 'Digital Kitchen Scale',
        type: 'scale',
        brand: 'PrecisionCook',
        model: 'PC-5000',
        capabilities: ['weight_measurement', 'recipe_sync', 'notification_alerts'],
        status: 'connected',
        connectionType: 'bluetooth',
        lastSeen: new Date(),
        batteryLevel: 92
      },
      {
        id: 'smart-cooker-001',
        name: 'Multi-Function Pressure Cooker',
        type: 'pressure_cooker',
        brand: 'InstantChef',
        model: 'IC-Duo',
        capabilities: ['temperature_control', 'pressure_control', 'timer_control', 'auto_shutoff', 'recipe_sync'],
        status: 'idle',
        connectionType: 'wifi',
        lastSeen: new Date(),
        firmwareVersion: '1.8.2'
      }
    ];

    demoDevices.forEach(device => {
      this.devices.set(device.id, device);
    });

    this.saveDevicesToStorage();
  }

  public async discoverDevices(): Promise<SmartDevice[]> {
    // Trigger manual device discovery
    this.startDeviceDiscovery();
    
    // Return current devices after a brief delay to simulate discovery
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(Array.from(this.devices.values()));
      }, 2000);
    });
  }

  public getDevices(): SmartDevice[] {
    return Array.from(this.devices.values());
  }

  public getDevice(deviceId: string): SmartDevice | undefined {
    return this.devices.get(deviceId);
  }

  public getDevicesByType(type: DeviceType): SmartDevice[] {
    return Array.from(this.devices.values()).filter(device => device.type === type);
  }

  public async connectDevice(deviceId: string): Promise<boolean> {
    const device = this.devices.get(deviceId);
    if (!device) return false;

    try {
      // Simulate device connection
      device.status = 'connected';
      device.lastSeen = new Date();
      
      this.devices.set(deviceId, device);
      await this.saveDevicesToStorage();
      
      this.notifyDeviceListeners(deviceId, 'connected');
      return true;
    } catch (error) {
      console.error(`Failed to connect to device ${deviceId}:`, error);
      return false;
    }
  }

  public async disconnectDevice(deviceId: string): Promise<boolean> {
    const device = this.devices.get(deviceId);
    if (!device) return false;

    try {
      device.status = 'disconnected';
      this.devices.set(deviceId, device);
      await this.saveDevicesToStorage();
      
      this.notifyDeviceListeners(deviceId, 'disconnected');
      return true;
    } catch (error) {
      console.error(`Failed to disconnect device ${deviceId}:`, error);
      return false;
    }
  }

  public async executeDeviceAction(deviceId: string, action: DeviceAction): Promise<boolean> {
    const device = this.devices.get(deviceId);
    if (!device || device.status !== 'connected') {
      throw new Error(`Device ${deviceId} is not available`);
    }

    try {
      console.log(`Executing action on ${device.name}:`, action);
      
      // Simulate device action execution
      switch (action.type) {
        case 'set_temperature':
          return this.setDeviceTemperature(deviceId, action.parameters.temperature);
        case 'start_timer':
          return this.startDeviceTimer(deviceId, action.parameters.duration);
        case 'preheat':
          return this.preheatDevice(deviceId, action.parameters.temperature);
        case 'start_cooking':
          return this.startCooking(deviceId, action.parameters);
        case 'stop_cooking':
          return this.stopCooking(deviceId);
        case 'measure_weight':
          return this.measureWeight(deviceId);
        case 'check_temperature':
          return this.checkTemperature(deviceId);
        default:
          throw new Error(`Unsupported action type: ${action.type}`);
      }
    } catch (error) {
      console.error(`Failed to execute action on device ${deviceId}:`, error);
      return false;
    }
  }

  private async setDeviceTemperature(deviceId: string, temperature: number): Promise<boolean> {
    const device = this.devices.get(deviceId);
    if (!device?.capabilities.includes('temperature_control')) {
      throw new Error('Device does not support temperature control');
    }

    // Simulate temperature setting
    console.log(`Setting ${device.name} temperature to ${temperature}°F`);
    
    // Update device status
    device.status = 'busy';
    this.devices.set(deviceId, device);
    
    // Simulate temperature change delay
    setTimeout(() => {
      device.status = 'idle';
      this.devices.set(deviceId, device);
      this.notifyDeviceListeners(deviceId, 'temperature_set', { temperature });
    }, 3000);

    return true;
  }

  private async startDeviceTimer(deviceId: string, duration: number): Promise<boolean> {
    const device = this.devices.get(deviceId);
    if (!device?.capabilities.includes('timer_control')) {
      throw new Error('Device does not support timer control');
    }

    console.log(`Starting ${device.name} timer for ${duration} minutes`);
    
    // Clear existing timer if any
    const existingTimer = this.activeTimers.get(deviceId);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    // Start new timer
    const timer = setTimeout(() => {
      this.notifyDeviceListeners(deviceId, 'timer_finished');
      this.activeTimers.delete(deviceId);
      
      // Send notification
      this.sendNotification(`Timer finished for ${device.name}`, {
        icon: '/icons/icon48.png',
        tag: `timer-${deviceId}`
      });
    }, duration * 60 * 1000);

    this.activeTimers.set(deviceId, timer);
    return true;
  }

  private async preheatDevice(deviceId: string, temperature: number): Promise<boolean> {
    const device = this.devices.get(deviceId);
    if (!device?.capabilities.includes('preheating')) {
      throw new Error('Device does not support preheating');
    }

    console.log(`Preheating ${device.name} to ${temperature}°F`);
    
    device.status = 'busy';
    this.devices.set(deviceId, device);

    // Simulate preheating time
    setTimeout(() => {
      device.status = 'idle';
      this.devices.set(deviceId, device);
      this.notifyDeviceListeners(deviceId, 'preheating_complete', { temperature });
      
      this.sendNotification(`${device.name} preheating complete`, {
        body: `Ready at ${temperature}°F`,
        icon: '/icons/icon48.png'
      });
    }, 10000); // 10 seconds for demo

    return true;
  }

  private async startCooking(deviceId: string, parameters: any): Promise<boolean> {
    const device = this.devices.get(deviceId);
    if (!device) return false;

    console.log(`Starting cooking on ${device.name}`, parameters);
    
    device.status = 'busy';
    this.devices.set(deviceId, device);
    
    this.notifyDeviceListeners(deviceId, 'cooking_started', parameters);
    return true;
  }

  private async stopCooking(deviceId: string): Promise<boolean> {
    const device = this.devices.get(deviceId);
    if (!device) return false;

    console.log(`Stopping cooking on ${device.name}`);
    
    device.status = 'idle';
    this.devices.set(deviceId, device);
    
    // Clear any active timers
    const timer = this.activeTimers.get(deviceId);
    if (timer) {
      clearTimeout(timer);
      this.activeTimers.delete(deviceId);
    }
    
    this.notifyDeviceListeners(deviceId, 'cooking_stopped');
    return true;
  }

  private async measureWeight(deviceId: string): Promise<boolean> {
    const device = this.devices.get(deviceId);
    if (!device?.capabilities.includes('weight_measurement')) {
      throw new Error('Device does not support weight measurement');
    }

    // Simulate weight measurement
    const weight = Math.round((Math.random() * 1000 + 100) * 100) / 100; // Random weight between 100-1100g
    
    console.log(`${device.name} measured weight: ${weight}g`);
    this.notifyDeviceListeners(deviceId, 'weight_measured', { weight });
    
    return true;
  }

  private async checkTemperature(deviceId: string): Promise<boolean> {
    const device = this.devices.get(deviceId);
    if (!device?.capabilities.includes('temperature_monitoring')) {
      throw new Error('Device does not support temperature monitoring');
    }

    // Simulate temperature reading
    const temperature = Math.round((Math.random() * 200 + 70) * 10) / 10; // Random temp between 70-270°F
    
    console.log(`${device.name} temperature reading: ${temperature}°F`);
    this.notifyDeviceListeners(deviceId, 'temperature_reading', { temperature });
    
    return true;
  }

  private async sendNotification(title: string, options: NotificationOptions = {}): Promise<void> {
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(title, {
        icon: '/icons/icon48.png',
        badge: '/icons/icon16.png',
        ...options
      });
    } else if (chrome.notifications) {
      chrome.notifications.create({
        type: 'basic',
        iconUrl: '/icons/icon48.png',
        title: title,
        message: options.body || ''
      });
    }
  }

  public addDeviceListener(deviceId: string, callback: Function): void {
    if (!this.deviceListeners.has(deviceId)) {
      this.deviceListeners.set(deviceId, []);
    }
    this.deviceListeners.get(deviceId)!.push(callback);
  }

  public removeDeviceListener(deviceId: string, callback: Function): void {
    const listeners = this.deviceListeners.get(deviceId);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private notifyDeviceListeners(deviceId: string, event: string, data?: any): void {
    const listeners = this.deviceListeners.get(deviceId);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(event, data);
        } catch (error) {
          console.error('Error in device listener:', error);
        }
      });
    }
  }

  // Automation Management
  public async createAutomation(automation: Omit<CookingAutomation, 'id' | 'createdAt'>): Promise<string> {
    const id = `automation-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const newAutomation: CookingAutomation = {
      ...automation,
      id,
      createdAt: new Date()
    };

    this.automations.set(id, newAutomation);
    await this.saveAutomationsToStorage();

    if (newAutomation.isActive) {
      this.activateAutomation(id);
    }

    return id;
  }

  public getAutomations(): CookingAutomation[] {
    return Array.from(this.automations.values());
  }

  public getAutomation(automationId: string): CookingAutomation | undefined {
    return this.automations.get(automationId);
  }

  public async updateAutomation(automationId: string, updates: Partial<CookingAutomation>): Promise<boolean> {
    const automation = this.automations.get(automationId);
    if (!automation) return false;

    const updatedAutomation = { ...automation, ...updates };
    this.automations.set(automationId, updatedAutomation);
    await this.saveAutomationsToStorage();

    if (updatedAutomation.isActive) {
      this.activateAutomation(automationId);
    } else {
      this.deactivateAutomation(automationId);
    }

    return true;
  }

  public async deleteAutomation(automationId: string): Promise<boolean> {
    const automation = this.automations.get(automationId);
    if (!automation) return false;

    this.deactivateAutomation(automationId);
    this.automations.delete(automationId);
    await this.saveAutomationsToStorage();

    return true;
  }

  private activateAutomation(automationId: string): void {
    const automation = this.automations.get(automationId);
    if (!automation) return;

    // Set up triggers for the automation
    automation.triggers.forEach(trigger => {
      this.setupAutomationTrigger(automationId, trigger);
    });
  }

  private deactivateAutomation(automationId: string): void {
    // Remove any active triggers or timers for this automation
    // Implementation would depend on the specific trigger types
  }

  private setupAutomationTrigger(automationId: string, trigger: AutomationTrigger): void {
    switch (trigger.type) {
      case 'time':
        this.setupTimeTrigger(automationId, trigger);
        break;
      case 'temperature':
        this.setupTemperatureTrigger(automationId, trigger);
        break;
      case 'timer':
        this.setupTimerTrigger(automationId, trigger);
        break;
      case 'recipe_start':
        this.setupRecipeStartTrigger(automationId, trigger);
        break;
    }
  }

  private setupTimeTrigger(automationId: string, trigger: AutomationTrigger): void {
    // Set up time-based trigger (e.g., specific time of day)
    const targetTime = new Date(trigger.value);
    const now = new Date();
    const delay = targetTime.getTime() - now.getTime();

    if (delay > 0) {
      setTimeout(() => {
        this.executeAutomation(automationId);
      }, delay);
    }
  }

  private setupTemperatureTrigger(automationId: string, trigger: AutomationTrigger): void {
    // Set up temperature-based trigger
    if (trigger.deviceId) {
      this.addDeviceListener(trigger.deviceId, (event: string, data: any) => {
        if (event === 'temperature_reading' && data.temperature >= trigger.value) {
          this.executeAutomation(automationId);
        }
      });
    }
  }

  private setupTimerTrigger(automationId: string, trigger: AutomationTrigger): void {
    // Set up timer-based trigger
    if (trigger.deviceId) {
      this.addDeviceListener(trigger.deviceId, (event: string) => {
        if (event === 'timer_finished') {
          this.executeAutomation(automationId);
        }
      });
    }
  }

  private setupRecipeStartTrigger(automationId: string, trigger: AutomationTrigger): void {
    // Set up recipe start trigger - would integrate with recipe service
    // This would listen for recipe start events
  }

  public async executeAutomation(automationId: string): Promise<boolean> {
    const automation = this.automations.get(automationId);
    if (!automation || !automation.isActive) return false;

    try {
      console.log(`Executing automation: ${automation.name}`);

      // Check conditions
      const conditionsMet = await this.checkAutomationConditions(automation.conditions);
      if (!conditionsMet) {
        console.log(`Automation ${automation.name} conditions not met`);
        return false;
      }

      // Execute steps in sequence
      for (const step of automation.steps.sort((a, b) => a.stepNumber - b.stepNumber)) {
        await this.executeAutomationStep(step);

        // Apply delay if specified
        if (step.delay) {
          await new Promise(resolve => setTimeout(resolve, step.delay! * 1000));
        }
      }

      // Update last executed time
      automation.lastExecuted = new Date();
      this.automations.set(automationId, automation);
      await this.saveAutomationsToStorage();

      return true;
    } catch (error) {
      console.error(`Failed to execute automation ${automation.name}:`, error);
      return false;
    }
  }

  private async checkAutomationConditions(conditions: AutomationCondition[]): Promise<boolean> {
    for (const condition of conditions) {
      const device = this.devices.get(condition.deviceId);
      if (!device) return false;

      // Check device-specific conditions
      // This would need to be expanded based on actual device properties
      const deviceProperty = this.getDeviceProperty(device, condition.property);
      if (!this.evaluateCondition(deviceProperty, condition.operator, condition.value)) {
        return false;
      }
    }
    return true;
  }

  private getDeviceProperty(device: SmartDevice, property: string): any {
    // Get device property value
    switch (property) {
      case 'status':
        return device.status;
      case 'batteryLevel':
        return device.batteryLevel;
      case 'connectionType':
        return device.connectionType;
      default:
        return null;
    }
  }

  private evaluateCondition(value: any, operator: string, targetValue: any): boolean {
    switch (operator) {
      case '=':
        return value === targetValue;
      case '>':
        return value > targetValue;
      case '<':
        return value < targetValue;
      case '>=':
        return value >= targetValue;
      case '<=':
        return value <= targetValue;
      case '!=':
        return value !== targetValue;
      default:
        return false;
    }
  }

  private async executeAutomationStep(step: AutomationStep): Promise<void> {
    const device = this.devices.get(step.deviceId);
    if (!device) {
      throw new Error(`Device ${step.deviceId} not found`);
    }

    console.log(`Executing automation step ${step.stepNumber} on ${device.name}`);

    await this.executeDeviceAction(step.deviceId, step.action);
  }

  // Recipe Integration
  public async createRecipeAutomation(recipe: Recipe): Promise<string> {
    const automationSteps: AutomationStep[] = [];
    let stepNumber = 1;

    // Analyze recipe steps and create automation
    recipe.instructions.forEach((instruction, index) => {
      const deviceActions = this.analyzeInstructionForDeviceActions(instruction);

      deviceActions.forEach(action => {
        automationSteps.push({
          stepNumber: stepNumber++,
          deviceId: action.deviceId,
          action: action.action,
          parameters: action.parameters,
          delay: action.delay
        });
      });
    });

    const automation: Omit<CookingAutomation, 'id' | 'createdAt'> = {
      name: `${recipe.title} Automation`,
      recipeId: recipe.id,
      devices: [...new Set(automationSteps.map(step => step.deviceId))],
      steps: automationSteps,
      triggers: [{
        type: 'manual',
        value: true
      }],
      conditions: [],
      isActive: false
    };

    return await this.createAutomation(automation);
  }

  private analyzeInstructionForDeviceActions(instruction: CookingStep): Array<{
    deviceId: string;
    action: DeviceAction;
    parameters: any;
    delay?: number;
  }> {
    const actions: Array<{
      deviceId: string;
      action: DeviceAction;
      parameters: any;
      delay?: number;
    }> = [];

    const description = instruction.description.toLowerCase();

    // Analyze instruction text for device actions
    if (description.includes('preheat') && description.includes('oven')) {
      const tempMatch = description.match(/(\d+)°?[f]?/);
      const temperature = tempMatch ? parseInt(tempMatch[1]) : 350;

      const ovenDevices = this.getDevicesByType('oven');
      if (ovenDevices.length > 0) {
        actions.push({
          deviceId: ovenDevices[0].id,
          action: {
            type: 'preheat',
            parameters: { temperature }
          },
          parameters: { temperature }
        });
      }
    }

    if (description.includes('timer') || description.includes('cook for')) {
      const timeMatch = description.match(/(\d+)\s*(minute|min|hour|hr)/);
      if (timeMatch) {
        const duration = parseInt(timeMatch[1]);
        const unit = timeMatch[2];
        const minutes = unit.includes('hour') || unit.includes('hr') ? duration * 60 : duration;

        const timerDevices = this.getDevicesByType('timer');
        if (timerDevices.length > 0) {
          actions.push({
            deviceId: timerDevices[0].id,
            action: {
              type: 'start_timer',
              parameters: { duration: minutes }
            },
            parameters: { duration: minutes }
          });
        }
      }
    }

    if (description.includes('weigh') || description.includes('measure')) {
      const scaleDevices = this.getDevicesByType('scale');
      if (scaleDevices.length > 0) {
        actions.push({
          deviceId: scaleDevices[0].id,
          action: {
            type: 'measure_weight',
            parameters: {}
          },
          parameters: {}
        });
      }
    }

    if (description.includes('temperature') && description.includes('check')) {
      const thermometerDevices = this.getDevicesByType('thermometer');
      if (thermometerDevices.length > 0) {
        actions.push({
          deviceId: thermometerDevices[0].id,
          action: {
            type: 'check_temperature',
            parameters: {}
          },
          parameters: {}
        });
      }
    }

    return actions;
  }
}
