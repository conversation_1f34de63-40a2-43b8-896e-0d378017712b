import React, { useState, useCallback } from 'react';
import { AIRecipeRequest, DietaryRestriction, MealType } from '../types';
import { useI18n } from '../hooks/useI18n';

interface RecipeGeneratorProps {
  onGenerate: (request: AIRecipeRequest) => Promise<void>;
  isGenerating: boolean;
  compact?: boolean;
}

export const RecipeGenerator: React.FC<RecipeGeneratorProps> = ({
  onGenerate,
  isGenerating,
  compact = false
}) => {
  const { t } = useI18n();
  const [ingredients, setIngredients] = useState<string[]>([]);
  const [currentIngredient, setCurrentIngredient] = useState('');
  const [title, setTitle] = useState('');
  const [cuisine, setCuisine] = useState('');
  const [dietaryRestrictions, setDietaryRestrictions] = useState<DietaryRestriction[]>([]);
  const [cookingTime, setCookingTime] = useState<number>(30);
  const [servings, setServings] = useState<number>(4);
  const [mealType, setMealType] = useState<MealType>('Dinner');
  const [difficulty, setDifficulty] = useState<'Easy' | 'Medium' | 'Hard'>('Medium');

  const handleAddIngredient = useCallback(() => {
    if (currentIngredient.trim() && !ingredients.includes(currentIngredient.trim())) {
      setIngredients(prev => [...prev, currentIngredient.trim()]);
      setCurrentIngredient('');
    }
  }, [currentIngredient, ingredients]);

  const handleRemoveIngredient = useCallback((ingredient: string) => {
    setIngredients(prev => prev.filter(item => item !== ingredient));
  }, []);

  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddIngredient();
    }
  }, [handleAddIngredient]);

  const handleDietaryRestrictionToggle = useCallback((restriction: DietaryRestriction) => {
    setDietaryRestrictions(prev => 
      prev.includes(restriction)
        ? prev.filter(item => item !== restriction)
        : [...prev, restriction]
    );
  }, []);

  const handleGenerate = useCallback(async () => {
    const request: AIRecipeRequest = {
      ingredients: ingredients.length > 0 ? ingredients : undefined,
      title: title.trim() || undefined,
      cuisine: cuisine.trim() || undefined,
      dietaryRestrictions: dietaryRestrictions.length > 0 ? dietaryRestrictions : undefined,
      cookingTime,
      servings,
      mealType,
      difficulty
    };

    await onGenerate(request);
  }, [ingredients, title, cuisine, dietaryRestrictions, cookingTime, servings, mealType, difficulty, onGenerate]);

  const isFormValid = ingredients.length > 0 || title.trim().length > 0;

  return (
    <div className="recipe-generator">
      {/* Ingredients Input */}
      <div className="input-group">
        <label className="input-label">
          {t('generator.ingredients')} {!compact && `(${ingredients.length})`}
        </label>
        <div className="ingredient-input-container">
          <input
            type="text"
            className="glass-input"
            placeholder={t('generator.ingredients_placeholder')}
            value={currentIngredient}
            onChange={(e) => setCurrentIngredient(e.target.value)}
            onKeyPress={handleKeyPress}
            disabled={isGenerating}
          />
          {currentIngredient.trim() && (
            <button
              type="button"
              className="glass-button"
              onClick={handleAddIngredient}
              style={{ 
                position: 'absolute', 
                right: '8px', 
                top: '50%', 
                transform: 'translateY(-50%)',
                padding: '4px 8px',
                fontSize: '12px'
              }}
            >
              +
            </button>
          )}
        </div>
        
        {ingredients.length > 0 && (
          <div className="ingredient-tags">
            {ingredients.map((ingredient, index) => (
              <span key={index} className="ingredient-tag">
                {ingredient}
                <button
                  type="button"
                  className="ingredient-tag-remove"
                  onClick={() => handleRemoveIngredient(ingredient)}
                  disabled={isGenerating}
                >
                  ×
                </button>
              </span>
            ))}
          </div>
        )}
      </div>

      {/* Recipe Title Input */}
      <div className="input-group">
        <label className="input-label">{t('generator.recipe_title')}</label>
        <input
          type="text"
          className="glass-input"
          placeholder={t('generator.recipe_title_placeholder')}
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          disabled={isGenerating}
        />
      </div>

      {!compact && (
        <>
          {/* Cuisine Type */}
          <div className="input-group">
            <label className="input-label">{t('generator.cuisine')}</label>
            <select
              className="glass-input"
              value={cuisine}
              onChange={(e) => setCuisine(e.target.value)}
              disabled={isGenerating}
            >
              <option value="">{t('generator.any_cuisine')}</option>
              <option value="Italian">Italian</option>
              <option value="Chinese">Chinese</option>
              <option value="Mexican">Mexican</option>
              <option value="Indian">Indian</option>
              <option value="French">French</option>
              <option value="Japanese">Japanese</option>
              <option value="Mediterranean">Mediterranean</option>
              <option value="American">American</option>
              <option value="Thai">Thai</option>
              <option value="Middle Eastern">Middle Eastern</option>
            </select>
          </div>

          {/* Meal Type */}
          <div className="input-group">
            <label className="input-label">{t('generator.meal_type')}</label>
            <select
              className="glass-input"
              value={mealType}
              onChange={(e) => setMealType(e.target.value as MealType)}
              disabled={isGenerating}
            >
              <option value="Breakfast">Breakfast</option>
              <option value="Lunch">Lunch</option>
              <option value="Dinner">Dinner</option>
              <option value="Snack">Snack</option>
              <option value="Dessert">Dessert</option>
              <option value="Appetizer">Appetizer</option>
              <option value="Beverage">Beverage</option>
            </select>
          </div>

          {/* Cooking Time and Servings */}
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 'var(--spacing-md)' }}>
            <div className="input-group">
              <label className="input-label">{t('generator.cooking_time')} ({cookingTime} min)</label>
              <input
                type="range"
                min="10"
                max="180"
                step="10"
                value={cookingTime}
                onChange={(e) => setCookingTime(Number(e.target.value))}
                disabled={isGenerating}
                style={{ width: '100%' }}
              />
            </div>
            
            <div className="input-group">
              <label className="input-label">{t('generator.servings')} ({servings})</label>
              <input
                type="range"
                min="1"
                max="12"
                step="1"
                value={servings}
                onChange={(e) => setServings(Number(e.target.value))}
                disabled={isGenerating}
                style={{ width: '100%' }}
              />
            </div>
          </div>

          {/* Difficulty */}
          <div className="input-group">
            <label className="input-label">{t('generator.difficulty')}</label>
            <div style={{ display: 'flex', gap: 'var(--spacing-sm)' }}>
              {(['Easy', 'Medium', 'Hard'] as const).map((level) => (
                <button
                  key={level}
                  type="button"
                  className={`glass-button ${difficulty === level ? 'glass-button-primary' : ''}`}
                  onClick={() => setDifficulty(level)}
                  disabled={isGenerating}
                  style={{ flex: 1, padding: 'var(--spacing-xs)' }}
                >
                  {t(`generator.difficulty_${level.toLowerCase()}`)}
                </button>
              ))}
            </div>
          </div>

          {/* Dietary Restrictions */}
          <div className="input-group">
            <label className="input-label">{t('generator.dietary_restrictions')}</label>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: 'var(--spacing-xs)' }}>
              {(['Vegetarian', 'Vegan', 'Gluten-Free', 'Dairy-Free', 'Nut-Free', 'Low-Carb'] as DietaryRestriction[]).map((restriction) => (
                <button
                  key={restriction}
                  type="button"
                  className={`glass-button ${dietaryRestrictions.includes(restriction) ? 'glass-button-accent' : ''}`}
                  onClick={() => handleDietaryRestrictionToggle(restriction)}
                  disabled={isGenerating}
                  style={{ 
                    padding: 'var(--spacing-xs)', 
                    fontSize: 'var(--font-size-xs)',
                    textAlign: 'center'
                  }}
                >
                  {t(`dietary.${restriction.toLowerCase().replace('-', '_')}`)}
                </button>
              ))}
            </div>
          </div>
        </>
      )}

      {/* Generate Button */}
      <button
        type="button"
        className={`generate-button ${isGenerating ? 'loading' : ''}`}
        onClick={handleGenerate}
        disabled={!isFormValid || isGenerating}
      >
        {isGenerating ? '' : t('actions.generate_recipe')}
      </button>
    </div>
  );
};
