import { Recipe, Ingredient, CookingStep } from '../types';

export interface LLMProvider {
  id: string;
  name: string;
  description: string;
  apiEndpoint: string;
  models: LLMModel[];
  requiresApiKey: boolean;
  maxTokens: number;
  supportedFeatures: LLMFeature[];
}

export interface LLMModel {
  id: string;
  name: string;
  description: string;
  contextLength: number;
  costPer1kTokens: number;
  capabilities: string[];
}

export type LLMFeature = 'recipe_generation' | 'ingredient_substitution' | 'cooking_tips' | 'meal_planning' | 'dietary_analysis';

export interface LLMConfig {
  provider: string;
  model: string;
  apiKey: string;
  temperature: number;
  maxTokens: number;
  systemPrompt: string;
}

export interface RecipeGenerationRequest {
  ingredients?: string[];
  cuisineType?: string;
  dietaryRestrictions?: string[];
  servings?: number;
  cookingTime?: number;
  difficulty?: string;
  mealType?: string;
  customPrompt?: string;
}

export interface LLMResponse {
  success: boolean;
  data?: any;
  error?: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
    cost?: number;
  };
}

export class LLMService {
  private static instance: LLMService;
  private config: LLMConfig | null = null;
  private providers: Map<string, LLMProvider> = new Map();

  private constructor() {
    this.initializeProviders();
    this.loadConfiguration();
  }

  public static getInstance(): LLMService {
    if (!LLMService.instance) {
      LLMService.instance = new LLMService();
    }
    return LLMService.instance;
  }

  private initializeProviders(): void {
    const providers: LLMProvider[] = [
      {
        id: 'openrouter',
        name: 'OpenRouter',
        description: 'Access to multiple AI models through OpenRouter API',
        apiEndpoint: 'https://openrouter.ai/api/v1/chat/completions',
        requiresApiKey: true,
        maxTokens: 4096,
        supportedFeatures: ['recipe_generation', 'ingredient_substitution', 'cooking_tips', 'meal_planning', 'dietary_analysis'],
        models: [
          {
            id: 'anthropic/claude-3.5-sonnet',
            name: 'Claude 3.5 Sonnet',
            description: 'Anthropic\'s most capable model for complex reasoning',
            contextLength: 200000,
            costPer1kTokens: 0.003,
            capabilities: ['recipe_generation', 'cooking_analysis', 'meal_planning']
          },
          {
            id: 'openai/gpt-4-turbo',
            name: 'GPT-4 Turbo',
            description: 'OpenAI\'s most capable model with large context',
            contextLength: 128000,
            costPer1kTokens: 0.01,
            capabilities: ['recipe_generation', 'cooking_tips', 'dietary_analysis']
          },
          {
            id: 'google/gemini-pro-1.5',
            name: 'Gemini Pro 1.5',
            description: 'Google\'s advanced multimodal model',
            contextLength: 1000000,
            costPer1kTokens: 0.0025,
            capabilities: ['recipe_generation', 'ingredient_analysis', 'cooking_guidance']
          },
          {
            id: 'meta-llama/llama-3.1-70b-instruct',
            name: 'Llama 3.1 70B',
            description: 'Meta\'s open-source large language model',
            contextLength: 131072,
            costPer1kTokens: 0.0009,
            capabilities: ['recipe_generation', 'cooking_tips']
          }
        ]
      },
      {
        id: 'gemini',
        name: 'Google Gemini',
        description: 'Google\'s native Gemini API for advanced AI capabilities',
        apiEndpoint: 'https://generativelanguage.googleapis.com/v1beta/models',
        requiresApiKey: true,
        maxTokens: 8192,
        supportedFeatures: ['recipe_generation', 'ingredient_substitution', 'cooking_tips', 'dietary_analysis'],
        models: [
          {
            id: 'gemini-1.5-pro',
            name: 'Gemini 1.5 Pro',
            description: 'Google\'s most capable model with multimodal understanding',
            contextLength: 2000000,
            costPer1kTokens: 0.0035,
            capabilities: ['recipe_generation', 'image_analysis', 'cooking_guidance']
          },
          {
            id: 'gemini-1.5-flash',
            name: 'Gemini 1.5 Flash',
            description: 'Fast and efficient model for quick responses',
            contextLength: 1000000,
            costPer1kTokens: 0.00015,
            capabilities: ['recipe_generation', 'quick_tips']
          }
        ]
      },
      {
        id: 'openai',
        name: 'OpenAI',
        description: 'Direct access to OpenAI\'s GPT models',
        apiEndpoint: 'https://api.openai.com/v1/chat/completions',
        requiresApiKey: true,
        maxTokens: 4096,
        supportedFeatures: ['recipe_generation', 'ingredient_substitution', 'cooking_tips', 'meal_planning'],
        models: [
          {
            id: 'gpt-4o',
            name: 'GPT-4o',
            description: 'OpenAI\'s flagship multimodal model',
            contextLength: 128000,
            costPer1kTokens: 0.005,
            capabilities: ['recipe_generation', 'image_analysis', 'cooking_guidance']
          },
          {
            id: 'gpt-4o-mini',
            name: 'GPT-4o Mini',
            description: 'Smaller, faster version of GPT-4o',
            contextLength: 128000,
            costPer1kTokens: 0.00015,
            capabilities: ['recipe_generation', 'quick_responses']
          }
        ]
      },
      {
        id: 'anthropic',
        name: 'Anthropic',
        description: 'Direct access to Anthropic\'s Claude models',
        apiEndpoint: 'https://api.anthropic.com/v1/messages',
        requiresApiKey: true,
        maxTokens: 4096,
        supportedFeatures: ['recipe_generation', 'ingredient_substitution', 'cooking_tips', 'dietary_analysis'],
        models: [
          {
            id: 'claude-3-5-sonnet-20241022',
            name: 'Claude 3.5 Sonnet',
            description: 'Anthropic\'s most capable model',
            contextLength: 200000,
            costPer1kTokens: 0.003,
            capabilities: ['recipe_generation', 'detailed_analysis', 'cooking_guidance']
          },
          {
            id: 'claude-3-haiku-20240307',
            name: 'Claude 3 Haiku',
            description: 'Fast and efficient model for quick tasks',
            contextLength: 200000,
            costPer1kTokens: 0.00025,
            capabilities: ['recipe_generation', 'quick_tips']
          }
        ]
      }
    ];

    providers.forEach(provider => {
      this.providers.set(provider.id, provider);
    });
  }

  private async loadConfiguration(): Promise<void> {
    try {
      const result = await chrome.storage.local.get(['llmConfig']);
      if (result.llmConfig) {
        this.config = JSON.parse(result.llmConfig);
      } else {
        // Set default configuration
        this.config = {
          provider: 'openrouter',
          model: 'anthropic/claude-3.5-sonnet',
          apiKey: '',
          temperature: 0.7,
          maxTokens: 2048,
          systemPrompt: 'You are ChefAI, an expert culinary assistant. Generate detailed, practical recipes with clear instructions, ingredient lists, and cooking tips. Focus on accuracy, safety, and delicious results.'
        };
        await this.saveConfiguration();
      }
    } catch (error) {
      console.error('Failed to load LLM configuration:', error);
    }
  }

  public async saveConfiguration(config?: LLMConfig): Promise<void> {
    if (config) {
      this.config = config;
    }
    
    try {
      await chrome.storage.local.set({
        llmConfig: JSON.stringify(this.config)
      });
    } catch (error) {
      console.error('Failed to save LLM configuration:', error);
    }
  }

  public getConfiguration(): LLMConfig | null {
    return this.config;
  }

  public getProviders(): LLMProvider[] {
    return Array.from(this.providers.values());
  }

  public getProvider(providerId: string): LLMProvider | undefined {
    return this.providers.get(providerId);
  }

  public async generateRecipe(request: RecipeGenerationRequest): Promise<LLMResponse> {
    if (!this.config || !this.config.apiKey) {
      return {
        success: false,
        error: 'LLM configuration not set. Please configure your API key in settings.'
      };
    }

    const provider = this.providers.get(this.config.provider);
    if (!provider) {
      return {
        success: false,
        error: 'Invalid LLM provider configured.'
      };
    }

    try {
      const prompt = this.buildRecipePrompt(request);
      const response = await this.callLLMAPI(prompt);
      
      if (response.success && response.data) {
        const recipe = this.parseRecipeResponse(response.data);
        return {
          success: true,
          data: recipe,
          usage: response.usage
        };
      }
      
      return response;
    } catch (error) {
      console.error('Recipe generation failed:', error);
      return {
        success: false,
        error: `Failed to generate recipe: ${error.message}`
      };
    }
  }

  private buildRecipePrompt(request: RecipeGenerationRequest): string {
    let prompt = 'Generate a detailed recipe with the following requirements:\n\n';

    if (request.ingredients && request.ingredients.length > 0) {
      prompt += `Available ingredients: ${request.ingredients.join(', ')}\n`;
    }

    if (request.cuisineType) {
      prompt += `Cuisine type: ${request.cuisineType}\n`;
    }

    if (request.dietaryRestrictions && request.dietaryRestrictions.length > 0) {
      prompt += `Dietary restrictions: ${request.dietaryRestrictions.join(', ')}\n`;
    }

    if (request.servings) {
      prompt += `Servings: ${request.servings}\n`;
    }

    if (request.cookingTime) {
      prompt += `Maximum cooking time: ${request.cookingTime} minutes\n`;
    }

    if (request.difficulty) {
      prompt += `Difficulty level: ${request.difficulty}\n`;
    }

    if (request.mealType) {
      prompt += `Meal type: ${request.mealType}\n`;
    }

    if (request.customPrompt) {
      prompt += `Additional requirements: ${request.customPrompt}\n`;
    }

    prompt += `\nPlease provide the recipe in the following JSON format:
{
  "title": "Recipe Name",
  "description": "Brief description of the dish",
  "servings": number,
  "prepTime": number (in minutes),
  "cookTime": number (in minutes),
  "difficulty": "Easy|Medium|Hard",
  "cuisine": "cuisine type",
  "ingredients": [
    {
      "name": "ingredient name",
      "amount": number,
      "unit": "unit of measurement",
      "category": "category"
    }
  ],
  "instructions": [
    {
      "step": number,
      "description": "detailed instruction",
      "duration": number (in minutes, if applicable)
    }
  ],
  "tips": ["cooking tip 1", "cooking tip 2"],
  "nutrition": {
    "calories": number,
    "protein": number,
    "carbs": number,
    "fat": number
  }
}

Ensure all measurements are precise, instructions are clear and detailed, and the recipe is practical and delicious.`;

    return prompt;
  }

  private async callLLMAPI(prompt: string): Promise<LLMResponse> {
    if (!this.config) {
      return { success: false, error: 'No configuration available' };
    }

    const provider = this.providers.get(this.config.provider);
    if (!provider) {
      return { success: false, error: 'Provider not found' };
    }

    try {
      let response: Response;
      let requestBody: any;

      switch (this.config.provider) {
        case 'openrouter':
          requestBody = {
            model: this.config.model,
            messages: [
              { role: 'system', content: this.config.systemPrompt },
              { role: 'user', content: prompt }
            ],
            temperature: this.config.temperature,
            max_tokens: this.config.maxTokens
          };

          response = await fetch(provider.apiEndpoint, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${this.config.apiKey}`,
              'HTTP-Referer': chrome.runtime.getURL(''),
              'X-Title': 'ChefAI Extension'
            },
            body: JSON.stringify(requestBody)
          });
          break;

        case 'gemini':
          const geminiUrl = `${provider.apiEndpoint}/${this.config.model}:generateContent?key=${this.config.apiKey}`;
          requestBody = {
            contents: [{
              parts: [{
                text: `${this.config.systemPrompt}\n\n${prompt}`
              }]
            }],
            generationConfig: {
              temperature: this.config.temperature,
              maxOutputTokens: this.config.maxTokens
            }
          };

          response = await fetch(geminiUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
          });
          break;

        case 'openai':
          requestBody = {
            model: this.config.model,
            messages: [
              { role: 'system', content: this.config.systemPrompt },
              { role: 'user', content: prompt }
            ],
            temperature: this.config.temperature,
            max_tokens: this.config.maxTokens
          };

          response = await fetch(provider.apiEndpoint, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${this.config.apiKey}`
            },
            body: JSON.stringify(requestBody)
          });
          break;

        case 'anthropic':
          requestBody = {
            model: this.config.model,
            max_tokens: this.config.maxTokens,
            temperature: this.config.temperature,
            system: this.config.systemPrompt,
            messages: [
              { role: 'user', content: prompt }
            ]
          };

          response = await fetch(provider.apiEndpoint, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'x-api-key': this.config.apiKey,
              'anthropic-version': '2023-06-01'
            },
            body: JSON.stringify(requestBody)
          });
          break;

        default:
          return { success: false, error: 'Unsupported provider' };
      }

      if (!response.ok) {
        const errorText = await response.text();
        return {
          success: false,
          error: `API request failed: ${response.status} ${response.statusText} - ${errorText}`
        };
      }

      const data = await response.json();
      return this.parseAPIResponse(data, this.config.provider);

    } catch (error) {
      console.error('LLM API call failed:', error);
      return {
        success: false,
        error: `Network error: ${error.message}`
      };
    }
  }

  private parseAPIResponse(data: any, provider: string): LLMResponse {
    try {
      let content: string;
      let usage: any = {};

      switch (provider) {
        case 'openrouter':
        case 'openai':
          content = data.choices[0].message.content;
          usage = {
            promptTokens: data.usage?.prompt_tokens || 0,
            completionTokens: data.usage?.completion_tokens || 0,
            totalTokens: data.usage?.total_tokens || 0
          };
          break;

        case 'gemini':
          content = data.candidates[0].content.parts[0].text;
          usage = {
            promptTokens: data.usageMetadata?.promptTokenCount || 0,
            completionTokens: data.usageMetadata?.candidatesTokenCount || 0,
            totalTokens: data.usageMetadata?.totalTokenCount || 0
          };
          break;

        case 'anthropic':
          content = data.content[0].text;
          usage = {
            promptTokens: data.usage?.input_tokens || 0,
            completionTokens: data.usage?.output_tokens || 0,
            totalTokens: (data.usage?.input_tokens || 0) + (data.usage?.output_tokens || 0)
          };
          break;

        default:
          return { success: false, error: 'Unknown provider response format' };
      }

      return {
        success: true,
        data: content,
        usage
      };

    } catch (error) {
      return {
        success: false,
        error: `Failed to parse API response: ${error.message}`
      };
    }
  }

  private parseRecipeResponse(content: string): Recipe | null {
    try {
      // Extract JSON from the response
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }

      const recipeData = JSON.parse(jsonMatch[0]);
      
      // Convert to our Recipe format
      const recipe: Recipe = {
        id: `llm-recipe-${Date.now()}`,
        title: recipeData.title || 'Generated Recipe',
        description: recipeData.description || '',
        servings: recipeData.servings || 4,
        prepTime: recipeData.prepTime || 15,
        cookTime: recipeData.cookTime || 30,
        difficulty: recipeData.difficulty || 'Medium',
        cuisine: recipeData.cuisine || 'International',
        ingredients: recipeData.ingredients?.map((ing: any, index: number) => ({
          id: `ing-${index}`,
          name: ing.name,
          amount: ing.amount,
          unit: ing.unit,
          category: ing.category || 'Other'
        })) || [],
        instructions: recipeData.instructions?.map((inst: any) => ({
          step: inst.step,
          description: inst.description,
          duration: inst.duration || 0
        })) || [],
        tags: recipeData.tags || [],
        nutrition: recipeData.nutrition || {
          calories: 0,
          protein: 0,
          carbs: 0,
          fat: 0,
          fiber: 0,
          sugar: 0,
          sodium: 0
        },
        tips: recipeData.tips || [],
        createdAt: new Date(),
        source: 'AI Generated'
      };

      return recipe;

    } catch (error) {
      console.error('Failed to parse recipe response:', error);
      return null;
    }
  }

  public async testConnection(): Promise<LLMResponse> {
    if (!this.config || !this.config.apiKey) {
      return {
        success: false,
        error: 'No API key configured'
      };
    }

    try {
      const testPrompt = 'Respond with "Connection successful" if you can read this message.';
      const response = await this.callLLMAPI(testPrompt);
      
      if (response.success) {
        return {
          success: true,
          data: 'Connection test successful',
          usage: response.usage
        };
      }
      
      return response;
    } catch (error) {
      return {
        success: false,
        error: `Connection test failed: ${error.message}`
      };
    }
  }

  public async getIngredientSubstitutions(ingredient: string, dietary_restrictions?: string[]): Promise<LLMResponse> {
    const prompt = `Suggest 3-5 substitutions for "${ingredient}" in cooking${dietary_restrictions ? ` considering these dietary restrictions: ${dietary_restrictions.join(', ')}` : ''}. 
    
    Provide the response in JSON format:
    {
      "ingredient": "${ingredient}",
      "substitutions": [
        {
          "name": "substitute name",
          "ratio": "1:1 or specific ratio",
          "notes": "any important notes about taste/texture changes"
        }
      ]
    }`;

    return await this.callLLMAPI(prompt);
  }

  public async getCookingTips(recipe_title: string, difficulty?: string): Promise<LLMResponse> {
    const prompt = `Provide 3-5 professional cooking tips for making "${recipe_title}"${difficulty ? ` (${difficulty} level)` : ''}. Focus on technique, timing, and common mistakes to avoid.
    
    Provide the response in JSON format:
    {
      "recipe": "${recipe_title}",
      "tips": [
        {
          "category": "technique|timing|ingredient|safety",
          "tip": "detailed cooking tip",
          "importance": "high|medium|low"
        }
      ]
    }`;

    return await this.callLLMAPI(prompt);
  }
}
