import { useState, useEffect, useCallback } from 'react';
import { ThemeSettings } from '../types';
import { ThemeService } from '../services/ThemeService';

export const useTheme = () => {
  const [theme, setTheme] = useState<ThemeSettings>({
    mode: 'auto',
    primaryColor: '#667eea',
    accentColor: '#f093fb',
    glassmorphismIntensity: 50,
    fontSize: 'medium',
    artMode: false
  });
  const [isLoading, setIsLoading] = useState(true);

  const themeService = ThemeService.getInstance();

  useEffect(() => {
    const loadTheme = async () => {
      try {
        const currentTheme = themeService.getCurrentTheme();
        setTheme(currentTheme);
      } catch (error) {
        console.error('Failed to load theme:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadTheme();
  }, [themeService]);

  const updateTheme = useCallback(async (updates: Partial<ThemeSettings>) => {
    try {
      await themeService.updateTheme(updates);
      setTheme(prev => ({ ...prev, ...updates }));
    } catch (error) {
      console.error('Failed to update theme:', error);
    }
  }, [themeService]);

  const resetTheme = useCallback(async () => {
    const defaultTheme: ThemeSettings = {
      mode: 'auto',
      primaryColor: '#667eea',
      accentColor: '#f093fb',
      glassmorphismIntensity: 50,
      fontSize: 'medium',
      artMode: false
    };
    await updateTheme(defaultTheme);
  }, [updateTheme]);

  const toggleArtMode = useCallback(async () => {
    await updateTheme({ artMode: !theme.artMode });
  }, [theme.artMode, updateTheme]);

  const setColorScheme = useCallback(async (mode: 'light' | 'dark' | 'auto') => {
    await updateTheme({ mode });
  }, [updateTheme]);

  const setPrimaryColor = useCallback(async (color: string) => {
    await updateTheme({ primaryColor: color });
  }, [updateTheme]);

  const setAccentColor = useCallback(async (color: string) => {
    await updateTheme({ accentColor: color });
  }, [updateTheme]);

  const setGlassmorphismIntensity = useCallback(async (intensity: number) => {
    await updateTheme({ glassmorphismIntensity: intensity });
  }, [updateTheme]);

  const setFontSize = useCallback(async (fontSize: 'small' | 'medium' | 'large') => {
    await updateTheme({ fontSize });
  }, [updateTheme]);

  const applyPresetTheme = useCallback(async (presetTheme: Partial<ThemeSettings>) => {
    await updateTheme(presetTheme);
  }, [updateTheme]);

  const generateColorPalette = useCallback((baseColor: string) => {
    return themeService.generateColorPalette(baseColor);
  }, [themeService]);

  const getPresetThemes = useCallback(() => {
    return themeService.getPresetThemes();
  }, [themeService]);

  const exportTheme = useCallback(() => {
    return themeService.exportTheme();
  }, [themeService]);

  const importTheme = useCallback(async (themeJson: string) => {
    const success = await themeService.importTheme(themeJson);
    if (success) {
      setTheme(themeService.getCurrentTheme());
    }
    return success;
  }, [themeService]);

  return {
    theme,
    isLoading,
    updateTheme,
    resetTheme,
    toggleArtMode,
    setColorScheme,
    setPrimaryColor,
    setAccentColor,
    setGlassmorphismIntensity,
    setFontSize,
    applyPresetTheme,
    generateColorPalette,
    getPresetThemes,
    exportTheme,
    importTheme
  };
};
