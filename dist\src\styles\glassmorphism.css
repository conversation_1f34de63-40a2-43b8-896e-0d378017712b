/* Glassmorphism Base Styles */
:root {
  /* Color Variables */
  --primary-color: #667eea;
  --secondary-color: #764ba2;
  --accent-color: #f093fb;
  --success-color: #4ade80;
  --warning-color: #fbbf24;
  --error-color: #f87171;
  
  /* Glass Effect Variables */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-bg-dark: rgba(0, 0, 0, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  --glass-backdrop: blur(8px);
  
  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  
  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  
  /* Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
}

/* Dark Theme Variables */
[data-theme="dark"] {
  --glass-bg: rgba(0, 0, 0, 0.2);
  --glass-bg-dark: rgba(255, 255, 255, 0.05);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.5);
}

/* Base Glass Container */
.glass-container {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  box-shadow: var(--glass-shadow);
  position: relative;
  overflow: hidden;
}

.glass-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--glass-border), transparent);
  opacity: 0.5;
}

/* Glass Card Variants */
.glass-card {
  @extend .glass-container;
  padding: var(--spacing-lg);
  transition: all var(--transition-normal);
}

.glass-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.5);
}

.glass-card-sm {
  @extend .glass-card;
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
}

.glass-card-lg {
  @extend .glass-card;
  padding: var(--spacing-xl);
  border-radius: var(--radius-xl);
}

/* Glass Buttons */
.glass-button {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-family: var(--font-family);
  font-weight: 500;
  color: white;
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.glass-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-normal);
}

.glass-button:hover::before {
  left: 100%;
}

.glass-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px 0 rgba(31, 38, 135, 0.4);
}

.glass-button:active {
  transform: translateY(0);
}

/* Glass Button Variants */
.glass-button-primary {
  @extend .glass-button;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.glass-button-accent {
  @extend .glass-button;
  background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
}

.glass-button-success {
  @extend .glass-button;
  background: linear-gradient(135deg, var(--success-color), #22c55e);
}

/* Glass Input Fields */
.glass-input {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  font-family: var(--font-family);
  color: white;
  width: 100%;
  transition: all var(--transition-fast);
}

.glass-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.glass-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Glass Navigation */
.glass-nav {
  @extend .glass-container;
  padding: var(--spacing-md) var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.glass-nav-item {
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-sm);
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all var(--transition-fast);
  font-weight: 500;
}

.glass-nav-item:hover {
  background: var(--glass-bg);
  color: white;
}

.glass-nav-item.active {
  background: var(--primary-color);
  color: white;
}

/* Glass Modal */
.glass-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.glass-modal {
  @extend .glass-container;
  padding: var(--spacing-xl);
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  margin: var(--spacing-lg);
}

/* Glass Loading Spinner */
.glass-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--glass-border);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Glass Progress Bar */
.glass-progress {
  @extend .glass-container;
  height: 8px;
  padding: 0;
  overflow: hidden;
}

.glass-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  border-radius: var(--radius-sm);
  transition: width var(--transition-normal);
}

/* Glass Tooltip */
.glass-tooltip {
  @extend .glass-container;
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-sm);
  position: absolute;
  z-index: 1001;
  white-space: nowrap;
  pointer-events: none;
}

/* Art Mode Styles */
.art-mode {
  background: linear-gradient(135deg, 
    rgba(102, 126, 234, 0.1) 0%,
    rgba(118, 75, 162, 0.1) 25%,
    rgba(240, 147, 251, 0.1) 50%,
    rgba(249, 168, 212, 0.1) 75%,
    rgba(196, 181, 253, 0.1) 100%
  );
  position: relative;
}

.art-mode::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
  pointer-events: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .glass-card {
    padding: var(--spacing-md);
  }
  
  .glass-modal {
    padding: var(--spacing-lg);
    margin: var(--spacing-sm);
  }
  
  .glass-nav {
    padding: var(--spacing-sm) var(--spacing-md);
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .glass-container {
    border-width: 2px;
    border-color: white;
  }
  
  .glass-button {
    border-width: 2px;
  }
}
