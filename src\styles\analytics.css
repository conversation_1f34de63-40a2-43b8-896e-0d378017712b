/* Analytics Dashboard Styles */
.analytics-dashboard {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.analytics-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 30px;
}

.analytics-header h2 {
  font-size: 2.5em;
  margin: 0 0 10px;
  background: linear-gradient(45deg, #fff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.analytics-header p {
  font-size: 1.1em;
  opacity: 0.8;
  margin: 0;
}

.analytics-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  overflow-x: auto;
}

.analytics-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  border-radius: 8px 8px 0 0;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.analytics-tab.active {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-bottom: 2px solid var(--accent-color);
}

.analytics-tab:hover {
  background: rgba(255, 255, 255, 0.05);
  color: white;
}

.tab-icon {
  font-size: 1.2em;
}

.tab-name {
  font-weight: 500;
}

.analytics-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.glass-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid var(--accent-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Overview Tab Styles */
.analytics-overview {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.metric-card {
  padding: 25px;
  text-align: center;
  transition: all 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-5px);
}

.metric-icon {
  font-size: 3em;
  margin-bottom: 15px;
}

.metric-value {
  font-size: 2.5em;
  font-weight: bold;
  margin-bottom: 10px;
  background: linear-gradient(45deg, var(--accent-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.metric-label {
  font-size: 1em;
  opacity: 0.8;
  font-weight: 500;
}

.frequency-chart {
  padding: 25px;
}

.frequency-chart h3 {
  margin: 0 0 20px;
  font-size: 1.3em;
}

.frequency-bars {
  display: flex;
  align-items: end;
  gap: 15px;
  height: 200px;
  padding: 20px 0;
}

.frequency-bar {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.bar-fill {
  width: 100%;
  background: linear-gradient(to top, var(--accent-color), var(--secondary-color));
  border-radius: 4px 4px 0 0;
  min-height: 10px;
  transition: all 0.3s ease;
}

.bar-label {
  font-size: 0.9em;
  font-weight: 500;
  opacity: 0.8;
}

.bar-value {
  font-size: 0.8em;
  opacity: 0.6;
}

.favorite-ingredients {
  padding: 25px;
}

.favorite-ingredients h3 {
  margin: 0 0 20px;
  font-size: 1.3em;
}

.ingredients-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.ingredient-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 10px 0;
}

.ingredient-name {
  min-width: 120px;
  font-weight: 500;
  text-transform: capitalize;
}

.ingredient-bar {
  flex: 1;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.ingredient-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--accent-color), var(--secondary-color));
  border-radius: 4px;
  transition: width 0.3s ease;
}

.ingredient-count {
  min-width: 30px;
  text-align: right;
  font-weight: 500;
  opacity: 0.8;
}

/* Nutrition Tab Styles */
.analytics-nutrition {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.health-score {
  padding: 25px;
  text-align: center;
}

.health-score h3 {
  margin: 0 0 20px;
  font-size: 1.3em;
}

.score-circle {
  width: 150px;
  height: 150px;
  margin: 0 auto 20px;
  position: relative;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.score-fill {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.score-fill::before {
  content: '';
  position: absolute;
  inset: 10px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  backdrop-filter: blur(10px);
}

.score-value {
  font-size: 2.5em;
  font-weight: bold;
  color: white;
  z-index: 1;
}

.macro-distribution {
  padding: 25px;
}

.macro-distribution h3 {
  margin: 0 0 20px;
  font-size: 1.3em;
}

.macro-chart {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.macro-item {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 500;
}

.macro-color {
  width: 20px;
  height: 20px;
  border-radius: 4px;
}

.macro-color.protein {
  background: #4CAF50;
}

.macro-color.carbs {
  background: #2196F3;
}

.macro-color.fat {
  background: #FF9800;
}

.avg-nutrition {
  padding: 25px;
}

.avg-nutrition h3 {
  margin: 0 0 20px;
  font-size: 1.3em;
}

.nutrition-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.nutrition-stat {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.stat-label {
  font-size: 0.9em;
  opacity: 0.8;
}

.stat-value {
  font-size: 1.5em;
  font-weight: bold;
  color: var(--accent-color);
}

/* Cooking Modes Tab Styles */
.analytics-modes {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.mode-card {
  padding: 25px;
  transition: all 0.3s ease;
}

.mode-card:hover {
  transform: translateY(-5px);
}

.mode-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.mode-header h3 {
  margin: 0;
  font-size: 1.2em;
}

.mode-usage {
  font-size: 0.9em;
  opacity: 0.8;
  background: rgba(255, 255, 255, 0.1);
  padding: 4px 12px;
  border-radius: 20px;
}

.mode-stats {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.mode-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-rating {
  display: flex;
  align-items: center;
  gap: 5px;
}

/* Insights Tab Styles */
.analytics-insights {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.recommendations {
  padding: 25px;
}

.recommendations h3 {
  margin: 0 0 20px;
  font-size: 1.3em;
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.recommendation-item {
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  border-left: 4px solid var(--accent-color);
}

.recommendation-item.priority-high {
  border-left-color: #F44336;
}

.recommendation-item.priority-medium {
  border-left-color: #FF9800;
}

.recommendation-item.priority-low {
  border-left-color: #4CAF50;
}

.recommendation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.recommendation-header h4 {
  margin: 0;
  font-size: 1.1em;
}

.priority-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8em;
  font-weight: 500;
}

.priority-badge.high {
  background: rgba(244, 67, 54, 0.3);
  color: #F44336;
}

.priority-badge.medium {
  background: rgba(255, 152, 0, 0.3);
  color: #FF9800;
}

.priority-badge.low {
  background: rgba(76, 175, 80, 0.3);
  color: #4CAF50;
}

.recommendation-action {
  margin-top: 15px;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.recommendation-action:hover {
  background: rgba(255, 255, 255, 0.15);
}

.cooking-insights {
  padding: 25px;
}

.cooking-insights h3 {
  margin: 0 0 20px;
  font-size: 1.3em;
}

.insights-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.insight-item {
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

.insight-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.insight-header h4 {
  margin: 0;
  font-size: 1em;
}

.insight-confidence {
  font-size: 0.8em;
  opacity: 0.8;
  background: rgba(255, 255, 255, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
}

.insight-type {
  font-size: 0.8em;
  opacity: 0.6;
  margin-top: 10px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .analytics-dashboard {
    padding: 15px;
  }
  
  .analytics-tabs {
    flex-wrap: wrap;
  }
  
  .metrics-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
  
  .analytics-nutrition {
    grid-template-columns: 1fr;
  }
  
  .analytics-modes {
    grid-template-columns: 1fr;
  }
  
  .frequency-bars {
    height: 150px;
  }
  
  .score-circle {
    width: 120px;
    height: 120px;
  }
  
  .score-value {
    font-size: 2em;
  }
}
