document.addEventListener('DOMContentLoaded', function() {
    const openSidebarBtn = document.getElementById('open-sidebar');
    const viewRecipesBtn = document.getElementById('view-recipes');
    const apiKeyInput = document.getElementById('api-key');
    const saveConfigBtn = document.getElementById('save-config');
    const testConfigBtn = document.getElementById('test-config');
    const statusDiv = document.getElementById('status');
    const recipeCountSpan = document.getElementById('recipe-count');
    const configStatusSpan = document.getElementById('config-status');

    // Initialize popup
    loadStats();
    loadCurrentConfig();

    openSidebarBtn.addEventListener('click', function() {
        showStatus('Opening ChefAI sidebar...', 'success');

        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            if (tabs[0]) {
                chrome.tabs.sendMessage(tabs[0].id, {action: 'showSidebar'}, function(response) {
                    if (chrome.runtime.lastError) {
                        showStatus('Please refresh the page and try again', 'error');
                    } else {
                        window.close();
                    }
                });
            }
        });
    });

    viewRecipesBtn.addEventListener('click', function() {
        showStatus('Opening sidebar to view recipes...', 'success');

        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            if (tabs[0]) {
                chrome.tabs.sendMessage(tabs[0].id, {action: 'showSidebar'}, function(response) {
                    if (chrome.runtime.lastError) {
                        showStatus('Please refresh the page and try again', 'error');
                    } else {
                        window.close();
                    }
                });
            }
        });
    });

    saveConfigBtn.addEventListener('click', function() {
        const apiKey = apiKeyInput.value.trim();

        if (!apiKey) {
            showStatus('Please enter an API key', 'error');
            return;
        }

        const config = {
            provider: 'openrouter',
            model: 'anthropic/claude-3.5-sonnet',
            apiKey: apiKey,
            temperature: 0.7,
            maxTokens: 2048,
            systemPrompt: 'You are ChefAI, an expert culinary assistant.'
        };

        chrome.storage.local.set({
            llmConfig: JSON.stringify(config)
        }, function() {
            showStatus('✅ Configuration saved!', 'success');
            loadStats();
        });
    });

    testConfigBtn.addEventListener('click', function() {
        const apiKey = apiKeyInput.value.trim();

        if (!apiKey) {
            showStatus('Please enter an API key first', 'error');
            return;
        }

        testConfigBtn.textContent = 'Testing...';
        testConfigBtn.disabled = true;

        // Simple test request
        fetch('https://openrouter.ai/api/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + apiKey,
                'HTTP-Referer': window.location.origin,
                'X-Title': 'ChefAI Extension'
            },
            body: JSON.stringify({
                model: 'anthropic/claude-3.5-sonnet',
                messages: [{ role: 'user', content: 'Say "test successful"' }],
                max_tokens: 10
            })
        })
        .then(response => {
            if (response.ok) {
                showStatus('✅ Connection successful!', 'success');
            } else {
                showStatus('❌ Connection failed: ' + response.status, 'error');
            }
        })
        .catch(error => {
            showStatus('❌ Connection failed: ' + error.message, 'error');
        })
        .finally(() => {
            testConfigBtn.textContent = 'Test';
            testConfigBtn.disabled = false;
        });
    });

    function showStatus(message, type = 'info') {
        statusDiv.textContent = message;
        statusDiv.className = `status ${type}`;
        statusDiv.style.display = 'block';

        setTimeout(() => {
            statusDiv.style.display = 'none';
        }, 3000);
    }

    function loadStats() {
        chrome.storage.local.get(['recipes', 'llmConfig'], function(result) {
            const recipes = result.recipes ? JSON.parse(result.recipes) : [];
            const config = result.llmConfig ? JSON.parse(result.llmConfig) : null;

            // Update recipe count
            if (recipeCountSpan) {
                recipeCountSpan.textContent = recipes.length;
            }

            // Update API status
            if (configStatusSpan) {
                configStatusSpan.textContent = (config && config.apiKey) ? '✅' : '❌';
            }
        });
    }

    function loadCurrentConfig() {
        chrome.storage.local.get(['llmConfig'], function(result) {
            const config = result.llmConfig ? JSON.parse(result.llmConfig) : null;

            if (config && config.apiKey && apiKeyInput) {
                apiKeyInput.value = config.apiKey;
            }
        });
    }
});
