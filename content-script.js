// ChefAI Extension - Working Content Script
console.log('🍳 ChefAI Extension loaded!');

// Simple LLM Service for content script
class SimpleLLMService {
  constructor() {
    this.config = null;
    this.loadConfiguration();
  }

  async loadConfiguration() {
    try {
      const result = await chrome.storage.local.get(['llmConfig']);
      if (result.llmConfig) {
        this.config = JSON.parse(result.llmConfig);
      } else {
        this.config = {
          provider: 'openrouter',
          model: 'anthropic/claude-3.5-sonnet',
          apiKey: '',
          temperature: 0.7,
          maxTokens: 2048,
          systemPrompt: 'You are <PERSON><PERSON><PERSON>, an expert culinary assistant. Generate detailed, practical recipes with clear instructions, ingredient lists, and cooking tips.'
        };
      }
    } catch (error) {
      console.error('Failed to load LLM configuration:', error);
    }
  }

  async saveConfiguration(config) {
    this.config = config;
    try {
      await chrome.storage.local.set({
        llmConfig: JSON.stringify(this.config)
      });
    } catch (error) {
      console.error('Failed to save LLM configuration:', error);
    }
  }

  getConfiguration() {
    return this.config;
  }

  async generateRecipe(request) {
    if (!this.config || !this.config.apiKey) {
      return {
        success: false,
        error: 'Please configure your API key in settings first.'
      };
    }

    try {
      const prompt = this.buildRecipePrompt(request);
      const response = await this.callAPI(prompt);
      
      if (response.success && response.data) {
        const recipe = this.parseRecipeResponse(response.data);
        return {
          success: true,
          data: recipe
        };
      }
      
      return response;
    } catch (error) {
      return {
        success: false,
        error: 'Failed to generate recipe: ' + error.message
      };
    }
  }

  buildRecipePrompt(request) {
    let prompt = 'Generate a detailed recipe with the following requirements:\n\n';

    if (request.ingredients && request.ingredients.length > 0) {
      prompt += 'Available ingredients: ' + request.ingredients.join(', ') + '\n';
    }
    if (request.cuisineType) prompt += 'Cuisine: ' + request.cuisineType + '\n';
    if (request.mealType) prompt += 'Meal type: ' + request.mealType + '\n';
    if (request.servings) prompt += 'Servings: ' + request.servings + '\n';
    if (request.cookingTime) prompt += 'Max cooking time: ' + request.cookingTime + ' minutes\n';
    if (request.difficulty) prompt += 'Difficulty: ' + request.difficulty + '\n';

    prompt += '\nProvide the recipe in JSON format:\n{\n  "title": "Recipe Name",\n  "description": "Brief description",\n  "servings": 4,\n  "prepTime": 15,\n  "cookTime": 30,\n  "difficulty": "Medium",\n  "cuisine": "cuisine type",\n  "ingredients": [{"name": "ingredient", "amount": 1, "unit": "cup"}],\n  "instructions": [{"step": 1, "description": "instruction"}],\n  "tips": ["tip1"],\n  "nutrition": {"calories": 300, "protein": 20, "carbs": 30, "fat": 10}\n}';

    return prompt;
  }

  async callAPI(prompt) {
    try {
      const requestBody = {
        model: this.config.model,
        messages: [
          { role: 'system', content: this.config.systemPrompt },
          { role: 'user', content: prompt }
        ],
        temperature: this.config.temperature,
        max_tokens: this.config.maxTokens
      };

      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ' + this.config.apiKey,
          'HTTP-Referer': window.location.origin,
          'X-Title': 'ChefAI Extension'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error('API request failed: ' + response.status);
      }

      const data = await response.json();
      return {
        success: true,
        data: data.choices[0].message.content
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  parseRecipeResponse(content) {
    try {
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }

      const recipeData = JSON.parse(jsonMatch[0]);
      
      return {
        id: 'recipe-' + Date.now(),
        title: recipeData.title || 'Generated Recipe',
        description: recipeData.description || '',
        servings: recipeData.servings || 4,
        prepTime: recipeData.prepTime || 15,
        cookTime: recipeData.cookTime || 30,
        difficulty: recipeData.difficulty || 'Medium',
        cuisine: recipeData.cuisine || 'International',
        ingredients: recipeData.ingredients || [],
        instructions: recipeData.instructions || [],
        tips: recipeData.tips || [],
        nutrition: recipeData.nutrition || {},
        createdAt: new Date(),
        source: 'AI Generated'
      };
    } catch (error) {
      console.error('Failed to parse recipe:', error);
      return null;
    }
  }

  async testConnection() {
    try {
      const response = await this.callAPI('Respond with "Connection successful"');
      return response;
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }
}

// Simple Recipe Service
class SimpleRecipeService {
  async saveRecipe(recipe) {
    try {
      const result = await chrome.storage.local.get(['recipes']);
      const recipes = result.recipes ? JSON.parse(result.recipes) : [];
      recipes.unshift(recipe);
      await chrome.storage.local.set({
        recipes: JSON.stringify(recipes)
      });
      return recipe.id;
    } catch (error) {
      console.error('Failed to save recipe:', error);
      throw error;
    }
  }

  async getRecipes() {
    try {
      const result = await chrome.storage.local.get(['recipes']);
      return result.recipes ? JSON.parse(result.recipes) : [];
    } catch (error) {
      console.error('Failed to load recipes:', error);
      return [];
    }
  }
}

// Sidebar Manager
class ChefAISidebar {
  constructor() {
    this.isVisible = false;
    this.container = null;
    this.floatingButton = null;
    this.llmService = new SimpleLLMService();
    this.recipeService = new SimpleRecipeService();
    this.ingredients = [];
    
    this.init();
  }

  init() {
    // Wait for DOM
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setup());
    } else {
      this.setup();
    }

    // Listen for messages from popup and background
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      console.log('Content script received message:', message);

      switch (message.action) {
        case 'showSidebar':
          this.showSidebar();
          if (message.ingredients) {
            // Pre-fill ingredients if provided
            setTimeout(() => {
              this.presetIngredients(message.ingredients);
            }, 500);
          }
          sendResponse({ success: true });
          break;

        case 'toggleSidebar':
          this.toggleSidebar();
          sendResponse({ success: true });
          break;

        case 'hideSidebar':
          this.hideSidebar();
          sendResponse({ success: true });
          break;

        default:
          sendResponse({ success: false, error: 'Unknown action' });
      }
    });
  }

  setup() {
    this.createFloatingButton();
    this.createSidebar();
    this.loadStyles();
  }

  createFloatingButton() {
    this.floatingButton = document.createElement('div');
    this.floatingButton.innerHTML = '🍳';
    this.floatingButton.title = 'Open ChefAI Assistant';
    
    Object.assign(this.floatingButton.style, {
      position: 'fixed',
      top: '20px',
      right: '20px',
      width: '60px',
      height: '60px',
      borderRadius: '50%',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      color: 'white',
      fontSize: '24px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      cursor: 'pointer',
      zIndex: '999999',
      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
      border: '2px solid rgba(255, 255, 255, 0.2)',
      transition: 'all 0.3s ease'
    });

    this.floatingButton.addEventListener('click', () => this.toggleSidebar());
    this.floatingButton.addEventListener('mouseenter', () => {
      this.floatingButton.style.transform = 'scale(1.1)';
    });
    this.floatingButton.addEventListener('mouseleave', () => {
      this.floatingButton.style.transform = 'scale(1)';
    });

    document.body.appendChild(this.floatingButton);
  }

  createSidebar() {
    this.container = document.createElement('div');
    this.container.style.display = 'none';
    this.container.style.position = 'fixed';
    this.container.style.top = '0';
    this.container.style.right = '0';
    this.container.style.width = '100%';
    this.container.style.height = '100%';
    this.container.style.zIndex = '999998';
    
    document.body.appendChild(this.container);
  }

  loadStyles() {
    if (document.getElementById('chefai-styles')) return;

    const style = document.createElement('style');
    style.id = 'chefai-styles';
    style.textContent = `
      .chefai-sidebar {
        position: fixed;
        top: 0;
        right: 0;
        width: 420px;
        height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        color: white;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        overflow-y: auto;
        box-shadow: -10px 0 30px rgba(0, 0, 0, 0.3);
        z-index: 999999;
      }
      
      .chefai-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 999997;
      }
      
      .chefai-header {
        padding: 20px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: rgba(255, 255, 255, 0.1);
      }
      
      .chefai-close {
        background: none;
        border: none;
        color: white;
        font-size: 24px;
        cursor: pointer;
        padding: 5px;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      .chefai-close:hover {
        background: rgba(255, 255, 255, 0.2);
      }
      
      .chefai-content {
        padding: 20px;
      }
      
      .chefai-input {
        width: 100%;
        padding: 12px;
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.1);
        color: white;
        margin-bottom: 15px;
        box-sizing: border-box;
      }
      
      .chefai-input::placeholder {
        color: rgba(255, 255, 255, 0.6);
      }
      
      .chefai-button {
        padding: 12px 20px;
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 8px;
        color: white;
        cursor: pointer;
        transition: all 0.3s ease;
        margin: 5px;
      }
      
      .chefai-button:hover {
        background: rgba(255, 255, 255, 0.3);
      }
      
      .chefai-button.primary {
        background: linear-gradient(45deg, #4CAF50, #45a049);
      }
      
      .chefai-ingredient-tag {
        display: inline-block;
        background: rgba(255, 255, 255, 0.2);
        padding: 5px 10px;
        margin: 3px;
        border-radius: 15px;
        font-size: 14px;
      }
      
      .chefai-remove {
        margin-left: 8px;
        cursor: pointer;
        font-weight: bold;
      }
      
      .chefai-status {
        padding: 15px;
        margin: 15px 0;
        border-radius: 8px;
        background: rgba(255, 152, 0, 0.2);
        border: 1px solid rgba(255, 152, 0, 0.5);
      }
      
      .chefai-success {
        background: rgba(76, 175, 80, 0.2);
        border-color: rgba(76, 175, 80, 0.5);
      }
      
      .chefai-error {
        background: rgba(244, 67, 54, 0.2);
        border-color: rgba(244, 67, 54, 0.5);
      }
      
      @media (max-width: 768px) {
        .chefai-sidebar {
          width: 100%;
        }
      }
    `;
    
    document.head.appendChild(style);
  }

  toggleSidebar() {
    if (this.isVisible) {
      this.hideSidebar();
    } else {
      this.showSidebar();
    }
  }

  showSidebar() {
    this.isVisible = true;
    this.container.style.display = 'block';
    this.floatingButton.style.display = 'none';
    this.renderSidebar();
  }

  hideSidebar() {
    this.isVisible = false;
    this.container.style.display = 'none';
    this.floatingButton.style.display = 'flex';
    this.container.innerHTML = '';
  }

  renderSidebar() {
    this.container.innerHTML = `
      <div class="chefai-overlay"></div>
      <div class="chefai-sidebar">
        <div class="chefai-header">
          <h2>🍳 ChefAI Assistant</h2>
          <button class="chefai-close">×</button>
        </div>
        <div class="chefai-content">
          <h3>Generate Recipe</h3>
          
          <div style="margin-bottom: 20px;">
            <label>Add Ingredients:</label>
            <input type="text" class="chefai-input" id="ingredient-input" placeholder="Enter an ingredient...">
            <button class="chefai-button" id="add-ingredient">Add</button>
            <div id="ingredients-list"></div>
          </div>
          
          <div style="margin-bottom: 20px;">
            <label>Cuisine Type:</label>
            <select class="chefai-input" id="cuisine">
              <option value="">Any</option>
              <option value="Italian">Italian</option>
              <option value="Asian">Asian</option>
              <option value="Mexican">Mexican</option>
              <option value="Mediterranean">Mediterranean</option>
            </select>
          </div>
          
          <div style="margin-bottom: 20px;">
            <label>Servings:</label>
            <input type="number" class="chefai-input" id="servings" value="4" min="1" max="12">
          </div>
          
          <button class="chefai-button primary" id="generate-recipe" style="width: 100%; margin: 20px 0;">
            ✨ Generate Recipe with AI
          </button>
          
          <div id="status" style="display: none;"></div>
          
          <div style="margin-top: 30px;">
            <h3>Settings</h3>
            <label>OpenRouter API Key:</label>
            <input type="password" class="chefai-input" id="api-key" placeholder="Enter your API key...">
            <button class="chefai-button" id="save-config">Save</button>
            <button class="chefai-button" id="test-connection">Test</button>
          </div>
        </div>
      </div>
    `;

    this.setupEventListeners();
    this.loadCurrentConfig();
  }

  setupEventListeners() {
    // Close button
    this.container.querySelector('.chefai-close').addEventListener('click', () => this.hideSidebar());
    this.container.querySelector('.chefai-overlay').addEventListener('click', () => this.hideSidebar());

    // Add ingredient
    const addBtn = this.container.querySelector('#add-ingredient');
    const ingredientInput = this.container.querySelector('#ingredient-input');
    
    addBtn.addEventListener('click', () => this.addIngredient());
    ingredientInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') this.addIngredient();
    });

    // Generate recipe
    this.container.querySelector('#generate-recipe').addEventListener('click', () => this.generateRecipe());

    // Save config
    this.container.querySelector('#save-config').addEventListener('click', () => this.saveConfig());

    // Test connection
    this.container.querySelector('#test-connection').addEventListener('click', () => this.testConnection());
  }

  addIngredient() {
    const input = this.container.querySelector('#ingredient-input');
    const ingredient = input.value.trim();
    
    if (ingredient && !this.ingredients.includes(ingredient)) {
      this.ingredients.push(ingredient);
      input.value = '';
      this.updateIngredientsList();
    }
  }

  updateIngredientsList() {
    const list = this.container.querySelector('#ingredients-list');
    list.innerHTML = this.ingredients.map(ingredient => 
      `<span class="chefai-ingredient-tag">
        ${ingredient}
        <span class="chefai-remove" onclick="window.chefAI.removeIngredient('${ingredient}')">×</span>
      </span>`
    ).join('');
  }

  removeIngredient(ingredient) {
    this.ingredients = this.ingredients.filter(ing => ing !== ingredient);
    this.updateIngredientsList();
  }

  presetIngredients(ingredientsList) {
    if (!this.isVisible || !ingredientsList || !Array.isArray(ingredientsList)) return;

    // Add ingredients to the list
    ingredientsList.forEach(ingredient => {
      const trimmed = ingredient.trim();
      if (trimmed && !this.ingredients.includes(trimmed)) {
        this.ingredients.push(trimmed);
      }
    });

    // Update the display
    this.updateIngredientsList();

    // Show a helpful message
    this.showStatus(`✅ Added ${ingredientsList.length} ingredients from popup`, 'success');
  }

  async generateRecipe() {
    const button = this.container.querySelector('#generate-recipe');
    const status = this.container.querySelector('#status');
    
    button.textContent = '⏳ Generating...';
    button.disabled = true;
    status.style.display = 'none';

    try {
      const request = {
        ingredients: this.ingredients,
        cuisineType: this.container.querySelector('#cuisine').value,
        servings: parseInt(this.container.querySelector('#servings').value)
      };

      const response = await this.llmService.generateRecipe(request);
      
      if (response.success) {
        await this.recipeService.saveRecipe(response.data);
        this.showStatus('✅ Recipe generated successfully!', 'success');
        setTimeout(() => this.hideSidebar(), 2000);
      } else {
        this.showStatus('❌ ' + response.error, 'error');
      }
    } catch (error) {
      this.showStatus('❌ Error: ' + error.message, 'error');
    } finally {
      button.textContent = '✨ Generate Recipe with AI';
      button.disabled = false;
    }
  }

  async saveConfig() {
    const apiKey = this.container.querySelector('#api-key').value;
    
    if (!apiKey) {
      this.showStatus('❌ Please enter an API key', 'error');
      return;
    }

    const config = {
      provider: 'openrouter',
      model: 'anthropic/claude-3.5-sonnet',
      apiKey: apiKey,
      temperature: 0.7,
      maxTokens: 2048,
      systemPrompt: 'You are ChefAI, an expert culinary assistant.'
    };

    await this.llmService.saveConfiguration(config);
    this.showStatus('✅ Configuration saved!', 'success');
  }

  async testConnection() {
    const button = this.container.querySelector('#test-connection');
    button.textContent = 'Testing...';
    button.disabled = true;

    try {
      const result = await this.llmService.testConnection();
      if (result.success) {
        this.showStatus('✅ Connection successful!', 'success');
      } else {
        this.showStatus('❌ ' + result.error, 'error');
      }
    } catch (error) {
      this.showStatus('❌ Connection failed', 'error');
    } finally {
      button.textContent = 'Test';
      button.disabled = false;
    }
  }

  async loadCurrentConfig() {
    const config = this.llmService.getConfiguration();
    if (config && config.apiKey) {
      this.container.querySelector('#api-key').value = config.apiKey;
    }
  }

  showStatus(message, type = 'info') {
    const status = this.container.querySelector('#status');
    status.textContent = message;
    status.className = `chefai-status chefai-${type}`;
    status.style.display = 'block';
  }
}

// Initialize the extension
window.chefAI = new ChefAISidebar();

console.log('🍳 ChefAI Extension initialized successfully!');
