import React, { useState, useEffect } from 'react';
import { AnalyticsService } from '../services/AnalyticsService';
import { NutritionService } from '../services/NutritionService';
import { CookingModesService } from '../services/CookingModesService';
import { useI18n } from '../hooks/useI18n';

interface AnalyticsDashboardProps {
  userId?: string;
}

export const AnalyticsDashboard: React.FC<AnalyticsDashboardProps> = ({ userId }) => {
  const { t } = useI18n();
  const [analytics, setAnalytics] = useState<any>(null);
  const [nutritionInsights, setNutritionInsights] = useState<any>(null);
  const [cookingModes, setCookingModes] = useState<any>(null);
  const [recommendations, setRecommendations] = useState<any[]>([]);
  const [insights, setInsights] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'nutrition' | 'modes' | 'insights'>('overview');

  const analyticsService = AnalyticsService.getInstance();
  const nutritionService = NutritionService.getInstance();
  const cookingModesService = CookingModesService.getInstance();

  useEffect(() => {
    loadAnalyticsData();
  }, [userId]);

  const loadAnalyticsData = async () => {
    try {
      setIsLoading(true);
      
      const [
        userAnalytics,
        nutritionalInsights,
        modeAnalytics,
        personalizedRecs,
        cookingInsights,
        advancedMetrics
      ] = await Promise.all([
        analyticsService.getUserAnalytics(userId),
        analyticsService.getNutritionalInsights(userId),
        analyticsService.getCookingModeAnalytics(),
        analyticsService.getPersonalizedRecommendations(userId),
        analyticsService.getCookingInsights(),
        analyticsService.getAdvancedMetrics()
      ]);

      setAnalytics({ ...userAnalytics, ...advancedMetrics });
      setNutritionInsights(nutritionalInsights);
      setCookingModes(modeAnalytics);
      setRecommendations(personalizedRecs);
      setInsights(cookingInsights);
    } catch (error) {
      console.error('Failed to load analytics data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatNumber = (num: number): string => {
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'k';
    }
    return num.toString();
  };

  const getHealthScoreColor = (score: number): string => {
    if (score >= 80) return '#4CAF50';
    if (score >= 60) return '#FF9800';
    return '#F44336';
  };

  if (isLoading) {
    return (
      <div className="analytics-loading">
        <div className="glass-spinner"></div>
        <p>{t('loading.analytics')}</p>
      </div>
    );
  }

  return (
    <div className="analytics-dashboard">
      <div className="analytics-header glass-card">
        <h2>{t('analytics.dashboard_title')}</h2>
        <p>{t('analytics.dashboard_description')}</p>
      </div>

      {/* Navigation Tabs */}
      <div className="analytics-tabs">
        {[
          { id: 'overview', name: t('analytics.overview'), icon: '📊' },
          { id: 'nutrition', name: t('analytics.nutrition'), icon: '🥗' },
          { id: 'modes', name: t('analytics.cooking_modes'), icon: '👨‍🍳' },
          { id: 'insights', name: t('analytics.insights'), icon: '💡' }
        ].map((tab) => (
          <button
            key={tab.id}
            className={`analytics-tab ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => setActiveTab(tab.id as any)}
          >
            <span className="tab-icon">{tab.icon}</span>
            <span className="tab-name">{tab.name}</span>
          </button>
        ))}
      </div>

      {/* Overview Tab */}
      {activeTab === 'overview' && (
        <div className="analytics-overview">
          {/* Key Metrics */}
          <div className="metrics-grid">
            <div className="metric-card glass-card">
              <div className="metric-icon">🍳</div>
              <div className="metric-value">{analytics?.totalRecipesGenerated || 0}</div>
              <div className="metric-label">{t('analytics.recipes_generated')}</div>
            </div>
            
            <div className="metric-card glass-card">
              <div className="metric-icon">⭐</div>
              <div className="metric-value">{analytics?.successfulRecipes || 0}</div>
              <div className="metric-label">{t('analytics.successful_recipes')}</div>
            </div>
            
            <div className="metric-card glass-card">
              <div className="metric-icon">📤</div>
              <div className="metric-value">{analytics?.sharedRecipes || 0}</div>
              <div className="metric-label">{t('analytics.shared_recipes')}</div>
            </div>
            
            <div className="metric-card glass-card">
              <div className="metric-icon">🏆</div>
              <div className="metric-value">{analytics?.challengesParticipated || 0}</div>
              <div className="metric-label">{t('analytics.challenges_joined')}</div>
            </div>
          </div>

          {/* Cooking Frequency Chart */}
          <div className="frequency-chart glass-card">
            <h3>{t('analytics.cooking_frequency')}</h3>
            <div className="frequency-bars">
              {Object.entries(analytics?.cookingFrequency || {}).map(([day, count]) => (
                <div key={day} className="frequency-bar">
                  <div 
                    className="bar-fill" 
                    style={{ height: `${(count as number) * 20}px` }}
                  ></div>
                  <div className="bar-label">{day.slice(0, 3)}</div>
                  <div className="bar-value">{count}</div>
                </div>
              ))}
            </div>
          </div>

          {/* Favorite Ingredients */}
          <div className="favorite-ingredients glass-card">
            <h3>{t('analytics.favorite_ingredients')}</h3>
            <div className="ingredients-list">
              {Object.entries(analytics?.favoriteIngredients || {})
                .sort(([,a], [,b]) => (b as number) - (a as number))
                .slice(0, 10)
                .map(([ingredient, count]) => (
                  <div key={ingredient} className="ingredient-item">
                    <span className="ingredient-name">{ingredient}</span>
                    <div className="ingredient-bar">
                      <div 
                        className="ingredient-fill" 
                        style={{ width: `${(count as number) * 10}%` }}
                      ></div>
                    </div>
                    <span className="ingredient-count">{count}</span>
                  </div>
                ))}
            </div>
          </div>
        </div>
      )}

      {/* Nutrition Tab */}
      {activeTab === 'nutrition' && (
        <div className="analytics-nutrition">
          {/* Health Score */}
          <div className="health-score glass-card">
            <h3>{t('analytics.health_score')}</h3>
            <div className="score-circle">
              <div 
                className="score-fill" 
                style={{ 
                  background: `conic-gradient(${getHealthScoreColor(nutritionInsights?.healthScore || 0)} ${(nutritionInsights?.healthScore || 0) * 3.6}deg, #e0e0e0 0deg)`
                }}
              >
                <div className="score-value">{nutritionInsights?.healthScore || 0}</div>
              </div>
            </div>
            <p>{t('analytics.health_score_description')}</p>
          </div>

          {/* Macro Distribution */}
          <div className="macro-distribution glass-card">
            <h3>{t('analytics.macro_distribution')}</h3>
            <div className="macro-chart">
              <div className="macro-item">
                <div className="macro-color protein"></div>
                <span>{t('analytics.protein')}: {nutritionInsights?.macroDistribution?.protein || 0}%</span>
              </div>
              <div className="macro-item">
                <div className="macro-color carbs"></div>
                <span>{t('analytics.carbs')}: {nutritionInsights?.macroDistribution?.carbs || 0}%</span>
              </div>
              <div className="macro-item">
                <div className="macro-color fat"></div>
                <span>{t('analytics.fat')}: {nutritionInsights?.macroDistribution?.fat || 0}%</span>
              </div>
            </div>
          </div>

          {/* Average Nutrition */}
          <div className="avg-nutrition glass-card">
            <h3>{t('analytics.average_nutrition')}</h3>
            <div className="nutrition-stats">
              <div className="nutrition-stat">
                <span className="stat-label">{t('analytics.calories')}</span>
                <span className="stat-value">{nutritionInsights?.averageCalories || 0}</span>
              </div>
              <div className="nutrition-stat">
                <span className="stat-label">{t('analytics.protein')}</span>
                <span className="stat-value">{nutritionInsights?.averageProtein || 0}g</span>
              </div>
              <div className="nutrition-stat">
                <span className="stat-label">{t('analytics.fiber')}</span>
                <span className="stat-value">{nutritionInsights?.averageFiber || 0}g</span>
              </div>
              <div className="nutrition-stat">
                <span className="stat-label">{t('analytics.sodium')}</span>
                <span className="stat-value">{nutritionInsights?.averageSodium || 0}mg</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Cooking Modes Tab */}
      {activeTab === 'modes' && (
        <div className="analytics-modes">
          <div className="modes-grid">
            {Object.entries(cookingModes || {}).map(([mode, stats]) => (
              <div key={mode} className="mode-card glass-card">
                <div className="mode-header">
                  <h3>{t(`analytics.mode_${mode}`)}</h3>
                  <div className="mode-usage">{(stats as any).count} {t('analytics.times_used')}</div>
                </div>
                
                <div className="mode-stats">
                  <div className="mode-stat">
                    <span className="stat-label">{t('analytics.satisfaction')}</span>
                    <div className="stat-rating">
                      {'⭐'.repeat(Math.round((stats as any).satisfaction || 0))}
                      <span>{((stats as any).satisfaction || 0).toFixed(1)}</span>
                    </div>
                  </div>
                  
                  {mode === 'quickCook' && (
                    <div className="mode-stat">
                      <span className="stat-label">{t('analytics.avg_time')}</span>
                      <span className="stat-value">{(stats as any).avgTime}min</span>
                    </div>
                  )}
                  
                  {mode === 'healthyEating' && (
                    <div className="mode-stat">
                      <span className="stat-label">{t('analytics.avg_calories')}</span>
                      <span className="stat-value">{(stats as any).avgCalories}</span>
                    </div>
                  )}
                  
                  {mode === 'experimental' && (
                    <div className="mode-stat">
                      <span className="stat-label">{t('analytics.success_rate')}</span>
                      <span className="stat-value">{(stats as any).successRate}%</span>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Insights Tab */}
      {activeTab === 'insights' && (
        <div className="analytics-insights">
          {/* Personalized Recommendations */}
          <div className="recommendations glass-card">
            <h3>{t('analytics.recommendations')}</h3>
            <div className="recommendations-list">
              {recommendations.map((rec, index) => (
                <div key={index} className={`recommendation-item priority-${rec.priority}`}>
                  <div className="recommendation-header">
                    <h4>{rec.title}</h4>
                    <span className={`priority-badge ${rec.priority}`}>
                      {t(`analytics.priority_${rec.priority}`)}
                    </span>
                  </div>
                  <p>{rec.description}</p>
                  {rec.actionable && (
                    <button className="recommendation-action glass-button">
                      {t('analytics.take_action')}
                    </button>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Cooking Insights */}
          <div className="cooking-insights glass-card">
            <h3>{t('analytics.cooking_insights')}</h3>
            <div className="insights-list">
              {insights.map((insight, index) => (
                <div key={index} className="insight-item">
                  <div className="insight-header">
                    <h4>{insight.title}</h4>
                    <div className="insight-confidence">
                      {insight.confidence}% {t('analytics.confidence')}
                    </div>
                  </div>
                  <p>{insight.description}</p>
                  <div className="insight-type">{t(`analytics.insight_${insight.type}`)}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
