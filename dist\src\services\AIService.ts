import { AIRecipeRequest, Recipe, Ingredient, Instruction, NutritionalInfo } from '../types';

interface AIRecipeResponse {
  title: string;
  description?: string;
  ingredients: Array<{
    name: string;
    amount: number;
    unit: string;
    category?: string;
    isOptional?: boolean;
    substitutes?: string[];
  }>;
  instructions: Array<{
    description: string;
    duration?: number;
    temperature?: number;
    tips?: string[];
  }>;
  prepTime?: number;
  cookTime?: number;
  servings?: number;
  difficulty?: 'Easy' | 'Medium' | 'Hard';
  cuisine?: string;
  tags?: string[];
  nutritionalInfo?: NutritionalInfo;
  image?: string;
}

export class AIService {
  private static instance: AIService;
  private apiKey: string | null = null;
  private baseUrl = 'https://api.openai.com/v1';

  private constructor() {
    this.loadApiKey();
  }

  public static getInstance(): AIService {
    if (!AIService.instance) {
      AIService.instance = new AIService();
    }
    return AIService.instance;
  }

  private async loadApiKey(): Promise<void> {
    try {
      const result = await chrome.storage.sync.get(['openai_api_key']);
      this.apiKey = result.openai_api_key || null;
    } catch (error) {
      console.error('Failed to load API key:', error);
    }
  }

  public async setApiKey(apiKey: string): Promise<void> {
    this.apiKey = apiKey;
    await chrome.storage.sync.set({ openai_api_key: apiKey });
  }

  public async generateRecipe(request: AIRecipeRequest): Promise<AIRecipeResponse | null> {
    if (!this.apiKey) {
      // Use mock data for development/demo
      return this.generateMockRecipe(request);
    }

    try {
      const prompt = this.buildPrompt(request);
      
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model: 'gpt-4',
          messages: [
            {
              role: 'system',
              content: 'You are a professional chef and recipe developer. Generate detailed, creative, and practical recipes based on user requirements. Always respond with valid JSON format.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: 0.8,
          max_tokens: 2000
        })
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      const data = await response.json();
      const content = data.choices[0]?.message?.content;

      if (!content) {
        throw new Error('No content received from API');
      }

      // Parse JSON response
      const recipe = JSON.parse(content) as AIRecipeResponse;
      return this.validateAndEnhanceRecipe(recipe, request);

    } catch (error) {
      console.error('AI recipe generation failed:', error);
      // Fallback to mock recipe
      return this.generateMockRecipe(request);
    }
  }

  private buildPrompt(request: AIRecipeRequest): string {
    let prompt = 'Generate a detailed recipe with the following requirements:\n\n';

    if (request.ingredients && request.ingredients.length > 0) {
      prompt += `Available ingredients: ${request.ingredients.join(', ')}\n`;
    }

    if (request.title) {
      prompt += `Recipe title/theme: ${request.title}\n`;
    }

    if (request.cuisine) {
      prompt += `Cuisine type: ${request.cuisine}\n`;
    }

    if (request.dietaryRestrictions && request.dietaryRestrictions.length > 0) {
      prompt += `Dietary restrictions: ${request.dietaryRestrictions.join(', ')}\n`;
    }

    if (request.cookingTime) {
      prompt += `Maximum cooking time: ${request.cookingTime} minutes\n`;
    }

    if (request.servings) {
      prompt += `Number of servings: ${request.servings}\n`;
    }

    if (request.difficulty) {
      prompt += `Difficulty level: ${request.difficulty}\n`;
    }

    if (request.mealType) {
      prompt += `Meal type: ${request.mealType}\n`;
    }

    prompt += `\nPlease respond with a JSON object containing:
- title: string
- description: string (brief description)
- ingredients: array of objects with name, amount, unit, category
- instructions: array of objects with description, duration, temperature
- prepTime: number (minutes)
- cookTime: number (minutes)
- servings: number
- difficulty: "Easy" | "Medium" | "Hard"
- cuisine: string
- tags: array of strings
- nutritionalInfo: object with calories, protein, carbs, fat, etc.

Make the recipe creative, practical, and delicious!`;

    return prompt;
  }

  private validateAndEnhanceRecipe(recipe: AIRecipeResponse, request: AIRecipeRequest): AIRecipeResponse {
    // Ensure required fields
    if (!recipe.title) {
      recipe.title = request.title || 'Generated Recipe';
    }

    if (!recipe.ingredients || recipe.ingredients.length === 0) {
      recipe.ingredients = request.ingredients?.map(ingredient => ({
        name: ingredient,
        amount: 1,
        unit: 'piece',
        category: 'Other'
      })) || [];
    }

    if (!recipe.instructions || recipe.instructions.length === 0) {
      recipe.instructions = [
        { description: 'Prepare all ingredients according to the recipe requirements.' },
        { description: 'Follow standard cooking procedures for this type of dish.' },
        { description: 'Season to taste and serve hot.' }
      ];
    }

    // Set defaults
    recipe.servings = recipe.servings || request.servings || 4;
    recipe.difficulty = recipe.difficulty || request.difficulty || 'Medium';
    recipe.cuisine = recipe.cuisine || request.cuisine || 'International';
    recipe.prepTime = recipe.prepTime || 15;
    recipe.cookTime = recipe.cookTime || 30;
    recipe.tags = recipe.tags || [];

    // Add nutritional info if missing
    if (!recipe.nutritionalInfo) {
      recipe.nutritionalInfo = this.estimateNutrition(recipe);
    }

    return recipe;
  }

  private estimateNutrition(recipe: AIRecipeResponse): NutritionalInfo {
    // Basic nutrition estimation based on ingredients
    // In a real implementation, this would use a nutrition database
    const baseCalories = recipe.ingredients.length * 50;
    
    return {
      calories: Math.round(baseCalories + Math.random() * 200),
      protein: Math.round(10 + Math.random() * 20),
      carbs: Math.round(20 + Math.random() * 40),
      fat: Math.round(5 + Math.random() * 15),
      fiber: Math.round(2 + Math.random() * 8),
      sugar: Math.round(5 + Math.random() * 15),
      sodium: Math.round(200 + Math.random() * 800),
      vitamins: {},
      minerals: {},
      allergens: []
    };
  }

  private generateMockRecipe(request: AIRecipeRequest): AIRecipeResponse {
    const mockRecipes = [
      {
        title: request.title || 'Delicious Pasta Primavera',
        description: 'A colorful and nutritious pasta dish loaded with fresh vegetables',
        ingredients: [
          { name: 'Pasta', amount: 300, unit: 'g', category: 'Grains' },
          { name: 'Bell peppers', amount: 2, unit: 'pieces', category: 'Vegetables' },
          { name: 'Zucchini', amount: 1, unit: 'piece', category: 'Vegetables' },
          { name: 'Cherry tomatoes', amount: 200, unit: 'g', category: 'Vegetables' },
          { name: 'Olive oil', amount: 3, unit: 'tbsp', category: 'Oils' },
          { name: 'Garlic', amount: 3, unit: 'cloves', category: 'Vegetables' },
          { name: 'Parmesan cheese', amount: 50, unit: 'g', category: 'Dairy' }
        ],
        instructions: [
          { description: 'Cook pasta according to package instructions until al dente', duration: 10 },
          { description: 'Heat olive oil in a large pan over medium heat', duration: 2 },
          { description: 'Add garlic and sauté until fragrant', duration: 1 },
          { description: 'Add bell peppers and zucchini, cook until tender', duration: 5 },
          { description: 'Add cherry tomatoes and cook until they start to burst', duration: 3 },
          { description: 'Toss with cooked pasta and Parmesan cheese', duration: 2 },
          { description: 'Season with salt and pepper, serve immediately', duration: 1 }
        ],
        prepTime: 15,
        cookTime: 25,
        servings: request.servings || 4,
        difficulty: request.difficulty || 'Easy',
        cuisine: request.cuisine || 'Italian',
        tags: ['Vegetarian', 'Quick', 'Healthy', 'Colorful'],
        nutritionalInfo: {
          calories: 420,
          protein: 15,
          carbs: 65,
          fat: 12,
          fiber: 6,
          sugar: 8,
          sodium: 320,
          vitamins: { 'Vitamin C': 80, 'Vitamin A': 45 },
          minerals: { 'Calcium': 150, 'Iron': 3 },
          allergens: ['Gluten', 'Dairy']
        }
      }
    ];

    return mockRecipes[0];
  }

  public async generateRecipeImage(recipeTitle: string): Promise<string | null> {
    if (!this.apiKey) {
      return null;
    }

    try {
      const response = await fetch(`${this.baseUrl}/images/generations`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model: 'dall-e-3',
          prompt: `A beautiful, appetizing photo of ${recipeTitle}, professional food photography, well-lit, colorful, restaurant quality presentation`,
          size: '1024x1024',
          quality: 'standard',
          n: 1
        })
      });

      if (!response.ok) {
        throw new Error(`Image generation failed: ${response.status}`);
      }

      const data = await response.json();
      return data.data[0]?.url || null;

    } catch (error) {
      console.error('Image generation failed:', error);
      return null;
    }
  }
}
