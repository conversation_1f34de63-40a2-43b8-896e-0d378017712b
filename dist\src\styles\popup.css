/* Popup Specific Styles */
body {
  margin: 0;
  padding: 0;
  font-family: var(--font-family);
  background: linear-gradient(135deg, 
    #667eea 0%, 
    #764ba2 50%, 
    #f093fb 100%
  );
  min-height: 600px;
  width: 400px;
  overflow-x: hidden;
}

.popup-container {
  width: 100%;
  height: 100%;
  position: relative;
  color: white;
}

/* Header Section */
.popup-header {
  padding: var(--spacing-lg);
  text-align: center;
  position: relative;
}

.popup-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border-bottom: 1px solid var(--glass-border);
}

.popup-header > * {
  position: relative;
  z-index: 1;
}

.popup-logo {
  width: 48px;
  height: 48px;
  margin: 0 auto var(--spacing-md);
  background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  font-weight: 700;
  box-shadow: var(--glass-shadow);
}

.popup-title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  margin: 0 0 var(--spacing-xs);
  background: linear-gradient(135deg, white, rgba(255, 255, 255, 0.8));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.popup-subtitle {
  font-size: var(--font-size-sm);
  opacity: 0.8;
  margin: 0;
}

/* Main Content */
.popup-content {
  padding: var(--spacing-lg);
  height: calc(100% - 120px);
  overflow-y: auto;
}

/* Quick Actions */
.quick-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.quick-action-card {
  @extend .glass-card-sm;
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-normal);
  min-height: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.quick-action-card:hover {
  transform: translateY(-4px) scale(1.02);
}

.quick-action-icon {
  font-size: var(--font-size-2xl);
  margin-bottom: var(--spacing-xs);
  opacity: 0.9;
}

.quick-action-title {
  font-size: var(--font-size-sm);
  font-weight: 600;
  margin: 0;
}

/* Recipe Input Section */
.recipe-input-section {
  margin-bottom: var(--spacing-lg);
}

.section-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin: 0 0 var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.section-icon {
  font-size: var(--font-size-xl);
  opacity: 0.8;
}

.input-group {
  margin-bottom: var(--spacing-md);
}

.input-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: 500;
  margin-bottom: var(--spacing-xs);
  opacity: 0.9;
}

.ingredient-input-container {
  position: relative;
}

.ingredient-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-sm);
}

.ingredient-tag {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-xs);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.ingredient-tag-remove {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  padding: 0;
  font-size: var(--font-size-xs);
}

.ingredient-tag-remove:hover {
  color: var(--error-color);
}

/* Generate Button */
.generate-button {
  @extend .glass-button-primary;
  width: 100%;
  padding: var(--spacing-md);
  font-size: var(--font-size-base);
  font-weight: 600;
  margin-bottom: var(--spacing-lg);
  position: relative;
  overflow: hidden;
}

.generate-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.generate-button.loading {
  color: transparent;
}

.generate-button.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Recent Recipes */
.recent-recipes {
  margin-bottom: var(--spacing-lg);
}

.recipe-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.recipe-item {
  @extend .glass-card-sm;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.recipe-item:hover {
  transform: translateX(4px);
}

.recipe-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-xs);
}

.recipe-item-title {
  font-size: var(--font-size-sm);
  font-weight: 600;
  margin: 0;
  flex: 1;
}

.recipe-item-time {
  font-size: var(--font-size-xs);
  opacity: 0.7;
  white-space: nowrap;
  margin-left: var(--spacing-sm);
}

.recipe-item-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-xs);
  opacity: 0.8;
}

.recipe-item-tags {
  display: flex;
  gap: var(--spacing-xs);
}

.recipe-item-tag {
  background: rgba(255, 255, 255, 0.1);
  padding: 2px var(--spacing-xs);
  border-radius: var(--radius-sm);
}

/* Footer Actions */
.popup-footer {
  padding: var(--spacing-md) var(--spacing-lg);
  border-top: 1px solid var(--glass-border);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
}

.footer-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-button {
  @extend .glass-button;
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-sm);
}

.footer-button-primary {
  @extend .footer-button;
  background: var(--primary-color);
}

/* Loading States */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(2px);
  -webkit-backdrop-filter: blur(2px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.loading-content {
  @extend .glass-card;
  text-align: center;
  max-width: 200px;
}

.loading-spinner {
  @extend .glass-spinner;
  margin: 0 auto var(--spacing-md);
}

.loading-text {
  font-size: var(--font-size-sm);
  opacity: 0.9;
}

/* Error States */
.error-message {
  @extend .glass-card-sm;
  background: rgba(248, 113, 113, 0.1);
  border-color: var(--error-color);
  color: var(--error-color);
  margin-bottom: var(--spacing-md);
}

.error-icon {
  margin-right: var(--spacing-sm);
}

/* Success States */
.success-message {
  @extend .glass-card-sm;
  background: rgba(74, 222, 128, 0.1);
  border-color: var(--success-color);
  color: var(--success-color);
  margin-bottom: var(--spacing-md);
}

/* Scrollbar Styling */
.popup-content::-webkit-scrollbar {
  width: 6px;
}

.popup-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-sm);
}

.popup-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-sm);
}

.popup-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Animations */
@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.popup-content > * {
  animation: slideInUp 0.3s ease-out;
}

.popup-content > *:nth-child(2) {
  animation-delay: 0.1s;
}

.popup-content > *:nth-child(3) {
  animation-delay: 0.2s;
}

.popup-content > *:nth-child(4) {
  animation-delay: 0.3s;
}
