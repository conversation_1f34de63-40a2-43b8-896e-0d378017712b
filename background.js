// ChefAI Extension Background Script
console.log('🍳 ChefAI Background Script loaded');

// Handle extension installation
chrome.runtime.onInstalled.addListener((details) => {
  console.log('ChefAI Extension installed:', details.reason);
  
  if (details.reason === 'install') {
    // Set default configuration
    chrome.storage.local.set({
      llmConfig: JSON.stringify({
        provider: 'openrouter',
        model: 'anthropic/claude-3.5-sonnet',
        apiKey: '',
        temperature: 0.7,
        maxTokens: 2048,
        systemPrompt: 'You are <PERSON><PERSON><PERSON>, an expert culinary assistant. Generate detailed, practical recipes with clear instructions, ingredient lists, and cooking tips.'
      }),
      recipes: JSON.stringify([])
    });
  }
});

// Handle messages from content scripts and popup
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('Background received message:', message);
  
  switch (message.action) {
    case 'toggleSidebar':
      // Send message to content script to toggle sidebar
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (tabs[0]) {
          chrome.tabs.sendMessage(tabs[0].id, { action: 'toggleSidebar' });
        }
      });
      sendResponse({ success: true });
      break;
      
    case 'showSidebar':
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (tabs[0]) {
          chrome.tabs.sendMessage(tabs[0].id, { action: 'showSidebar' });
        }
      });
      sendResponse({ success: true });
      break;
      
    case 'getConfig':
      chrome.storage.local.get(['llmConfig'], (result) => {
        sendResponse({ 
          success: true, 
          config: result.llmConfig ? JSON.parse(result.llmConfig) : null 
        });
      });
      return true; // Keep message channel open for async response
      
    case 'saveConfig':
      chrome.storage.local.set({
        llmConfig: JSON.stringify(message.config)
      }, () => {
        sendResponse({ success: true });
      });
      return true;
      
    case 'getRecipes':
      chrome.storage.local.get(['recipes'], (result) => {
        sendResponse({ 
          success: true, 
          recipes: result.recipes ? JSON.parse(result.recipes) : [] 
        });
      });
      return true;
      
    case 'saveRecipe':
      chrome.storage.local.get(['recipes'], (result) => {
        const recipes = result.recipes ? JSON.parse(result.recipes) : [];
        recipes.unshift(message.recipe);
        
        chrome.storage.local.set({
          recipes: JSON.stringify(recipes)
        }, () => {
          sendResponse({ success: true, id: message.recipe.id });
        });
      });
      return true;
      
    default:
      sendResponse({ success: false, error: 'Unknown action' });
  }
});

// Handle extension icon click
chrome.action.onClicked.addListener((tab) => {
  console.log('Extension icon clicked');
  
  // Send message to content script to show sidebar
  chrome.tabs.sendMessage(tab.id, { action: 'toggleSidebar' }, (response) => {
    if (chrome.runtime.lastError) {
      console.log('Could not send message to content script:', chrome.runtime.lastError.message);
    }
  });
});

// Handle notifications
chrome.notifications.onClicked.addListener((notificationId) => {
  console.log('Notification clicked:', notificationId);
});

// Keep service worker alive
let keepAlive = () => setInterval(chrome.runtime.getPlatformInfo, 20e3);
chrome.runtime.onStartup.addListener(keepAlive);
keepAlive();
