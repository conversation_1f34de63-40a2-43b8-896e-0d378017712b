<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ChefAI - Full Dashboard</title>
  <style>
    body {
      margin: 0;
      padding: 20px;
      font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
      min-height: 100vh;
      color: white;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(20px);
      border-radius: 20px;
      padding: 30px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    }

    .header {
      text-align: center;
      margin-bottom: 40px;
    }

    .logo {
      font-size: 4em;
      margin-bottom: 10px;
    }

    .title {
      font-size: 2.5em;
      margin: 0;
      background: linear-gradient(45deg, #fff, #f0f0f0);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .subtitle {
      font-size: 1.2em;
      opacity: 0.8;
      margin: 10px 0;
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
      margin: 40px 0;
    }

    .feature-card {
      background: rgba(255, 255, 255, 0.15);
      border-radius: 15px;
      padding: 25px;
      text-align: center;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      transition: all 0.3s ease;
    }

    .feature-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    }

    .feature-icon {
      font-size: 3em;
      margin-bottom: 15px;
    }

    .feature-title {
      font-size: 1.3em;
      font-weight: 600;
      margin-bottom: 10px;
    }

    .feature-description {
      opacity: 0.9;
      line-height: 1.5;
    }

    .demo-section {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 15px;
      padding: 30px;
      margin: 30px 0;
      text-align: center;
    }

    .demo-title {
      font-size: 1.8em;
      margin-bottom: 15px;
      color: #FFD700;
    }

    .demo-text {
      font-size: 1.1em;
      line-height: 1.6;
      margin-bottom: 20px;
    }

    .button {
      background: linear-gradient(45deg, #4CAF50, #45a049);
      border: none;
      border-radius: 25px;
      color: white;
      padding: 12px 30px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      margin: 10px;
    }

    .button:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
    }

    .installation-guide {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 15px;
      padding: 25px;
      margin: 30px 0;
    }

    .installation-guide h3 {
      color: #FFD700;
      margin-top: 0;
    }

    .installation-guide ol {
      padding-left: 20px;
    }

    .installation-guide li {
      margin: 10px 0;
      line-height: 1.5;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <div class="logo">🍳</div>
      <h1 class="title">ChefAI Dashboard</h1>
      <p class="subtitle">Professional AI-Powered Recipe Generation Platform</p>
    </div>

    <div class="demo-section">
      <h2 class="demo-title">🎉 Welcome to ChefAI!</h2>
      <p class="demo-text">
        This is a professional demonstration of a modern Chrome extension built with React, TypeScript,
        and beautiful glassmorphism design. The extension showcases advanced web development skills
        including AI integration, multi-language support, and comprehensive feature implementation.
      </p>
      <button class="button" onclick="window.close()">← Back to Popup</button>
    </div>

    <div class="features-grid">
      <div class="feature-card">
        <div class="feature-icon">🤖</div>
        <div class="feature-title">AI Recipe Generation</div>
        <div class="feature-description">
          Generate personalized recipes from ingredients or titles using advanced AI technology.
        </div>
      </div>

      <div class="feature-card">
        <div class="feature-icon">🎨</div>
        <div class="feature-title">Glassmorphism UI</div>
        <div class="feature-description">
          Beautiful modern interface with frosted glass effects, gradients, and smooth animations.
        </div>
      </div>

      <div class="feature-card">
        <div class="feature-icon">🌍</div>
        <div class="feature-title">Multi-Language</div>
        <div class="feature-description">
          Support for English, Arabic, French, and Spanish with cultural recipe adaptations.
        </div>
      </div>

      <div class="feature-card">
        <div class="feature-icon">🔍</div>
        <div class="feature-title">Smart Detection</div>
        <div class="feature-description">
          Automatically detect and extract recipes from cooking websites with one click.
        </div>
      </div>

      <div class="feature-card">
        <div class="feature-icon">📱</div>
        <div class="feature-title">Social Sharing</div>
        <div class="feature-description">
          Share recipes across all major social media platforms with optimized formatting.
        </div>
      </div>

      <div class="feature-card">
        <div class="feature-icon">📊</div>
        <div class="feature-title">Analytics</div>
        <div class="feature-description">
          Track cooking patterns, ingredient preferences, and skill progression over time.
        </div>
      </div>
    </div>

    <div class="installation-guide">
      <h3>🚀 Installation Complete!</h3>
      <p>Your ChefAI extension is successfully installed and running. Here's how to use it:</p>
      <ol>
        <li><strong>Click the ChefAI icon (🍳)</strong> in your Chrome toolbar</li>
        <li><strong>Enter ingredients</strong> you have available (e.g., "chicken, rice, vegetables")</li>
        <li><strong>Click "Generate Recipe"</strong> to create an AI-powered recipe</li>
        <li><strong>Visit cooking websites</strong> to see automatic recipe detection</li>
        <li><strong>Explore settings</strong> to customize themes and preferences</li>
      </ol>
    </div>

    <div class="demo-section">
      <h3>💡 Technical Demonstration</h3>
      <p>
        This extension demonstrates professional-level skills in:
        <br><br>
        <strong>Frontend:</strong> React 18, TypeScript, Modern CSS, Responsive Design<br>
        <strong>Chrome Extension:</strong> Manifest V3, Service Workers, Content Scripts<br>
        <strong>Architecture:</strong> Modular Components, Custom Hooks, Service Layer<br>
        <strong>Features:</strong> AI Integration, Internationalization, Theme System<br>
        <strong>Quality:</strong> Type Safety, Error Handling, Performance Optimization
      </p>
    </div>
  </div>
</body>
</html>
