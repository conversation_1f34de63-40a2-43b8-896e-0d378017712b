import { useState, useCallback } from 'react';
import { Recipe, AIRecipeRequest } from '../types';
import { AIService } from '../services/AIService';
import { generateUniqueId } from '../utils/helpers';

export const useRecipeGeneration = () => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const generateRecipe = useCallback(async (request: AIRecipeRequest): Promise<Recipe | null> => {
    try {
      setIsGenerating(true);
      setError(null);

      // Get AI service instance
      const aiService = AIService.getInstance();
      
      // Generate recipe using AI
      const generatedRecipe = await aiService.generateRecipe(request);
      
      if (!generatedRecipe) {
        throw new Error('Failed to generate recipe');
      }

      // Create complete recipe object
      const recipe: Recipe = {
        id: generateUniqueId(),
        title: generatedRecipe.title,
        description: generatedRecipe.description || '',
        ingredients: generatedRecipe.ingredients.map((ingredient, index) => ({
          id: generateUniqueId(),
          name: ingredient.name,
          amount: ingredient.amount,
          unit: ingredient.unit,
          category: ingredient.category || 'Other',
          isOptional: ingredient.isOptional || false,
          substitutes: ingredient.substitutes || []
        })),
        instructions: generatedRecipe.instructions.map((instruction, index) => ({
          id: generateUniqueId(),
          step: index + 1,
          description: instruction.description,
          duration: instruction.duration,
          temperature: instruction.temperature,
          tips: instruction.tips || []
        })),
        prepTime: generatedRecipe.prepTime || 15,
        cookTime: generatedRecipe.cookTime || 30,
        servings: generatedRecipe.servings || request.servings || 4,
        difficulty: generatedRecipe.difficulty || request.difficulty || 'Medium',
        cuisine: generatedRecipe.cuisine || request.cuisine || 'International',
        tags: generatedRecipe.tags || [],
        nutritionalInfo: generatedRecipe.nutritionalInfo || {
          calories: 0,
          protein: 0,
          carbs: 0,
          fat: 0,
          fiber: 0,
          sugar: 0,
          sodium: 0,
          vitamins: {},
          minerals: {},
          allergens: []
        },
        image: generatedRecipe.image,
        source: 'AI_Generated',
        popularity: 0,
        rating: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      return recipe;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Recipe generation failed:', err);
      return null;
    } finally {
      setIsGenerating(false);
    }
  }, []);

  const generateRecipeFromIngredients = useCallback(async (ingredients: string[]): Promise<Recipe | null> => {
    return generateRecipe({ ingredients });
  }, [generateRecipe]);

  const generateRecipeFromTitle = useCallback(async (title: string): Promise<Recipe | null> => {
    return generateRecipe({ title });
  }, [generateRecipe]);

  const generateTrendingRecipe = useCallback(async (): Promise<Recipe | null> => {
    // This would integrate with trend analysis service
    return generateRecipe({ 
      title: 'Trending Recipe',
      // Add trending ingredients or themes here
    });
  }, [generateRecipe]);

  return {
    generateRecipe,
    generateRecipeFromIngredients,
    generateRecipeFromTitle,
    generateTrendingRecipe,
    isGenerating,
    error,
    clearError: () => setError(null)
  };
};
