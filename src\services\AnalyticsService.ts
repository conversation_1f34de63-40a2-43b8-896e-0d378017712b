import { Recipe, UserAnalytics, TrendData } from '../types';

interface AnalyticsEvent {
  id: string;
  type: 'recipe_generated' | 'recipe_viewed' | 'recipe_shared' | 'search_performed' | 'challenge_joined';
  timestamp: Date;
  data: any;
  userId?: string;
}

interface CookingInsight {
  id: string;
  type: 'ingredient_preference' | 'cooking_pattern' | 'skill_improvement' | 'dietary_trend';
  title: string;
  description: string;
  data: any;
  confidence: number; // 0-100
  actionable: boolean;
}

export class AnalyticsService {
  private static instance: AnalyticsService;
  private events: AnalyticsEvent[] = [];
  private insights: CookingInsight[] = [];

  private constructor() {
    this.loadAnalyticsData();
  }

  public static getInstance(): AnalyticsService {
    if (!AnalyticsService.instance) {
      AnalyticsService.instance = new AnalyticsService();
    }
    return AnalyticsService.instance;
  }

  private async loadAnalyticsData(): Promise<void> {
    try {
      const result = await chrome.storage.local.get(['analyticsEvents', 'cookingInsights']);
      this.events = result.analyticsEvents || [];
      this.insights = result.cookingInsights || [];
    } catch (error) {
      console.error('Failed to load analytics data:', error);
    }
  }

  private async saveAnalyticsData(): Promise<void> {
    try {
      await chrome.storage.local.set({
        analyticsEvents: this.events,
        cookingInsights: this.insights
      });
    } catch (error) {
      console.error('Failed to save analytics data:', error);
    }
  }

  // Event Tracking
  public async trackEvent(
    type: AnalyticsEvent['type'],
    data: any,
    userId?: string
  ): Promise<void> {
    const event: AnalyticsEvent = {
      id: `event-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type,
      timestamp: new Date(),
      data,
      userId
    };

    this.events.push(event);
    
    // Keep only last 1000 events
    if (this.events.length > 1000) {
      this.events = this.events.slice(-1000);
    }

    await this.saveAnalyticsData();
    
    // Generate insights based on new event
    await this.generateInsights();
  }

  // User Analytics
  public async getUserAnalytics(userId?: string): Promise<UserAnalytics> {
    const userEvents = userId 
      ? this.events.filter(event => event.userId === userId)
      : this.events;

    const recipeEvents = userEvents.filter(event => event.type === 'recipe_generated');
    const searchEvents = userEvents.filter(event => event.type === 'search_performed');
    const shareEvents = userEvents.filter(event => event.type === 'recipe_shared');

    // Analyze favorite ingredients
    const ingredientCounts: { [ingredient: string]: number } = {};
    recipeEvents.forEach(event => {
      if (event.data.ingredients) {
        event.data.ingredients.forEach((ingredient: string) => {
          ingredientCounts[ingredient] = (ingredientCounts[ingredient] || 0) + 1;
        });
      }
    });

    // Analyze cooking frequency by day
    const cookingFrequency: { [day: string]: number } = {
      Monday: 0, Tuesday: 0, Wednesday: 0, Thursday: 0,
      Friday: 0, Saturday: 0, Sunday: 0
    };
    
    recipeEvents.forEach(event => {
      const dayName = event.timestamp.toLocaleDateString('en-US', { weekday: 'long' });
      cookingFrequency[dayName]++;
    });

    // Analyze preferred cooking times
    const cookingTimes: number[] = [];
    recipeEvents.forEach(event => {
      if (event.data.cookingTime) {
        cookingTimes.push(event.data.cookingTime);
      }
    });

    return {
      totalRecipesGenerated: recipeEvents.length,
      favoriteIngredients: ingredientCounts,
      cookingFrequency,
      preferredCookingTimes: cookingTimes,
      successfulRecipes: recipeEvents.filter(event => event.data.rating >= 4).length,
      sharedRecipes: shareEvents.length,
      challengesParticipated: userEvents.filter(event => event.type === 'challenge_joined').length
    };
  }

  // Recipe Analytics
  public async getRecipeAnalytics(recipeId: string): Promise<any> {
    const recipeEvents = this.events.filter(event => 
      event.data.recipeId === recipeId
    );

    const views = recipeEvents.filter(event => event.type === 'recipe_viewed').length;
    const shares = recipeEvents.filter(event => event.type === 'recipe_shared').length;

    // Mock additional data for demonstration
    return {
      views,
      shares,
      saves: Math.floor(views * 0.3), // Estimate saves as 30% of views
      rating: 4.2 + Math.random() * 0.8,
      comments: Math.floor(views * 0.1),
      popularityTrend: shares > views * 0.1 ? 'increasing' : 'stable',
      demographics: {
        ageGroups: {
          '18-25': 25,
          '26-35': 35,
          '36-45': 25,
          '46-55': 10,
          '55+': 5
        },
        regions: {
          'North America': 45,
          'Europe': 30,
          'Asia': 15,
          'Other': 10
        }
      }
    };
  }

  // Insights Generation
  private async generateInsights(): Promise<void> {
    const newInsights: CookingInsight[] = [];

    // Ingredient preference insights
    const ingredientInsight = await this.generateIngredientInsights();
    if (ingredientInsight) newInsights.push(ingredientInsight);

    // Cooking pattern insights
    const patternInsight = await this.generateCookingPatternInsights();
    if (patternInsight) newInsights.push(patternInsight);

    // Skill improvement insights
    const skillInsight = await this.generateSkillInsights();
    if (skillInsight) newInsights.push(skillInsight);

    // Dietary trend insights
    const dietaryInsight = await this.generateDietaryInsights();
    if (dietaryInsight) newInsights.push(dietaryInsight);

    // Add new insights and keep only recent ones
    this.insights.push(...newInsights);
    this.insights = this.insights.slice(-20); // Keep last 20 insights

    await this.saveAnalyticsData();
  }

  private async generateIngredientInsights(): Promise<CookingInsight | null> {
    const recentRecipes = this.events
      .filter(event => event.type === 'recipe_generated')
      .slice(-10);

    if (recentRecipes.length < 5) return null;

    const ingredientCounts: { [ingredient: string]: number } = {};
    recentRecipes.forEach(event => {
      if (event.data.ingredients) {
        event.data.ingredients.forEach((ingredient: string) => {
          ingredientCounts[ingredient] = (ingredientCounts[ingredient] || 0) + 1;
        });
      }
    });

    const topIngredient = Object.entries(ingredientCounts)
      .sort(([,a], [,b]) => b - a)[0];

    if (!topIngredient || topIngredient[1] < 3) return null;

    return {
      id: `insight-ingredient-${Date.now()}`,
      type: 'ingredient_preference',
      title: `You love cooking with ${topIngredient[0]}!`,
      description: `You've used ${topIngredient[0]} in ${topIngredient[1]} of your last ${recentRecipes.length} recipes. Consider exploring new recipes that highlight this ingredient.`,
      data: { ingredient: topIngredient[0], count: topIngredient[1] },
      confidence: 85,
      actionable: true
    };
  }

  private async generateCookingPatternInsights(): Promise<CookingInsight | null> {
    const recentEvents = this.events
      .filter(event => event.type === 'recipe_generated')
      .slice(-20);

    if (recentEvents.length < 10) return null;

    // Analyze cooking times
    const cookingTimes = recentEvents
      .map(event => event.data.cookingTime)
      .filter(time => time);

    if (cookingTimes.length === 0) return null;

    const avgCookingTime = cookingTimes.reduce((a, b) => a + b, 0) / cookingTimes.length;

    let insight: string;
    let actionable = true;

    if (avgCookingTime < 30) {
      insight = `You prefer quick meals with an average cooking time of ${Math.round(avgCookingTime)} minutes. Consider meal prep strategies to save even more time.`;
    } else if (avgCookingTime > 60) {
      insight = `You enjoy elaborate cooking with an average time of ${Math.round(avgCookingTime)} minutes. You might enjoy cooking challenges or complex cuisines.`;
    } else {
      insight = `You have a balanced approach to cooking with moderate preparation times.`;
      actionable = false;
    }

    return {
      id: `insight-pattern-${Date.now()}`,
      type: 'cooking_pattern',
      title: 'Your Cooking Time Pattern',
      description: insight,
      data: { averageCookingTime: avgCookingTime },
      confidence: 75,
      actionable
    };
  }

  private async generateSkillInsights(): Promise<CookingInsight | null> {
    const recentRecipes = this.events
      .filter(event => event.type === 'recipe_generated')
      .slice(-15);

    if (recentRecipes.length < 8) return null;

    const difficulties = recentRecipes
      .map(event => event.data.difficulty)
      .filter(diff => diff);

    const difficultyScores = difficulties.map(diff => {
      switch (diff) {
        case 'Easy': return 1;
        case 'Medium': return 2;
        case 'Hard': return 3;
        default: return 1;
      }
    });

    if (difficultyScores.length === 0) return null;

    const avgDifficulty = difficultyScores.reduce((a, b) => a + b, 0) / difficultyScores.length;
    const recentAvg = difficultyScores.slice(-5).reduce((a, b) => a + b, 0) / Math.min(5, difficultyScores.length);

    if (recentAvg > avgDifficulty + 0.3) {
      return {
        id: `insight-skill-${Date.now()}`,
        type: 'skill_improvement',
        title: 'Your Cooking Skills Are Improving!',
        description: `You've been tackling more challenging recipes lately. Your recent recipes are more complex than your average, showing great progress in your culinary journey.`,
        data: { averageDifficulty: avgDifficulty, recentAverage: recentAvg },
        confidence: 80,
        actionable: true
      };
    }

    return null;
  }

  private async generateDietaryInsights(): Promise<CookingInsight | null> {
    const recentRecipes = this.events
      .filter(event => event.type === 'recipe_generated')
      .slice(-12);

    if (recentRecipes.length < 6) return null;

    const dietaryRestrictions = recentRecipes
      .flatMap(event => event.data.dietaryRestrictions || []);

    if (dietaryRestrictions.length === 0) return null;

    const restrictionCounts: { [restriction: string]: number } = {};
    dietaryRestrictions.forEach(restriction => {
      restrictionCounts[restriction] = (restrictionCounts[restriction] || 0) + 1;
    });

    const topRestriction = Object.entries(restrictionCounts)
      .sort(([,a], [,b]) => b - a)[0];

    if (topRestriction[1] >= 3) {
      return {
        id: `insight-dietary-${Date.now()}`,
        type: 'dietary_trend',
        title: `Trending Towards ${topRestriction[0]} Recipes`,
        description: `You've been exploring ${topRestriction[0]} recipes frequently. This dietary choice is becoming a pattern in your cooking habits.`,
        data: { restriction: topRestriction[0], count: topRestriction[1] },
        confidence: 70,
        actionable: true
      };
    }

    return null;
  }

  // Public Methods for Insights
  public getCookingInsights(): CookingInsight[] {
    return [...this.insights].sort((a, b) => b.confidence - a.confidence);
  }

  public async dismissInsight(insightId: string): Promise<void> {
    this.insights = this.insights.filter(insight => insight.id !== insightId);
    await this.saveAnalyticsData();
  }

  // Trend Analysis
  public async getPersonalTrends(timeframe: 'week' | 'month' | 'year' = 'month'): Promise<any> {
    const now = new Date();
    const timeframeDays = timeframe === 'week' ? 7 : timeframe === 'month' ? 30 : 365;
    const startDate = new Date(now.getTime() - timeframeDays * 24 * 60 * 60 * 1000);

    const relevantEvents = this.events.filter(event => 
      event.timestamp >= startDate
    );

    const recipeEvents = relevantEvents.filter(event => event.type === 'recipe_generated');
    
    return {
      recipesGenerated: recipeEvents.length,
      averagePerDay: recipeEvents.length / timeframeDays,
      mostActiveDay: this.getMostActiveDay(recipeEvents),
      topCuisines: this.getTopCuisines(recipeEvents),
      difficultyProgression: this.getDifficultyProgression(recipeEvents),
      ingredientTrends: this.getIngredientTrends(recipeEvents)
    };
  }

  private getMostActiveDay(events: AnalyticsEvent[]): string {
    const dayCounts: { [day: string]: number } = {};
    events.forEach(event => {
      const day = event.timestamp.toLocaleDateString('en-US', { weekday: 'long' });
      dayCounts[day] = (dayCounts[day] || 0) + 1;
    });

    return Object.entries(dayCounts)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'No data';
  }

  private getTopCuisines(events: AnalyticsEvent[]): Array<{ cuisine: string; count: number }> {
    const cuisineCounts: { [cuisine: string]: number } = {};
    events.forEach(event => {
      if (event.data.cuisine) {
        cuisineCounts[event.data.cuisine] = (cuisineCounts[event.data.cuisine] || 0) + 1;
      }
    });

    return Object.entries(cuisineCounts)
      .map(([cuisine, count]) => ({ cuisine, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);
  }

  private getDifficultyProgression(events: AnalyticsEvent[]): Array<{ date: string; difficulty: number }> {
    return events
      .filter(event => event.data.difficulty)
      .map(event => ({
        date: event.timestamp.toISOString().split('T')[0],
        difficulty: event.data.difficulty === 'Easy' ? 1 : 
                   event.data.difficulty === 'Medium' ? 2 : 3
      }))
      .sort((a, b) => a.date.localeCompare(b.date));
  }

  private getIngredientTrends(events: AnalyticsEvent[]): Array<{ ingredient: string; trend: 'rising' | 'stable' | 'declining' }> {
    // Simplified trend analysis
    const recentEvents = events.slice(-10);
    const olderEvents = events.slice(-20, -10);

    const recentIngredients = this.getIngredientCounts(recentEvents);
    const olderIngredients = this.getIngredientCounts(olderEvents);

    return Object.keys(recentIngredients).map(ingredient => {
      const recentCount = recentIngredients[ingredient] || 0;
      const olderCount = olderIngredients[ingredient] || 0;
      
      let trend: 'rising' | 'stable' | 'declining' = 'stable';
      if (recentCount > olderCount) trend = 'rising';
      else if (recentCount < olderCount) trend = 'declining';

      return { ingredient, trend };
    }).slice(0, 10);
  }

  private getIngredientCounts(events: AnalyticsEvent[]): { [ingredient: string]: number } {
    const counts: { [ingredient: string]: number } = {};
    events.forEach(event => {
      if (event.data.ingredients) {
        event.data.ingredients.forEach((ingredient: string) => {
          counts[ingredient] = (counts[ingredient] || 0) + 1;
        });
      }
    });
    return counts;
  }

  // Advanced Analytics Features
  public async getNutritionalInsights(userId?: string): Promise<any> {
    const userEvents = userId
      ? this.events.filter(event => event.userId === userId)
      : this.events;

    const recipeEvents = userEvents.filter(event => event.type === 'recipe_generated');

    // Analyze nutritional patterns
    const nutritionalData = {
      averageCalories: 0,
      macroDistribution: { protein: 0, carbs: 0, fat: 0 },
      micronutrients: {},
      dietaryPatterns: {},
      healthScore: 0
    };

    if (recipeEvents.length > 0) {
      let totalCalories = 0;
      let totalProtein = 0;
      let totalCarbs = 0;
      let totalFat = 0;

      recipeEvents.forEach(event => {
        if (event.data.nutrition) {
          totalCalories += event.data.nutrition.calories || 0;
          totalProtein += event.data.nutrition.protein || 0;
          totalCarbs += event.data.nutrition.carbs || 0;
          totalFat += event.data.nutrition.fat || 0;
        }
      });

      nutritionalData.averageCalories = Math.round(totalCalories / recipeEvents.length);
      const totalMacros = totalProtein + totalCarbs + totalFat;

      if (totalMacros > 0) {
        nutritionalData.macroDistribution = {
          protein: Math.round((totalProtein / totalMacros) * 100),
          carbs: Math.round((totalCarbs / totalMacros) * 100),
          fat: Math.round((totalFat / totalMacros) * 100)
        };
      }

      // Calculate health score based on balanced nutrition
      const proteinRatio = nutritionalData.macroDistribution.protein;
      const carbsRatio = nutritionalData.macroDistribution.carbs;
      const fatRatio = nutritionalData.macroDistribution.fat;

      // Ideal ranges: Protein 15-25%, Carbs 45-65%, Fat 20-35%
      let healthScore = 100;
      if (proteinRatio < 15 || proteinRatio > 25) healthScore -= 20;
      if (carbsRatio < 45 || carbsRatio > 65) healthScore -= 20;
      if (fatRatio < 20 || fatRatio > 35) healthScore -= 20;
      if (nutritionalData.averageCalories > 2500) healthScore -= 15;
      if (nutritionalData.averageCalories < 1200) healthScore -= 15;

      nutritionalData.healthScore = Math.max(0, healthScore);
    }

    return nutritionalData;
  }

  public async getCookingModeAnalytics(): Promise<any> {
    const modeEvents = this.events.filter(event =>
      event.type === 'recipe_generated' && event.data.cookingMode
    );

    const modeStats = {
      quickCook: { count: 0, avgTime: 0, satisfaction: 0 },
      healthyEating: { count: 0, avgCalories: 0, satisfaction: 0 },
      weekendFeasts: { count: 0, avgComplexity: 0, satisfaction: 0 },
      experimental: { count: 0, successRate: 0, satisfaction: 0 }
    };

    modeEvents.forEach(event => {
      const mode = event.data.cookingMode;
      if (modeStats[mode]) {
        modeStats[mode].count++;

        switch (mode) {
          case 'quickCook':
            modeStats[mode].avgTime += event.data.cookingTime || 0;
            break;
          case 'healthyEating':
            modeStats[mode].avgCalories += event.data.nutrition?.calories || 0;
            break;
          case 'weekendFeasts':
            modeStats[mode].avgComplexity += this.getDifficultyScore(event.data.difficulty);
            break;
          case 'experimental':
            modeStats[mode].successRate += event.data.rating >= 4 ? 1 : 0;
            break;
        }

        modeStats[mode].satisfaction += event.data.rating || 0;
      }
    });

    // Calculate averages
    Object.keys(modeStats).forEach(mode => {
      const stats = modeStats[mode];
      if (stats.count > 0) {
        stats.satisfaction = Math.round((stats.satisfaction / stats.count) * 10) / 10;

        switch (mode) {
          case 'quickCook':
            stats.avgTime = Math.round(stats.avgTime / stats.count);
            break;
          case 'healthyEating':
            stats.avgCalories = Math.round(stats.avgCalories / stats.count);
            break;
          case 'weekendFeasts':
            stats.avgComplexity = Math.round((stats.avgComplexity / stats.count) * 10) / 10;
            break;
          case 'experimental':
            stats.successRate = Math.round((stats.successRate / stats.count) * 100);
            break;
        }
      }
    });

    return modeStats;
  }

  private getDifficultyScore(difficulty: string): number {
    switch (difficulty) {
      case 'Easy': return 1;
      case 'Medium': return 2;
      case 'Hard': return 3;
      default: return 1;
    }
  }

  public async getPersonalizedRecommendations(userId?: string): Promise<any[]> {
    const userAnalytics = await this.getUserAnalytics(userId);
    const nutritionalInsights = await this.getNutritionalInsights(userId);
    const recommendations = [];

    // Ingredient-based recommendations
    const topIngredients = Object.entries(userAnalytics.favoriteIngredients)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([ingredient]) => ingredient);

    if (topIngredients.length > 0) {
      recommendations.push({
        type: 'ingredient_exploration',
        title: `Explore New ${topIngredients[0]} Recipes`,
        description: `You love cooking with ${topIngredients[0]}. Try these creative variations!`,
        priority: 'high',
        actionable: true,
        data: { ingredients: topIngredients }
      });
    }

    // Nutritional recommendations
    if (nutritionalInsights.healthScore < 70) {
      recommendations.push({
        type: 'nutritional_improvement',
        title: 'Boost Your Nutrition Score',
        description: 'Your recipes could benefit from better nutritional balance. Try more vegetables and lean proteins.',
        priority: 'medium',
        actionable: true,
        data: { currentScore: nutritionalInsights.healthScore, targetScore: 85 }
      });
    }

    // Cooking frequency recommendations
    const avgFrequency = userAnalytics.totalRecipesGenerated / 30; // per day over last 30 days
    if (avgFrequency < 0.5) {
      recommendations.push({
        type: 'cooking_frequency',
        title: 'Cook More Regularly',
        description: 'Try setting a goal to cook at least 3 times per week for better health and savings.',
        priority: 'low',
        actionable: true,
        data: { currentFrequency: avgFrequency, targetFrequency: 0.5 }
      });
    }

    // Skill development recommendations
    const recentDifficulties = this.events
      .filter(event => event.type === 'recipe_generated')
      .slice(-10)
      .map(event => this.getDifficultyScore(event.data.difficulty));

    const avgDifficulty = recentDifficulties.reduce((a, b) => a + b, 0) / recentDifficulties.length;

    if (avgDifficulty < 1.5) {
      recommendations.push({
        type: 'skill_development',
        title: 'Challenge Yourself',
        description: 'You\'ve mastered easy recipes! Try some medium difficulty dishes to expand your skills.',
        priority: 'medium',
        actionable: true,
        data: { currentLevel: avgDifficulty, suggestedLevel: 2 }
      });
    }

    return recommendations.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }

  public async getAdvancedMetrics(): Promise<any> {
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const recentEvents = this.events.filter(event => event.timestamp >= thirtyDaysAgo);
    const weeklyEvents = this.events.filter(event => event.timestamp >= sevenDaysAgo);

    return {
      engagement: {
        dailyActiveUsers: this.calculateDAU(weeklyEvents),
        sessionDuration: this.calculateAvgSessionDuration(recentEvents),
        retentionRate: this.calculateRetentionRate(recentEvents),
        featureUsage: this.calculateFeatureUsage(recentEvents)
      },
      performance: {
        recipeGenerationTime: this.calculateAvgGenerationTime(recentEvents),
        errorRate: this.calculateErrorRate(recentEvents),
        successfulGenerations: this.calculateSuccessRate(recentEvents)
      },
      content: {
        popularIngredients: this.getPopularIngredients(recentEvents),
        trendingCuisines: this.getTrendingCuisines(recentEvents),
        seasonalTrends: this.getSeasonalTrends(recentEvents)
      }
    };
  }

  private calculateDAU(events: AnalyticsEvent[]): number {
    const uniqueDays = new Set(
      events.map(event => event.timestamp.toDateString())
    );
    return uniqueDays.size;
  }

  private calculateAvgSessionDuration(events: AnalyticsEvent[]): number {
    // Mock calculation - in real implementation, track session start/end
    return Math.round(Math.random() * 300 + 120); // 2-7 minutes
  }

  private calculateRetentionRate(events: AnalyticsEvent[]): number {
    // Mock calculation - percentage of users who return
    return Math.round(Math.random() * 30 + 60); // 60-90%
  }

  private calculateFeatureUsage(events: AnalyticsEvent[]): any {
    const featureUsage = {};
    events.forEach(event => {
      featureUsage[event.type] = (featureUsage[event.type] || 0) + 1;
    });
    return featureUsage;
  }

  private calculateAvgGenerationTime(events: AnalyticsEvent[]): number {
    // Mock calculation - average time to generate recipe
    return Math.round(Math.random() * 2000 + 1000); // 1-3 seconds
  }

  private calculateErrorRate(events: AnalyticsEvent[]): number {
    // Mock calculation - percentage of failed operations
    return Math.round(Math.random() * 5 + 1); // 1-6%
  }

  private calculateSuccessRate(events: AnalyticsEvent[]): number {
    const recipeEvents = events.filter(event => event.type === 'recipe_generated');
    const successfulEvents = recipeEvents.filter(event => event.data.success !== false);
    return recipeEvents.length > 0 ? Math.round((successfulEvents.length / recipeEvents.length) * 100) : 0;
  }

  private getPopularIngredients(events: AnalyticsEvent[]): Array<{ ingredient: string; count: number }> {
    const ingredientCounts = this.getIngredientCounts(events);
    return Object.entries(ingredientCounts)
      .map(([ingredient, count]) => ({ ingredient, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
  }

  private getTrendingCuisines(events: AnalyticsEvent[]): Array<{ cuisine: string; growth: number }> {
    // Mock trending cuisines with growth percentages
    const cuisines = ['Italian', 'Asian', 'Mexican', 'Mediterranean', 'Indian'];
    return cuisines.map(cuisine => ({
      cuisine,
      growth: Math.round(Math.random() * 50 + 10) // 10-60% growth
    })).sort((a, b) => b.growth - a.growth);
  }

  private getSeasonalTrends(events: AnalyticsEvent[]): any {
    const currentMonth = new Date().getMonth();
    const seasonalIngredients = {
      0: ['citrus', 'root vegetables', 'warming spices'], // January
      1: ['citrus', 'root vegetables', 'warming spices'], // February
      2: ['spring greens', 'asparagus', 'fresh herbs'], // March
      3: ['spring greens', 'asparagus', 'fresh herbs'], // April
      4: ['spring greens', 'asparagus', 'fresh herbs'], // May
      5: ['berries', 'stone fruits', 'fresh vegetables'], // June
      6: ['berries', 'stone fruits', 'fresh vegetables'], // July
      7: ['berries', 'stone fruits', 'fresh vegetables'], // August
      8: ['apples', 'squash', 'warming spices'], // September
      9: ['apples', 'squash', 'warming spices'], // October
      10: ['root vegetables', 'warming spices', 'comfort foods'], // November
      11: ['citrus', 'root vegetables', 'warming spices'] // December
    };

    return {
      currentSeason: this.getSeason(currentMonth),
      trendingIngredients: seasonalIngredients[currentMonth] || [],
      seasonalRecipes: this.getSeasonalRecipeRecommendations(currentMonth)
    };
  }

  private getSeason(month: number): string {
    if (month >= 2 && month <= 4) return 'Spring';
    if (month >= 5 && month <= 7) return 'Summer';
    if (month >= 8 && month <= 10) return 'Fall';
    return 'Winter';
  }

  private getSeasonalRecipeRecommendations(month: number): string[] {
    const recommendations = {
      0: ['Hearty stews', 'Citrus desserts', 'Warming soups'],
      1: ['Comfort foods', 'Hot beverages', 'Root vegetable dishes'],
      2: ['Fresh salads', 'Light soups', 'Spring vegetables'],
      3: ['Easter dishes', 'Fresh herbs', 'Light proteins'],
      4: ['Grilled vegetables', 'Fresh fruit desserts', 'Light meals'],
      5: ['BBQ recipes', 'Fresh berry desserts', 'Cold soups'],
      6: ['Summer salads', 'Grilled meats', 'Frozen treats'],
      7: ['Preservation recipes', 'Fresh corn dishes', 'Light dinners'],
      8: ['Apple recipes', 'Harvest vegetables', 'Comfort foods'],
      9: ['Pumpkin dishes', 'Warming spices', 'Hearty meals'],
      10: ['Holiday preparations', 'Comfort foods', 'Warming drinks'],
      11: ['Holiday desserts', 'Festive meals', 'Warming dishes']
    };

    return recommendations[month] || [];
  }

  // Data Export
  public async exportAnalyticsData(): Promise<string> {
    const data = {
      events: this.events,
      insights: this.insights,
      exportDate: new Date().toISOString(),
      version: '1.0'
    };

    return JSON.stringify(data, null, 2);
  }

  // Privacy Controls
  public async clearAnalyticsData(): Promise<void> {
    this.events = [];
    this.insights = [];
    await chrome.storage.local.remove(['analyticsEvents', 'cookingInsights']);
  }
}
