import { SmartDeviceService } from './SmartDeviceService';
import { TimerService } from './TimerService';
import { Recipe } from '../types';

export interface VoiceCommand {
  id: string;
  phrase: string;
  action: VoiceAction;
  parameters?: { [key: string]: any };
  deviceType?: string;
  confidence: number;
}

export interface VoiceAction {
  type: 'device_control' | 'timer_control' | 'recipe_action' | 'query' | 'navigation';
  command: string;
  target?: string;
}

export interface VoiceResponse {
  text: string;
  action?: VoiceAction;
  success: boolean;
  data?: any;
}

export class VoiceControlService {
  private static instance: VoiceControlService;
  private recognition: SpeechRecognition | null = null;
  private synthesis: SpeechSynthesis | null = null;
  private isListening: boolean = false;
  private deviceService: SmartDeviceService;
  private timerService: TimerService;
  private voiceCommands: VoiceCommand[] = [];
  private listeners: Function[] = [];

  private constructor() {
    this.deviceService = SmartDeviceService.getInstance();
    this.timerService = TimerService.getInstance();
    this.initializeVoiceRecognition();
    this.initializeVoiceSynthesis();
    this.setupVoiceCommands();
  }

  public static getInstance(): VoiceControlService {
    if (!VoiceControlService.instance) {
      VoiceControlService.instance = new VoiceControlService();
    }
    return VoiceControlService.instance;
  }

  private initializeVoiceRecognition(): void {
    if ('webkitSpeechRecognition' in window) {
      this.recognition = new (window as any).webkitSpeechRecognition();
    } else if ('SpeechRecognition' in window) {
      this.recognition = new SpeechRecognition();
    }

    if (this.recognition) {
      this.recognition.continuous = false;
      this.recognition.interimResults = false;
      this.recognition.lang = 'en-US';

      this.recognition.onstart = () => {
        this.isListening = true;
        this.notifyListeners('listening_started');
      };

      this.recognition.onend = () => {
        this.isListening = false;
        this.notifyListeners('listening_stopped');
      };

      this.recognition.onresult = (event) => {
        const transcript = event.results[0][0].transcript.toLowerCase().trim();
        const confidence = event.results[0][0].confidence;
        this.processVoiceCommand(transcript, confidence);
      };

      this.recognition.onerror = (event) => {
        console.error('Speech recognition error:', event.error);
        this.notifyListeners('recognition_error', { error: event.error });
      };
    }
  }

  private initializeVoiceSynthesis(): void {
    if ('speechSynthesis' in window) {
      this.synthesis = window.speechSynthesis;
    }
  }

  private setupVoiceCommands(): void {
    this.voiceCommands = [
      // Device Control Commands
      {
        id: 'preheat_oven',
        phrase: 'preheat oven to * degrees',
        action: { type: 'device_control', command: 'preheat', target: 'oven' },
        deviceType: 'oven',
        confidence: 0.8
      },
      {
        id: 'start_timer',
        phrase: 'start timer for * minutes',
        action: { type: 'timer_control', command: 'start' },
        confidence: 0.9
      },
      {
        id: 'stop_timer',
        phrase: 'stop timer',
        action: { type: 'timer_control', command: 'stop' },
        confidence: 0.9
      },
      {
        id: 'pause_timer',
        phrase: 'pause timer',
        action: { type: 'timer_control', command: 'pause' },
        confidence: 0.9
      },
      {
        id: 'check_temperature',
        phrase: 'check temperature',
        action: { type: 'device_control', command: 'check_temperature', target: 'thermometer' },
        deviceType: 'thermometer',
        confidence: 0.8
      },
      {
        id: 'weigh_ingredients',
        phrase: 'weigh ingredients',
        action: { type: 'device_control', command: 'measure_weight', target: 'scale' },
        deviceType: 'scale',
        confidence: 0.8
      },
      {
        id: 'set_temperature',
        phrase: 'set temperature to * degrees',
        action: { type: 'device_control', command: 'set_temperature' },
        confidence: 0.8
      },
      // Recipe Commands
      {
        id: 'next_step',
        phrase: 'next step',
        action: { type: 'recipe_action', command: 'next_step' },
        confidence: 0.9
      },
      {
        id: 'previous_step',
        phrase: 'previous step',
        action: { type: 'recipe_action', command: 'previous_step' },
        confidence: 0.9
      },
      {
        id: 'repeat_step',
        phrase: 'repeat step',
        action: { type: 'recipe_action', command: 'repeat_step' },
        confidence: 0.9
      },
      {
        id: 'read_ingredients',
        phrase: 'read ingredients',
        action: { type: 'recipe_action', command: 'read_ingredients' },
        confidence: 0.9
      },
      // Query Commands
      {
        id: 'how_much_time_left',
        phrase: 'how much time left',
        action: { type: 'query', command: 'timer_status' },
        confidence: 0.9
      },
      {
        id: 'what_temperature',
        phrase: 'what temperature',
        action: { type: 'query', command: 'temperature_status' },
        confidence: 0.8
      },
      {
        id: 'device_status',
        phrase: 'device status',
        action: { type: 'query', command: 'device_status' },
        confidence: 0.8
      },
      // Navigation Commands
      {
        id: 'open_recipes',
        phrase: 'open recipes',
        action: { type: 'navigation', command: 'open_recipes' },
        confidence: 0.9
      },
      {
        id: 'open_devices',
        phrase: 'open devices',
        action: { type: 'navigation', command: 'open_devices' },
        confidence: 0.9
      },
      {
        id: 'open_timers',
        phrase: 'open timers',
        action: { type: 'navigation', command: 'open_timers' },
        confidence: 0.9
      }
    ];
  }

  public async startListening(): Promise<boolean> {
    if (!this.recognition) {
      console.error('Speech recognition not supported');
      return false;
    }

    if (this.isListening) {
      return true;
    }

    try {
      this.recognition.start();
      return true;
    } catch (error) {
      console.error('Failed to start voice recognition:', error);
      return false;
    }
  }

  public stopListening(): void {
    if (this.recognition && this.isListening) {
      this.recognition.stop();
    }
  }

  public isCurrentlyListening(): boolean {
    return this.isListening;
  }

  private async processVoiceCommand(transcript: string, confidence: number): Promise<void> {
    console.log(`Processing voice command: "${transcript}" (confidence: ${confidence})`);

    const matchedCommand = this.matchVoiceCommand(transcript);
    if (!matchedCommand) {
      const response: VoiceResponse = {
        text: "I didn't understand that command. Try saying something like 'start timer for 5 minutes' or 'preheat oven to 350 degrees'.",
        success: false
      };
      this.speak(response.text);
      this.notifyListeners('command_processed', response);
      return;
    }

    try {
      const response = await this.executeVoiceCommand(matchedCommand, transcript);
      this.speak(response.text);
      this.notifyListeners('command_processed', response);
    } catch (error) {
      console.error('Failed to execute voice command:', error);
      const errorResponse: VoiceResponse = {
        text: "Sorry, I couldn't complete that command. Please try again.",
        success: false
      };
      this.speak(errorResponse.text);
      this.notifyListeners('command_processed', errorResponse);
    }
  }

  private matchVoiceCommand(transcript: string): VoiceCommand | null {
    let bestMatch: VoiceCommand | null = null;
    let bestScore = 0;

    for (const command of this.voiceCommands) {
      const score = this.calculateCommandMatch(transcript, command.phrase);
      if (score > bestScore && score > 0.6) {
        bestMatch = command;
        bestScore = score;
      }
    }

    return bestMatch;
  }

  private calculateCommandMatch(transcript: string, pattern: string): number {
    // Simple pattern matching with wildcards (*)
    const patternParts = pattern.split('*');
    let score = 0;
    let lastIndex = 0;

    for (let i = 0; i < patternParts.length; i++) {
      const part = patternParts[i].trim();
      if (part === '') continue;

      const index = transcript.indexOf(part, lastIndex);
      if (index === -1) {
        return 0; // Pattern part not found
      }

      score += part.length;
      lastIndex = index + part.length;
    }

    return score / transcript.length;
  }

  private async executeVoiceCommand(command: VoiceCommand, transcript: string): Promise<VoiceResponse> {
    const parameters = this.extractParameters(transcript, command.phrase);

    switch (command.action.type) {
      case 'device_control':
        return await this.executeDeviceCommand(command, parameters);
      case 'timer_control':
        return await this.executeTimerCommand(command, parameters);
      case 'recipe_action':
        return await this.executeRecipeCommand(command, parameters);
      case 'query':
        return await this.executeQueryCommand(command, parameters);
      case 'navigation':
        return await this.executeNavigationCommand(command, parameters);
      default:
        return {
          text: "I don't know how to handle that type of command.",
          success: false
        };
    }
  }

  private extractParameters(transcript: string, pattern: string): { [key: string]: any } {
    const parameters: { [key: string]: any } = {};
    const patternParts = pattern.split('*');
    
    if (patternParts.length < 2) {
      return parameters;
    }

    // Extract numeric values
    const numberMatch = transcript.match(/(\d+)/);
    if (numberMatch) {
      parameters.number = parseInt(numberMatch[1]);
    }

    // Extract temperature values
    const tempMatch = transcript.match(/(\d+)\s*degrees?/);
    if (tempMatch) {
      parameters.temperature = parseInt(tempMatch[1]);
    }

    // Extract time values
    const timeMatch = transcript.match(/(\d+)\s*(minutes?|mins?|hours?|hrs?)/);
    if (timeMatch) {
      const value = parseInt(timeMatch[1]);
      const unit = timeMatch[2];
      parameters.duration = unit.includes('hour') || unit.includes('hr') ? value * 60 : value;
    }

    return parameters;
  }

  private async executeDeviceCommand(command: VoiceCommand, parameters: any): Promise<VoiceResponse> {
    const devices = this.deviceService.getDevicesByType(command.deviceType as any);
    if (devices.length === 0) {
      return {
        text: `No ${command.deviceType} devices found.`,
        success: false
      };
    }

    const device = devices[0]; // Use first available device
    
    try {
      switch (command.action.command) {
        case 'preheat':
          const temp = parameters.temperature || 350;
          await this.deviceService.executeDeviceAction(device.id, {
            type: 'preheat',
            parameters: { temperature: temp }
          });
          return {
            text: `Preheating ${device.name} to ${temp} degrees.`,
            success: true,
            action: command.action
          };

        case 'set_temperature':
          const setTemp = parameters.temperature || 350;
          await this.deviceService.executeDeviceAction(device.id, {
            type: 'set_temperature',
            parameters: { temperature: setTemp }
          });
          return {
            text: `Setting ${device.name} temperature to ${setTemp} degrees.`,
            success: true,
            action: command.action
          };

        case 'check_temperature':
          await this.deviceService.executeDeviceAction(device.id, {
            type: 'check_temperature',
            parameters: {}
          });
          return {
            text: `Checking temperature with ${device.name}.`,
            success: true,
            action: command.action
          };

        case 'measure_weight':
          await this.deviceService.executeDeviceAction(device.id, {
            type: 'measure_weight',
            parameters: {}
          });
          return {
            text: `Measuring weight with ${device.name}.`,
            success: true,
            action: command.action
          };

        default:
          return {
            text: `I don't know how to ${command.action.command} the ${device.name}.`,
            success: false
          };
      }
    } catch (error) {
      return {
        text: `Failed to control ${device.name}. Please check the device connection.`,
        success: false
      };
    }
  }

  private async executeTimerCommand(command: VoiceCommand, parameters: any): Promise<VoiceResponse> {
    const activeTimers = this.timerService.getActiveTimers();

    switch (command.action.command) {
      case 'start':
        const duration = parameters.duration || 5; // Default 5 minutes
        const timerId = await this.timerService.createTimer({
          name: `Voice Timer - ${duration} minutes`,
          duration: duration * 60,
          type: 'cooking',
          notificationSettings: {
            enabled: true,
            sound: true,
            vibration: true,
            intervals: [60, 30, 10]
          }
        });
        await this.timerService.startTimer(timerId);
        return {
          text: `Started a ${duration} minute timer.`,
          success: true,
          action: command.action,
          data: { timerId, duration }
        };

      case 'stop':
        if (activeTimers.length === 0) {
          return {
            text: "No active timers to stop.",
            success: false
          };
        }
        await this.timerService.stopTimer(activeTimers[0].id);
        return {
          text: `Stopped the timer.`,
          success: true,
          action: command.action
        };

      case 'pause':
        if (activeTimers.length === 0) {
          return {
            text: "No active timers to pause.",
            success: false
          };
        }
        await this.timerService.pauseTimer(activeTimers[0].id);
        return {
          text: `Paused the timer.`,
          success: true,
          action: command.action
        };

      default:
        return {
          text: `I don't know how to ${command.action.command} timers.`,
          success: false
        };
    }
  }

  private async executeRecipeCommand(command: VoiceCommand, parameters: any): Promise<VoiceResponse> {
    // This would integrate with the recipe service
    switch (command.action.command) {
      case 'next_step':
        return {
          text: "Moving to the next recipe step.",
          success: true,
          action: command.action
        };

      case 'previous_step':
        return {
          text: "Going back to the previous step.",
          success: true,
          action: command.action
        };

      case 'repeat_step':
        return {
          text: "Repeating the current step.",
          success: true,
          action: command.action
        };

      case 'read_ingredients':
        return {
          text: "Here are the ingredients for this recipe.",
          success: true,
          action: command.action
        };

      default:
        return {
          text: `I don't know how to ${command.action.command} recipes.`,
          success: false
        };
    }
  }

  private async executeQueryCommand(command: VoiceCommand, parameters: any): Promise<VoiceResponse> {
    switch (command.action.command) {
      case 'timer_status':
        const activeTimers = this.timerService.getActiveTimers();
        if (activeTimers.length === 0) {
          return {
            text: "No active timers.",
            success: true,
            action: command.action
          };
        }
        const timer = activeTimers[0];
        const minutes = Math.floor(timer.remainingTime / 60);
        const seconds = timer.remainingTime % 60;
        return {
          text: `${minutes} minutes and ${seconds} seconds remaining.`,
          success: true,
          action: command.action,
          data: { remainingTime: timer.remainingTime }
        };

      case 'temperature_status':
        const thermometers = this.deviceService.getDevicesByType('thermometer');
        if (thermometers.length === 0) {
          return {
            text: "No thermometer devices available.",
            success: false
          };
        }
        return {
          text: "Checking current temperature.",
          success: true,
          action: command.action
        };

      case 'device_status':
        const connectedDevices = this.deviceService.getDevices().filter(d => d.status === 'connected');
        return {
          text: `${connectedDevices.length} devices are currently connected.`,
          success: true,
          action: command.action,
          data: { connectedCount: connectedDevices.length }
        };

      default:
        return {
          text: `I don't know how to answer that question.`,
          success: false
        };
    }
  }

  private async executeNavigationCommand(command: VoiceCommand, parameters: any): Promise<VoiceResponse> {
    return {
      text: `Opening ${command.action.command.replace('open_', '').replace('_', ' ')}.`,
      success: true,
      action: command.action
    };
  }

  public speak(text: string, options: SpeechSynthesisUtterance = new SpeechSynthesisUtterance()): void {
    if (!this.synthesis) {
      console.warn('Speech synthesis not supported');
      return;
    }

    // Cancel any ongoing speech
    this.synthesis.cancel();

    const utterance = new SpeechSynthesisUtterance(text);
    utterance.rate = options.rate || 1;
    utterance.pitch = options.pitch || 1;
    utterance.volume = options.volume || 1;
    utterance.lang = options.lang || 'en-US';

    utterance.onstart = () => {
      this.notifyListeners('speech_started');
    };

    utterance.onend = () => {
      this.notifyListeners('speech_ended');
    };

    utterance.onerror = (event) => {
      console.error('Speech synthesis error:', event.error);
      this.notifyListeners('speech_error', { error: event.error });
    };

    this.synthesis.speak(utterance);
  }

  public stopSpeaking(): void {
    if (this.synthesis) {
      this.synthesis.cancel();
    }
  }

  public getAvailableCommands(): VoiceCommand[] {
    return [...this.voiceCommands];
  }

  public addVoiceCommand(command: VoiceCommand): void {
    this.voiceCommands.push(command);
  }

  public removeVoiceCommand(commandId: string): void {
    this.voiceCommands = this.voiceCommands.filter(cmd => cmd.id !== commandId);
  }

  public addListener(callback: Function): void {
    this.listeners.push(callback);
  }

  public removeListener(callback: Function): void {
    const index = this.listeners.indexOf(callback);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  private notifyListeners(event: string, data?: any): void {
    this.listeners.forEach(callback => {
      try {
        callback(event, data);
      } catch (error) {
        console.error('Error in voice control listener:', error);
      }
    });
  }

  public async requestMicrophonePermission(): Promise<boolean> {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach(track => track.stop());
      return true;
    } catch (error) {
      console.error('Microphone permission denied:', error);
      return false;
    }
  }
}
