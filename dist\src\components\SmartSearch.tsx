import React, { useState, useEffect, useCallback } from 'react';
import { SearchResult, SearchSource, TrendData } from '../types';
import { SearchService } from '../services/SearchService';
import { useI18n } from '../hooks/useI18n';
import { debounce } from '../utils/helpers';

interface SmartSearchProps {
  onRecipeSelect?: (result: SearchResult) => void;
}

export const SmartSearch: React.FC<SmartSearchProps> = ({ onRecipeSelect }) => {
  const { t } = useI18n();
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [trending, setTrending] = useState<TrendData[]>([]);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [popularSearches, setPopularSearches] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedSources, setSelectedSources] = useState<SearchSource[]>(['Google', 'YouTube', 'Pinterest']);
  const [filters, setFilters] = useState({
    cuisine: '',
    difficulty: '',
    cookingTime: 0,
    dietary: [] as string[]
  });
  const [activeTab, setActiveTab] = useState<'search' | 'trending' | 'history'>('search');

  const searchService = SearchService.getInstance();

  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      const [trendingData, historyData, popularData] = await Promise.all([
        searchService.getTrendingRecipes(),
        Promise.resolve(searchService.getSearchHistory()),
        searchService.getPopularSearches()
      ]);
      
      setTrending(trendingData);
      setSearchHistory(historyData);
      setPopularSearches(popularData);
    } catch (error) {
      console.error('Failed to load initial data:', error);
    }
  };

  const debouncedSearch = useCallback(
    debounce(async (searchQuery: string) => {
      if (searchQuery.trim().length < 2) {
        setResults([]);
        return;
      }

      try {
        setIsLoading(true);
        const searchResults = await searchService.searchRecipes(
          searchQuery,
          selectedSources,
          filters
        );
        setResults(searchResults);
      } catch (error) {
        console.error('Search failed:', error);
        setResults([]);
      } finally {
        setIsLoading(false);
      }
    }, 500),
    [selectedSources, filters]
  );

  const debouncedSuggestions = useCallback(
    debounce(async (partial: string) => {
      if (partial.trim().length > 0) {
        const suggestionResults = await searchService.getSearchSuggestions(partial);
        setSuggestions(suggestionResults);
      } else {
        setSuggestions([]);
      }
    }, 300),
    []
  );

  useEffect(() => {
    debouncedSearch(query);
  }, [query, debouncedSearch]);

  useEffect(() => {
    debouncedSuggestions(query);
  }, [query, debouncedSuggestions]);

  const handleSearch = (searchQuery: string) => {
    setQuery(searchQuery);
    setSuggestions([]);
  };

  const handleSuggestionClick = (suggestion: string) => {
    handleSearch(suggestion);
  };

  const handleSourceToggle = (source: SearchSource) => {
    setSelectedSources(prev => 
      prev.includes(source)
        ? prev.filter(s => s !== source)
        : [...prev, source]
    );
  };

  const handleFilterChange = (filterType: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  const handleResultClick = (result: SearchResult) => {
    if (onRecipeSelect) {
      onRecipeSelect(result);
    } else {
      window.open(result.url, '_blank');
    }
  };

  const handleTrendingClick = (trend: TrendData) => {
    handleSearch(trend.keyword);
    setActiveTab('search');
  };

  const clearHistory = async () => {
    await searchService.clearSearchHistory();
    setSearchHistory([]);
  };

  const getSourceIcon = (source: SearchSource) => {
    const icons = {
      Google: '🔍',
      YouTube: '📺',
      Pinterest: '📌',
      Facebook: '📘',
      Instagram: '📷',
      Reddit: '🤖',
      TikTok: '🎵',
      Food_Blog: '📝'
    };
    return icons[source] || '🌐';
  };

  return (
    <div className="smart-search">
      {/* Search Header */}
      <div className="search-header glass-card">
        <h2>{t('search.title')}</h2>
        <p>{t('search.description')}</p>
      </div>

      {/* Search Input */}
      <div className="search-input-container glass-card">
        <div className="search-input-wrapper">
          <input
            type="text"
            className="search-input glass-input"
            placeholder={t('search.placeholder')}
            value={query}
            onChange={(e) => setQuery(e.target.value)}
          />
          <button className="search-button glass-button-primary">
            🔍
          </button>
        </div>

        {/* Search Suggestions */}
        {suggestions.length > 0 && (
          <div className="search-suggestions">
            {suggestions.map((suggestion, index) => (
              <button
                key={index}
                className="suggestion-item"
                onClick={() => handleSuggestionClick(suggestion)}
              >
                {suggestion}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Search Filters */}
      <div className="search-filters glass-card">
        <h3>{t('search.filters')}</h3>
        
        {/* Source Selection */}
        <div className="filter-group">
          <label>{t('search.sources')}</label>
          <div className="source-buttons">
            {(['Google', 'YouTube', 'Pinterest', 'Facebook', 'Instagram', 'Reddit'] as SearchSource[]).map(source => (
              <button
                key={source}
                className={`source-button ${selectedSources.includes(source) ? 'active' : ''}`}
                onClick={() => handleSourceToggle(source)}
              >
                <span className="source-icon">{getSourceIcon(source)}</span>
                <span className="source-name">{source}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Other Filters */}
        <div className="filter-row">
          <div className="filter-item">
            <label>{t('search.cuisine')}</label>
            <select
              className="glass-input"
              value={filters.cuisine}
              onChange={(e) => handleFilterChange('cuisine', e.target.value)}
            >
              <option value="">{t('search.any_cuisine')}</option>
              <option value="Italian">Italian</option>
              <option value="Chinese">Chinese</option>
              <option value="Mexican">Mexican</option>
              <option value="Indian">Indian</option>
              <option value="French">French</option>
            </select>
          </div>

          <div className="filter-item">
            <label>{t('search.difficulty')}</label>
            <select
              className="glass-input"
              value={filters.difficulty}
              onChange={(e) => handleFilterChange('difficulty', e.target.value)}
            >
              <option value="">{t('search.any_difficulty')}</option>
              <option value="Easy">Easy</option>
              <option value="Medium">Medium</option>
              <option value="Hard">Hard</option>
            </select>
          </div>

          <div className="filter-item">
            <label>{t('search.max_time')} ({filters.cookingTime || 'Any'} min)</label>
            <input
              type="range"
              min="0"
              max="180"
              step="15"
              value={filters.cookingTime}
              onChange={(e) => handleFilterChange('cookingTime', Number(e.target.value))}
              className="glass-slider"
            />
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="search-tabs">
        <button
          className={`tab-button ${activeTab === 'search' ? 'active' : ''}`}
          onClick={() => setActiveTab('search')}
        >
          🔍 {t('search.search_results')} {results.length > 0 && `(${results.length})`}
        </button>
        <button
          className={`tab-button ${activeTab === 'trending' ? 'active' : ''}`}
          onClick={() => setActiveTab('trending')}
        >
          🔥 {t('search.trending')} ({trending.length})
        </button>
        <button
          className={`tab-button ${activeTab === 'history' ? 'active' : ''}`}
          onClick={() => setActiveTab('history')}
        >
          📚 {t('search.history')} ({searchHistory.length})
        </button>
      </div>

      {/* Content */}
      <div className="search-content">
        {activeTab === 'search' && (
          <div className="search-results">
            {isLoading && (
              <div className="loading-state">
                <div className="glass-spinner"></div>
                <p>{t('loading.searching')}</p>
              </div>
            )}

            {!isLoading && query && results.length === 0 && (
              <div className="no-results">
                <div className="no-results-icon">🔍</div>
                <h3>{t('search.no_results')}</h3>
                <p>{t('search.no_results_description')}</p>
              </div>
            )}

            {!query && !isLoading && (
              <div className="search-suggestions-home">
                <h3>{t('search.popular_searches')}</h3>
                <div className="popular-searches">
                  {popularSearches.map((search, index) => (
                    <button
                      key={index}
                      className="popular-search-item glass-card-sm"
                      onClick={() => handleSearch(search)}
                    >
                      {search}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {results.map((result) => (
              <div
                key={result.id}
                className="search-result-item glass-card"
                onClick={() => handleResultClick(result)}
              >
                <div className="result-thumbnail">
                  {result.thumbnail ? (
                    <img src={result.thumbnail} alt={result.title} />
                  ) : (
                    <div className="thumbnail-placeholder">🍳</div>
                  )}
                </div>
                
                <div className="result-content">
                  <div className="result-header">
                    <h3>{result.title}</h3>
                    <div className="result-source">
                      <span className="source-icon">{getSourceIcon(result.source)}</span>
                      <span>{result.source}</span>
                    </div>
                  </div>
                  
                  <p className="result-description">{result.description}</p>
                  
                  <div className="result-meta">
                    <div className="result-stats">
                      {result.rating && (
                        <span>⭐ {result.rating.toFixed(1)}</span>
                      )}
                      <span>📈 {result.popularity}%</span>
                    </div>
                    
                    <div className="result-tags">
                      {result.tags.slice(0, 3).map((tag, index) => (
                        <span key={index} className="result-tag">{tag}</span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {activeTab === 'trending' && (
          <div className="trending-content">
            <div className="trending-header">
              <h3>{t('search.trending_recipes')}</h3>
              <p>{t('search.trending_description')}</p>
            </div>
            
            <div className="trending-list">
              {trending.map((trend, index) => (
                <div
                  key={trend.id}
                  className="trending-item glass-card"
                  onClick={() => handleTrendingClick(trend)}
                >
                  <div className="trending-rank">#{index + 1}</div>
                  <div className="trending-content">
                    <h4>{trend.keyword}</h4>
                    <div className="trending-stats">
                      <span className="popularity">📈 {trend.popularity}%</span>
                      <span className="growth">🚀 +{trend.growth}%</span>
                    </div>
                    <div className="trending-sources">
                      {trend.sources.map((source, idx) => (
                        <span key={idx} className="trending-source">
                          {getSourceIcon(source as SearchSource)} {source}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'history' && (
          <div className="history-content">
            <div className="history-header">
              <h3>{t('search.search_history')}</h3>
              {searchHistory.length > 0 && (
                <button className="clear-history-button glass-button" onClick={clearHistory}>
                  {t('search.clear_history')}
                </button>
              )}
            </div>
            
            {searchHistory.length === 0 ? (
              <div className="empty-history">
                <div className="empty-icon">📚</div>
                <h3>{t('search.no_history')}</h3>
                <p>{t('search.no_history_description')}</p>
              </div>
            ) : (
              <div className="history-list">
                {searchHistory.map((historyItem, index) => (
                  <button
                    key={index}
                    className="history-item glass-card-sm"
                    onClick={() => handleSearch(historyItem)}
                  >
                    <span className="history-icon">🔍</span>
                    <span className="history-text">{historyItem}</span>
                  </button>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
