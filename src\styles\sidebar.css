/* Sidebar Interface Styles */
.sidebar-interface {
  position: fixed;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 10000;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.sidebar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.sidebar-content {
  position: absolute;
  top: 0;
  right: 0;
  width: 420px;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  backdrop-filter: blur(20px);
  border-left: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: -10px 0 30px rgba(0, 0, 0, 0.3);
  overflow-y: auto;
  color: white;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.sidebar-header h2 {
  margin: 0;
  font-size: 1.5em;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 2em;
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: rotate(90deg);
}

/* Sidebar Tabs */
.sidebar-tabs {
  display: flex;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
}

.tab {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 12px 8px;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85em;
  font-weight: 500;
}

.tab.active {
  color: white;
  background: rgba(255, 255, 255, 0.1);
  border-bottom: 2px solid #FFD700;
}

.tab:hover {
  color: white;
  background: rgba(255, 255, 255, 0.05);
}

.tab-icon {
  font-size: 1.2em;
}

/* Tab Content */
.tab-content {
  padding: 20px;
  height: calc(100vh - 140px);
  overflow-y: auto;
}

/* Generate Tab Styles */
.form-section h3 {
  margin: 0 0 20px;
  font-size: 1.3em;
  display: flex;
  align-items: center;
  gap: 8px;
}

.input-group {
  margin-bottom: 20px;
}

.input-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  font-size: 0.9em;
}

.glass-input,
.glass-select,
.glass-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 14px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
}

.glass-input::placeholder,
.glass-textarea::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.glass-input:focus,
.glass-select:focus,
.glass-textarea:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.15);
}

.glass-select option {
  background: #333;
  color: white;
}

.glass-range {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.2);
  outline: none;
  -webkit-appearance: none;
}

.glass-range::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #FFD700;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.glass-range::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #FFD700;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

/* Ingredient Input */
.ingredient-input {
  display: flex;
  gap: 10px;
  align-items: center;
}

.ingredient-input input {
  flex: 1;
}

.add-btn {
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.add-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.add-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.ingredients-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}

.ingredient-tag {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  font-size: 0.9em;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.remove-ingredient {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 1.2em;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.remove-ingredient:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Parameters Grid */
.parameters-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.parameters-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

/* Dietary Options */
.dietary-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9em;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.checkbox-label:hover {
  background: rgba(255, 255, 255, 0.1);
}

.checkbox-label input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #FFD700;
}

/* Buttons */
.glass-button {
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.glass-button:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.glass-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.glass-button.primary {
  background: linear-gradient(45deg, #4CAF50, #45a049);
  border-color: #4CAF50;
}

.glass-button.primary:hover:not(:disabled) {
  background: linear-gradient(45deg, #45a049, #3d8b40);
}

.generate-btn {
  width: 100%;
  padding: 16px;
  font-size: 1.1em;
  margin-top: 20px;
}

.btn-icon {
  font-size: 1.2em;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.warning-message {
  background: rgba(255, 152, 0, 0.2);
  border: 1px solid rgba(255, 152, 0, 0.5);
  border-radius: 8px;
  padding: 12px;
  margin-top: 15px;
  font-size: 0.9em;
  text-align: center;
}

/* Settings Tab */
.llm-settings h3 {
  margin: 0 0 20px;
  font-size: 1.3em;
  display: flex;
  align-items: center;
  gap: 8px;
}

.settings-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.provider-description {
  display: block;
  margin-top: 5px;
  font-size: 0.8em;
  opacity: 0.8;
  line-height: 1.4;
}

.settings-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.test-btn,
.save-btn {
  flex: 1;
}

.test-result {
  padding: 12px;
  border-radius: 8px;
  margin-top: 15px;
  font-size: 0.9em;
  text-align: center;
}

.test-result.success {
  background: rgba(76, 175, 80, 0.2);
  border: 1px solid rgba(76, 175, 80, 0.5);
}

.test-result.error {
  background: rgba(244, 67, 54, 0.2);
  border: 1px solid rgba(244, 67, 54, 0.5);
}

/* History Tab */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  opacity: 0.8;
}

.empty-state p {
  margin: 10px 0;
  line-height: 1.5;
}

.recipes-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.recipe-item {
  padding: 15px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.recipe-item h4 {
  margin: 0 0 8px;
  font-size: 1.1em;
}

.recipe-item p {
  margin: 0 0 12px;
  opacity: 0.9;
  font-size: 0.9em;
  line-height: 1.4;
}

.recipe-meta {
  display: flex;
  gap: 12px;
  margin: 12px 0;
  font-size: 0.8em;
  opacity: 0.8;
}

.view-recipe-btn {
  width: 100%;
  margin-top: 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar-content {
    width: 100%;
  }
  
  .parameters-grid,
  .parameters-row {
    grid-template-columns: 1fr;
  }
  
  .dietary-options {
    grid-template-columns: 1fr;
  }
  
  .settings-actions {
    flex-direction: column;
  }
  
  .ingredient-input {
    flex-direction: column;
    align-items: stretch;
  }
  
  .tab {
    font-size: 0.8em;
    padding: 10px 6px;
  }
}

/* Scrollbar Styling */
.sidebar-content::-webkit-scrollbar,
.tab-content::-webkit-scrollbar {
  width: 6px;
}

.sidebar-content::-webkit-scrollbar-track,
.tab-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.sidebar-content::-webkit-scrollbar-thumb,
.tab-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover,
.tab-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
