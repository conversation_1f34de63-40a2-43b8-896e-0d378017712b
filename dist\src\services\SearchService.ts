import { SearchResult, SearchSource, TrendData, Recipe } from '../types';

export class SearchService {
  private static instance: SearchService;
  private searchHistory: string[] = [];
  private trendingCache: Map<string, { data: any; timestamp: number }> = new Map();
  private readonly CACHE_DURATION = 30 * 60 * 1000; // 30 minutes

  private constructor() {
    this.loadSearchHistory();
  }

  public static getInstance(): SearchService {
    if (!SearchService.instance) {
      SearchService.instance = new SearchService();
    }
    return SearchService.instance;
  }

  // Main Search Function
  public async searchRecipes(
    query: string,
    sources: SearchSource[] = ['Google', 'YouTube', 'Pinterest'],
    filters?: {
      cuisine?: string;
      difficulty?: string;
      cookingTime?: number;
      dietary?: string[];
    }
  ): Promise<SearchResult[]> {
    try {
      // Add to search history
      await this.addToSearchHistory(query);

      // Combine results from multiple sources
      const searchPromises = sources.map(source => this.searchBySource(query, source, filters));
      const results = await Promise.allSettled(searchPromises);

      // Flatten and deduplicate results
      const allResults: SearchResult[] = [];
      results.forEach(result => {
        if (result.status === 'fulfilled' && result.value) {
          allResults.push(...result.value);
        }
      });

      // Sort by popularity and relevance
      return this.rankSearchResults(allResults, query);
    } catch (error) {
      console.error('Search failed:', error);
      return [];
    }
  }

  // Source-specific search methods
  private async searchBySource(
    query: string,
    source: SearchSource,
    filters?: any
  ): Promise<SearchResult[]> {
    switch (source) {
      case 'Google':
        return this.searchGoogle(query, filters);
      case 'YouTube':
        return this.searchYouTube(query, filters);
      case 'Pinterest':
        return this.searchPinterest(query, filters);
      case 'Facebook':
        return this.searchFacebook(query, filters);
      case 'Instagram':
        return this.searchInstagram(query, filters);
      case 'Reddit':
        return this.searchReddit(query, filters);
      case 'TikTok':
        return this.searchTikTok(query, filters);
      default:
        return [];
    }
  }

  private async searchGoogle(query: string, filters?: any): Promise<SearchResult[]> {
    // Mock Google search results
    return [
      {
        id: 'google-1',
        title: `${query} - Best Recipe Collection`,
        description: `Discover the most popular ${query} recipes from top cooking websites`,
        url: `https://example.com/recipes/${query.replace(/\s+/g, '-')}`,
        source: 'Google',
        thumbnail: 'https://example.com/thumb1.jpg',
        rating: 4.5,
        popularity: 95,
        tags: [query, 'popular', 'trending'],
        createdAt: new Date()
      },
      {
        id: 'google-2',
        title: `Easy ${query} Recipe - 30 Minutes`,
        description: `Quick and delicious ${query} recipe that anyone can make`,
        url: `https://example.com/easy-${query.replace(/\s+/g, '-')}`,
        source: 'Google',
        thumbnail: 'https://example.com/thumb2.jpg',
        rating: 4.3,
        popularity: 87,
        tags: [query, 'easy', 'quick'],
        createdAt: new Date()
      }
    ];
  }

  private async searchYouTube(query: string, filters?: any): Promise<SearchResult[]> {
    // Mock YouTube search results
    return [
      {
        id: 'youtube-1',
        title: `How to Make ${query} - Professional Chef Tutorial`,
        description: `Step-by-step video guide for making perfect ${query}`,
        url: `https://youtube.com/watch?v=example1`,
        source: 'YouTube',
        thumbnail: 'https://img.youtube.com/vi/example1/maxresdefault.jpg',
        rating: 4.8,
        popularity: 92,
        tags: [query, 'tutorial', 'chef', 'video'],
        createdAt: new Date()
      },
      {
        id: 'youtube-2',
        title: `${query} Recipe - 5 Minute Cooking`,
        description: `Quick ${query} recipe for busy people`,
        url: `https://youtube.com/watch?v=example2`,
        source: 'YouTube',
        thumbnail: 'https://img.youtube.com/vi/example2/maxresdefault.jpg',
        rating: 4.2,
        popularity: 78,
        tags: [query, 'quick', 'easy', 'video'],
        createdAt: new Date()
      }
    ];
  }

  private async searchPinterest(query: string, filters?: any): Promise<SearchResult[]> {
    // Mock Pinterest search results
    return [
      {
        id: 'pinterest-1',
        title: `Beautiful ${query} Recipe Ideas`,
        description: `Stunning ${query} recipes with gorgeous photos`,
        url: `https://pinterest.com/pin/example1`,
        source: 'Pinterest',
        thumbnail: 'https://i.pinimg.com/564x/example1.jpg',
        rating: 4.6,
        popularity: 89,
        tags: [query, 'beautiful', 'inspiration', 'photos'],
        createdAt: new Date()
      }
    ];
  }

  private async searchFacebook(query: string, filters?: any): Promise<SearchResult[]> {
    // Mock Facebook search results
    return [
      {
        id: 'facebook-1',
        title: `${query} Recipe - Cooking Group Share`,
        description: `Popular ${query} recipe shared in cooking communities`,
        url: `https://facebook.com/groups/cooking/posts/example1`,
        source: 'Facebook',
        thumbnail: 'https://example.com/fb-thumb1.jpg',
        rating: 4.4,
        popularity: 85,
        tags: [query, 'community', 'shared', 'popular'],
        createdAt: new Date()
      }
    ];
  }

  private async searchInstagram(query: string, filters?: any): Promise<SearchResult[]> {
    // Mock Instagram search results
    return [
      {
        id: 'instagram-1',
        title: `#${query.replace(/\s+/g, '')} - Viral Recipe`,
        description: `Trending ${query} recipe from food influencers`,
        url: `https://instagram.com/p/example1`,
        source: 'Instagram',
        thumbnail: 'https://example.com/ig-thumb1.jpg',
        rating: 4.7,
        popularity: 94,
        tags: [query, 'viral', 'influencer', 'trending'],
        createdAt: new Date()
      }
    ];
  }

  private async searchReddit(query: string, filters?: any): Promise<SearchResult[]> {
    // Mock Reddit search results
    return [
      {
        id: 'reddit-1',
        title: `Best ${query} Recipe - r/Cooking`,
        description: `Community-approved ${query} recipe with tips and variations`,
        url: `https://reddit.com/r/Cooking/comments/example1`,
        source: 'Reddit',
        thumbnail: 'https://example.com/reddit-thumb1.jpg',
        rating: 4.5,
        popularity: 82,
        tags: [query, 'community', 'tips', 'discussion'],
        createdAt: new Date()
      }
    ];
  }

  private async searchTikTok(query: string, filters?: any): Promise<SearchResult[]> {
    // Mock TikTok search results
    return [
      {
        id: 'tiktok-1',
        title: `${query} Recipe Hack - Viral TikTok`,
        description: `Creative ${query} recipe that went viral on TikTok`,
        url: `https://tiktok.com/@user/video/example1`,
        source: 'TikTok',
        thumbnail: 'https://example.com/tiktok-thumb1.jpg',
        rating: 4.3,
        popularity: 96,
        tags: [query, 'viral', 'hack', 'creative'],
        createdAt: new Date()
      }
    ];
  }

  // Trending Analysis
  public async getTrendingRecipes(
    timeframe: 'daily' | 'weekly' | 'monthly' = 'weekly',
    region: string = 'global'
  ): Promise<TrendData[]> {
    const cacheKey = `trending-${timeframe}-${region}`;
    
    // Check cache first
    if (this.isCacheValid(cacheKey)) {
      return this.trendingCache.get(cacheKey)!.data;
    }

    try {
      // Mock trending data
      const trendingData: TrendData[] = [
        {
          id: 'trend-1',
          keyword: 'air fryer recipes',
          popularity: 95,
          growth: 25,
          region,
          timeframe,
          relatedKeywords: ['air fryer chicken', 'air fryer vegetables', 'healthy air fryer'],
          sources: ['YouTube', 'Pinterest', 'TikTok']
        },
        {
          id: 'trend-2',
          keyword: 'sourdough bread',
          popularity: 88,
          growth: 15,
          region,
          timeframe,
          relatedKeywords: ['sourdough starter', 'artisan bread', 'homemade bread'],
          sources: ['Instagram', 'YouTube', 'Pinterest']
        },
        {
          id: 'trend-3',
          keyword: 'plant-based meals',
          popularity: 82,
          growth: 35,
          region,
          timeframe,
          relatedKeywords: ['vegan recipes', 'vegetarian meals', 'plant protein'],
          sources: ['Instagram', 'TikTok', 'Pinterest']
        },
        {
          id: 'trend-4',
          keyword: 'korean cuisine',
          popularity: 79,
          growth: 42,
          region,
          timeframe,
          relatedKeywords: ['kimchi', 'bulgogi', 'korean bbq', 'bibimbap'],
          sources: ['TikTok', 'YouTube', 'Instagram']
        },
        {
          id: 'trend-5',
          keyword: 'meal prep',
          popularity: 76,
          growth: 18,
          region,
          timeframe,
          relatedKeywords: ['batch cooking', 'healthy meal prep', 'weekly meal prep'],
          sources: ['Pinterest', 'YouTube', 'Instagram']
        }
      ];

      // Cache the results
      this.trendingCache.set(cacheKey, {
        data: trendingData,
        timestamp: Date.now()
      });

      return trendingData;
    } catch (error) {
      console.error('Failed to get trending recipes:', error);
      return [];
    }
  }

  // Search History Management
  private async loadSearchHistory(): Promise<void> {
    try {
      const result = await chrome.storage.local.get(['searchHistory']);
      this.searchHistory = result.searchHistory || [];
    } catch (error) {
      console.error('Failed to load search history:', error);
    }
  }

  private async addToSearchHistory(query: string): Promise<void> {
    try {
      // Remove if already exists and add to beginning
      this.searchHistory = this.searchHistory.filter(item => item !== query);
      this.searchHistory.unshift(query);
      
      // Keep only last 50 searches
      this.searchHistory = this.searchHistory.slice(0, 50);
      
      await chrome.storage.local.set({ searchHistory: this.searchHistory });
    } catch (error) {
      console.error('Failed to save search history:', error);
    }
  }

  public getSearchHistory(): string[] {
    return [...this.searchHistory];
  }

  public async clearSearchHistory(): Promise<void> {
    try {
      this.searchHistory = [];
      await chrome.storage.local.set({ searchHistory: [] });
    } catch (error) {
      console.error('Failed to clear search history:', error);
    }
  }

  // Utility Methods
  private rankSearchResults(results: SearchResult[], query: string): SearchResult[] {
    return results.sort((a, b) => {
      // Calculate relevance score
      const aScore = this.calculateRelevanceScore(a, query);
      const bScore = this.calculateRelevanceScore(b, query);
      
      return bScore - aScore;
    });
  }

  private calculateRelevanceScore(result: SearchResult, query: string): number {
    let score = 0;
    
    // Title relevance
    if (result.title.toLowerCase().includes(query.toLowerCase())) {
      score += 10;
    }
    
    // Description relevance
    if (result.description.toLowerCase().includes(query.toLowerCase())) {
      score += 5;
    }
    
    // Tag relevance
    result.tags.forEach(tag => {
      if (tag.toLowerCase().includes(query.toLowerCase())) {
        score += 3;
      }
    });
    
    // Popularity boost
    score += result.popularity * 0.1;
    
    // Rating boost
    if (result.rating) {
      score += result.rating * 2;
    }
    
    // Source preference (YouTube and Pinterest tend to have good recipe content)
    if (result.source === 'YouTube' || result.source === 'Pinterest') {
      score += 2;
    }
    
    return score;
  }

  private isCacheValid(key: string): boolean {
    const cached = this.trendingCache.get(key);
    if (!cached) return false;
    
    return Date.now() - cached.timestamp < this.CACHE_DURATION;
  }

  // Advanced Search Features
  public async getSearchSuggestions(partial: string): Promise<string[]> {
    const suggestions = [
      'pasta recipes',
      'chicken dinner',
      'vegetarian meals',
      'quick breakfast',
      'healthy snacks',
      'dessert ideas',
      'soup recipes',
      'salad recipes',
      'bread recipes',
      'smoothie recipes'
    ];

    return suggestions
      .filter(suggestion => 
        suggestion.toLowerCase().includes(partial.toLowerCase())
      )
      .slice(0, 5);
  }

  public async getPopularSearches(): Promise<string[]> {
    return [
      'air fryer recipes',
      'instant pot meals',
      'keto recipes',
      'vegan desserts',
      'meal prep ideas',
      'one pot meals',
      'gluten free recipes',
      'healthy breakfast',
      'quick dinner',
      'comfort food'
    ];
  }
}
