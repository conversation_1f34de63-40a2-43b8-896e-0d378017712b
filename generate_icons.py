#!/usr/bin/env python3
"""
ChefAI Chrome Extension Icon Generator
Creates professional icons in multiple sizes for the Chrome extension
"""

from PIL import Image, ImageDraw, ImageFont
import os
import math

def create_gradient_background(size, colors):
    """Create a gradient background"""
    image = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(image)
    
    # Create gradient effect
    for i in range(size):
        # Calculate color interpolation
        ratio = i / size
        if ratio < 0.5:
            # First half: color1 to color2
            t = ratio * 2
            r = int(colors[0][0] * (1-t) + colors[1][0] * t)
            g = int(colors[0][1] * (1-t) + colors[1][1] * t)
            b = int(colors[0][2] * (1-t) + colors[1][2] * t)
        else:
            # Second half: color2 to color3
            t = (ratio - 0.5) * 2
            r = int(colors[1][0] * (1-t) + colors[2][0] * t)
            g = int(colors[1][1] * (1-t) + colors[2][1] * t)
            b = int(colors[1][2] * (1-t) + colors[2][2] * t)
        
        draw.line([(0, i), (size, i)], fill=(r, g, b, 255))
    
    return image

def create_rounded_rectangle(size, radius, fill_color):
    """Create a rounded rectangle mask"""
    image = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(image)
    
    # Draw rounded rectangle
    draw.rounded_rectangle(
        [(0, 0), (size-1, size-1)], 
        radius=radius, 
        fill=fill_color
    )
    
    return image

def draw_chef_hat(draw, size):
    """Draw a chef hat icon"""
    # Chef hat base (band)
    hat_base_height = int(size * 0.15)
    hat_base_width = int(size * 0.7)
    hat_base_x = (size - hat_base_width) // 2
    hat_base_y = int(size * 0.7)
    
    draw.rectangle(
        [(hat_base_x, hat_base_y), (hat_base_x + hat_base_width, hat_base_y + hat_base_height)],
        fill='white'
    )
    
    # Chef hat top (puffy part)
    hat_top_center_x = size // 2
    hat_top_center_y = int(size * 0.4)
    hat_top_radius = int(size * 0.25)
    
    # Main hat circle
    draw.ellipse(
        [(hat_top_center_x - hat_top_radius, hat_top_center_y - hat_top_radius),
         (hat_top_center_x + hat_top_radius, hat_top_center_y + hat_top_radius)],
        fill='white'
    )
    
    # Add smaller puffs for detail (only for larger icons)
    if size >= 32:
        puff_size = int(size * 0.08)
        
        # Left puff
        left_puff_x = hat_top_center_x - int(hat_top_radius * 0.6)
        left_puff_y = hat_top_center_y - int(hat_top_radius * 0.3)
        draw.ellipse(
            [(left_puff_x - puff_size, left_puff_y - puff_size),
             (left_puff_x + puff_size, left_puff_y + puff_size)],
            fill='white'
        )
        
        # Right puff
        right_puff_x = hat_top_center_x + int(hat_top_radius * 0.6)
        right_puff_y = hat_top_center_y - int(hat_top_radius * 0.3)
        draw.ellipse(
            [(right_puff_x - puff_size, right_puff_y - puff_size),
             (right_puff_x + puff_size, right_puff_y + puff_size)],
            fill='white'
        )
        
        # Top puff
        top_puff_x = hat_top_center_x
        top_puff_y = hat_top_center_y - int(hat_top_radius * 0.7)
        draw.ellipse(
            [(top_puff_x - puff_size, top_puff_y - puff_size),
             (top_puff_x + puff_size, top_puff_y + puff_size)],
            fill='white'
        )

def add_sparkles(draw, size):
    """Add sparkle effects for AI theme"""
    if size >= 48:
        sparkle_size = int(size * 0.03)
        
        # Sparkle positions
        sparkles = [
            (int(size * 0.75), int(size * 0.25)),
            (int(size * 0.25), int(size * 0.3)),
            (int(size * 0.8), int(size * 0.6))
        ]
        
        for i, (x, y) in enumerate(sparkles):
            current_size = sparkle_size if i == 0 else int(sparkle_size * (0.7 if i == 1 else 0.5))
            draw.ellipse(
                [(x - current_size, y - current_size),
                 (x + current_size, y + current_size)],
                fill='#FFD700'  # Gold color
            )

def create_icon(size):
    """Create a single icon of the specified size"""
    # Color scheme: ChefAI gradient
    colors = [
        (102, 126, 234),  # #667eea
        (118, 75, 162),   # #764ba2  
        (240, 147, 251)   # #f093fb
    ]
    
    # Create gradient background
    image = create_gradient_background(size, colors)
    
    # Create rounded rectangle mask
    radius = int(size * 0.2)
    mask = create_rounded_rectangle(size, radius, (255, 255, 255, 255))
    
    # Apply mask to create rounded corners
    image = Image.alpha_composite(
        Image.new('RGBA', (size, size), (0, 0, 0, 0)),
        image
    )
    
    # Create a new image with the gradient and rounded corners
    final_image = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(final_image)
    
    # Draw rounded rectangle with gradient
    draw.rounded_rectangle(
        [(0, 0), (size-1, size-1)], 
        radius=radius, 
        fill=None
    )
    
    # Paste gradient background
    final_image.paste(image, (0, 0))
    
    # Create new draw object for the final image
    draw = ImageDraw.Draw(final_image)
    
    # Draw chef hat
    draw_chef_hat(draw, size)
    
    # Add sparkles for larger icons
    add_sparkles(draw, size)
    
    # Add subtle highlight
    highlight = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    highlight_draw = ImageDraw.Draw(highlight)
    
    # Create highlight gradient
    for i in range(size // 2):
        alpha = int(76 * (1 - i / (size // 2)))  # 30% opacity fading to 0
        highlight_draw.ellipse(
            [(0, 0), (size // 2 + i, size // 2 + i)],
            fill=(255, 255, 255, alpha)
        )
    
    # Composite highlight
    final_image = Image.alpha_composite(final_image, highlight)
    
    return final_image

def generate_all_icons():
    """Generate all required icon sizes"""
    icon_sizes = [
        (16, 'icon16.png', 'Toolbar Icon'),
        (32, 'icon32.png', 'Extension Management'),
        (48, 'icon48.png', 'Extension Management'),
        (128, 'icon128.png', 'Chrome Web Store')
    ]
    
    # Create icons directory if it doesn't exist
    icons_dir = 'icons'
    if not os.path.exists(icons_dir):
        os.makedirs(icons_dir)
    
    print("🍳 Generating ChefAI Chrome Extension Icons...")
    print("=" * 50)
    
    for size, filename, description in icon_sizes:
        print(f"Creating {description} ({size}x{size})...")
        
        # Generate icon
        icon = create_icon(size)
        
        # Save icon
        icon_path = os.path.join(icons_dir, filename)
        icon.save(icon_path, 'PNG', optimize=True)
        
        print(f"✅ Saved: {icon_path}")
    
    print("=" * 50)
    print("🎉 All icons generated successfully!")
    print(f"📁 Icons saved in: {os.path.abspath(icons_dir)}")
    print("\n📋 Generated files:")
    for size, filename, description in icon_sizes:
        print(f"  • {filename} - {description} ({size}x{size})")

if __name__ == "__main__":
    try:
        generate_all_icons()
    except ImportError:
        print("❌ Error: PIL (Pillow) is required to generate icons.")
        print("📦 Install it with: pip install Pillow")
        print("\n🔄 Alternative: Open 'create-icons.html' in your browser to generate icons manually.")
    except Exception as e:
        print(f"❌ Error generating icons: {e}")
        print("\n🔄 Alternative: Open 'create-icons.html' in your browser to generate icons manually.")
