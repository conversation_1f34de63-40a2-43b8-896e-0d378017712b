import { Recipe, NutritionData, DietaryRestriction, HealthGoal } from '../types';

interface FoodNutritionData {
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  fiber: number;
  sugar: number;
  sodium: number;
  vitamins: { [key: string]: number };
  minerals: { [key: string]: number };
}

interface NutritionDatabase {
  [ingredient: string]: FoodNutritionData;
}

export class NutritionService {
  private static instance: NutritionService;
  private nutritionDatabase: NutritionDatabase;

  private constructor() {
    this.initializeNutritionDatabase();
  }

  public static getInstance(): NutritionService {
    if (!NutritionService.instance) {
      NutritionService.instance = new NutritionService();
    }
    return NutritionService.instance;
  }

  private initializeNutritionDatabase(): void {
    // Comprehensive nutrition database (per 100g)
    this.nutritionDatabase = {
      // Proteins
      'chicken breast': { calories: 165, protein: 31, carbs: 0, fat: 3.6, fiber: 0, sugar: 0, sodium: 74, vitamins: { B6: 0.5, B12: 0.3 }, minerals: { iron: 0.7, zinc: 1.0 } },
      'salmon': { calories: 208, protein: 25, carbs: 0, fat: 12, fiber: 0, sugar: 0, sodium: 59, vitamins: { D: 11, B12: 4.9 }, minerals: { selenium: 24.9 } },
      'eggs': { calories: 155, protein: 13, carbs: 1.1, fat: 11, fiber: 0, sugar: 1.1, sodium: 124, vitamins: { B12: 0.9, D: 2.0 }, minerals: { iron: 1.8 } },
      'tofu': { calories: 76, protein: 8, carbs: 1.9, fat: 4.8, fiber: 0.3, sugar: 0.6, sodium: 7, vitamins: { B1: 0.1 }, minerals: { calcium: 350, iron: 5.4 } },
      
      // Grains & Carbs
      'rice': { calories: 130, protein: 2.7, carbs: 28, fat: 0.3, fiber: 0.4, sugar: 0.1, sodium: 1, vitamins: { B1: 0.07 }, minerals: { manganese: 0.9 } },
      'quinoa': { calories: 120, protein: 4.4, carbs: 22, fat: 1.9, fiber: 2.8, sugar: 0.9, sodium: 7, vitamins: { folate: 42 }, minerals: { iron: 1.5, magnesium: 64 } },
      'oats': { calories: 389, protein: 17, carbs: 66, fat: 7, fiber: 11, sugar: 0.99, sodium: 2, vitamins: { B1: 0.76 }, minerals: { manganese: 4.9, phosphorus: 523 } },
      'bread': { calories: 265, protein: 9, carbs: 49, fat: 3.2, fiber: 2.7, sugar: 5, sodium: 491, vitamins: { folate: 34 }, minerals: { iron: 3.6 } },
      
      // Vegetables
      'broccoli': { calories: 34, protein: 2.8, carbs: 7, fat: 0.4, fiber: 2.6, sugar: 1.5, sodium: 33, vitamins: { C: 89.2, K: 101.6 }, minerals: { folate: 63 } },
      'spinach': { calories: 23, protein: 2.9, carbs: 3.6, fat: 0.4, fiber: 2.2, sugar: 0.4, sodium: 79, vitamins: { K: 483, A: 469 }, minerals: { iron: 2.7, folate: 194 } },
      'carrots': { calories: 41, protein: 0.9, carbs: 10, fat: 0.2, fiber: 2.8, sugar: 4.7, sodium: 69, vitamins: { A: 835 }, minerals: { potassium: 320 } },
      'tomatoes': { calories: 18, protein: 0.9, carbs: 3.9, fat: 0.2, fiber: 1.2, sugar: 2.6, sodium: 5, vitamins: { C: 13.7, K: 7.9 }, minerals: { potassium: 237 } },
      
      // Fruits
      'apple': { calories: 52, protein: 0.3, carbs: 14, fat: 0.2, fiber: 2.4, sugar: 10, sodium: 1, vitamins: { C: 4.6 }, minerals: { potassium: 107 } },
      'banana': { calories: 89, protein: 1.1, carbs: 23, fat: 0.3, fiber: 2.6, sugar: 12, sodium: 1, vitamins: { B6: 0.4, C: 8.7 }, minerals: { potassium: 358 } },
      'berries': { calories: 57, protein: 0.7, carbs: 14, fat: 0.3, fiber: 2.4, sugar: 10, sodium: 1, vitamins: { C: 9.7 }, minerals: { manganese: 0.4 } },
      
      // Dairy
      'milk': { calories: 42, protein: 3.4, carbs: 5, fat: 1, fiber: 0, sugar: 5, sodium: 44, vitamins: { B12: 0.5, D: 1.3 }, minerals: { calcium: 125 } },
      'cheese': { calories: 113, protein: 7, carbs: 1, fat: 9, fiber: 0, sugar: 1, sodium: 621, vitamins: { B12: 0.8 }, minerals: { calcium: 200 } },
      'yogurt': { calories: 59, protein: 10, carbs: 3.6, fat: 0.4, fiber: 0, sugar: 3.2, sodium: 36, vitamins: { B12: 0.5 }, minerals: { calcium: 110 } },
      
      // Fats & Oils
      'olive oil': { calories: 884, protein: 0, carbs: 0, fat: 100, fiber: 0, sugar: 0, sodium: 2, vitamins: { E: 14.4, K: 60.2 }, minerals: {} },
      'avocado': { calories: 160, protein: 2, carbs: 9, fat: 15, fiber: 7, sugar: 0.7, sodium: 7, vitamins: { K: 21, folate: 20 }, minerals: { potassium: 485 } },
      'nuts': { calories: 607, protein: 15, carbs: 7, fat: 54, fiber: 8, sugar: 2.3, sodium: 2, vitamins: { E: 26 }, minerals: { magnesium: 158 } }
    };
  }

  public async analyzeRecipeNutrition(recipe: Recipe): Promise<NutritionData> {
    let totalNutrition: NutritionData = {
      calories: 0,
      protein: 0,
      carbs: 0,
      fat: 0,
      fiber: 0,
      sugar: 0,
      sodium: 0,
      vitamins: {},
      minerals: {},
      healthScore: 0,
      macroDistribution: { protein: 0, carbs: 0, fat: 0 },
      micronutrientDensity: 0
    };

    // Analyze each ingredient
    for (const ingredient of recipe.ingredients) {
      const nutritionData = this.getNutritionForIngredient(ingredient.name, ingredient.amount, ingredient.unit);
      if (nutritionData) {
        totalNutrition = this.addNutritionData(totalNutrition, nutritionData);
      }
    }

    // Calculate per serving
    if (recipe.servings > 0) {
      totalNutrition = this.divideNutritionData(totalNutrition, recipe.servings);
    }

    // Calculate derived metrics
    totalNutrition.healthScore = this.calculateHealthScore(totalNutrition);
    totalNutrition.macroDistribution = this.calculateMacroDistribution(totalNutrition);
    totalNutrition.micronutrientDensity = this.calculateMicronutrientDensity(totalNutrition);

    return totalNutrition;
  }

  private getNutritionForIngredient(ingredientName: string, amount: number, unit: string): NutritionData | null {
    // Normalize ingredient name for lookup
    const normalizedName = this.normalizeIngredientName(ingredientName);
    const baseNutrition = this.nutritionDatabase[normalizedName];
    
    if (!baseNutrition) {
      // Try partial matches
      const partialMatch = Object.keys(this.nutritionDatabase).find(key => 
        normalizedName.includes(key) || key.includes(normalizedName)
      );
      
      if (partialMatch) {
        return this.scaleNutritionData(this.nutritionDatabase[partialMatch], amount, unit);
      }
      
      return null;
    }

    return this.scaleNutritionData(baseNutrition, amount, unit);
  }

  private normalizeIngredientName(name: string): string {
    return name.toLowerCase()
      .replace(/\b(fresh|dried|chopped|sliced|diced|minced|ground)\b/g, '')
      .replace(/\s+/g, ' ')
      .trim();
  }

  private scaleNutritionData(baseData: FoodNutritionData, amount: number, unit: string): NutritionData {
    // Convert to grams (base data is per 100g)
    const grams = this.convertToGrams(amount, unit);
    const scaleFactor = grams / 100;

    return {
      calories: Math.round(baseData.calories * scaleFactor),
      protein: Math.round(baseData.protein * scaleFactor * 10) / 10,
      carbs: Math.round(baseData.carbs * scaleFactor * 10) / 10,
      fat: Math.round(baseData.fat * scaleFactor * 10) / 10,
      fiber: Math.round(baseData.fiber * scaleFactor * 10) / 10,
      sugar: Math.round(baseData.sugar * scaleFactor * 10) / 10,
      sodium: Math.round(baseData.sodium * scaleFactor),
      vitamins: Object.fromEntries(
        Object.entries(baseData.vitamins).map(([key, value]) => [
          key, Math.round(value * scaleFactor * 10) / 10
        ])
      ),
      minerals: Object.fromEntries(
        Object.entries(baseData.minerals).map(([key, value]) => [
          key, Math.round(value * scaleFactor * 10) / 10
        ])
      ),
      healthScore: 0,
      macroDistribution: { protein: 0, carbs: 0, fat: 0 },
      micronutrientDensity: 0
    };
  }

  private convertToGrams(amount: number, unit: string): number {
    const conversions: { [unit: string]: number } = {
      'g': 1,
      'gram': 1,
      'grams': 1,
      'kg': 1000,
      'kilogram': 1000,
      'oz': 28.35,
      'ounce': 28.35,
      'ounces': 28.35,
      'lb': 453.59,
      'pound': 453.59,
      'pounds': 453.59,
      'cup': 240, // approximate for liquids
      'cups': 240,
      'tbsp': 15,
      'tablespoon': 15,
      'tablespoons': 15,
      'tsp': 5,
      'teaspoon': 5,
      'teaspoons': 5,
      'ml': 1, // approximate for water
      'milliliter': 1,
      'milliliters': 1,
      'l': 1000,
      'liter': 1000,
      'liters': 1000,
      'piece': 100, // average piece weight
      'pieces': 100,
      'item': 100,
      'items': 100
    };

    const normalizedUnit = unit.toLowerCase().trim();
    return amount * (conversions[normalizedUnit] || 100); // default to 100g if unknown
  }

  private addNutritionData(data1: NutritionData, data2: NutritionData): NutritionData {
    return {
      calories: data1.calories + data2.calories,
      protein: data1.protein + data2.protein,
      carbs: data1.carbs + data2.carbs,
      fat: data1.fat + data2.fat,
      fiber: data1.fiber + data2.fiber,
      sugar: data1.sugar + data2.sugar,
      sodium: data1.sodium + data2.sodium,
      vitamins: this.mergeNutrientMaps(data1.vitamins, data2.vitamins),
      minerals: this.mergeNutrientMaps(data1.minerals, data2.minerals),
      healthScore: 0,
      macroDistribution: { protein: 0, carbs: 0, fat: 0 },
      micronutrientDensity: 0
    };
  }

  private mergeNutrientMaps(map1: { [key: string]: number }, map2: { [key: string]: number }): { [key: string]: number } {
    const result = { ...map1 };
    Object.entries(map2).forEach(([key, value]) => {
      result[key] = (result[key] || 0) + value;
    });
    return result;
  }

  private divideNutritionData(data: NutritionData, divisor: number): NutritionData {
    return {
      calories: Math.round(data.calories / divisor),
      protein: Math.round(data.protein / divisor * 10) / 10,
      carbs: Math.round(data.carbs / divisor * 10) / 10,
      fat: Math.round(data.fat / divisor * 10) / 10,
      fiber: Math.round(data.fiber / divisor * 10) / 10,
      sugar: Math.round(data.sugar / divisor * 10) / 10,
      sodium: Math.round(data.sodium / divisor),
      vitamins: Object.fromEntries(
        Object.entries(data.vitamins).map(([key, value]) => [
          key, Math.round(value / divisor * 10) / 10
        ])
      ),
      minerals: Object.fromEntries(
        Object.entries(data.minerals).map(([key, value]) => [
          key, Math.round(value / divisor * 10) / 10
        ])
      ),
      healthScore: data.healthScore,
      macroDistribution: data.macroDistribution,
      micronutrientDensity: data.micronutrientDensity
    };
  }

  private calculateHealthScore(nutrition: NutritionData): number {
    let score = 100;

    // Calorie assessment (assuming 2000 cal/day, this should be 400-600 per meal)
    if (nutrition.calories > 800) score -= 20;
    if (nutrition.calories < 200) score -= 15;

    // Macro balance assessment
    const totalMacros = nutrition.protein * 4 + nutrition.carbs * 4 + nutrition.fat * 9;
    if (totalMacros > 0) {
      const proteinPercent = (nutrition.protein * 4 / totalMacros) * 100;
      const carbPercent = (nutrition.carbs * 4 / totalMacros) * 100;
      const fatPercent = (nutrition.fat * 9 / totalMacros) * 100;

      // Ideal ranges: Protein 15-25%, Carbs 45-65%, Fat 20-35%
      if (proteinPercent < 15 || proteinPercent > 25) score -= 15;
      if (carbPercent < 45 || carbPercent > 65) score -= 15;
      if (fatPercent < 20 || fatPercent > 35) score -= 15;
    }

    // Fiber bonus
    if (nutrition.fiber >= 5) score += 10;
    else if (nutrition.fiber >= 3) score += 5;

    // Sugar penalty
    if (nutrition.sugar > 25) score -= 15;
    else if (nutrition.sugar > 15) score -= 10;

    // Sodium penalty
    if (nutrition.sodium > 1000) score -= 20;
    else if (nutrition.sodium > 600) score -= 10;

    // Micronutrient bonus
    const vitaminCount = Object.keys(nutrition.vitamins).length;
    const mineralCount = Object.keys(nutrition.minerals).length;
    score += Math.min(vitaminCount + mineralCount, 15);

    return Math.max(0, Math.min(100, score));
  }

  private calculateMacroDistribution(nutrition: NutritionData): { protein: number; carbs: number; fat: number } {
    const proteinCals = nutrition.protein * 4;
    const carbCals = nutrition.carbs * 4;
    const fatCals = nutrition.fat * 9;
    const totalCals = proteinCals + carbCals + fatCals;

    if (totalCals === 0) {
      return { protein: 0, carbs: 0, fat: 0 };
    }

    return {
      protein: Math.round((proteinCals / totalCals) * 100),
      carbs: Math.round((carbCals / totalCals) * 100),
      fat: Math.round((fatCals / totalCals) * 100)
    };
  }

  private calculateMicronutrientDensity(nutrition: NutritionData): number {
    const vitaminCount = Object.keys(nutrition.vitamins).length;
    const mineralCount = Object.keys(nutrition.minerals).length;
    const totalMicronutrients = vitaminCount + mineralCount;
    
    // Density per 100 calories
    return nutrition.calories > 0 ? Math.round((totalMicronutrients / nutrition.calories) * 100 * 10) / 10 : 0;
  }

  public async getDietaryCompatibility(nutrition: NutritionData, restrictions: DietaryRestriction[]): Promise<any> {
    const compatibility = {
      compatible: true,
      warnings: [],
      suggestions: []
    };

    restrictions.forEach(restriction => {
      switch (restriction) {
        case 'low_sodium':
          if (nutrition.sodium > 600) {
            compatibility.compatible = false;
            compatibility.warnings.push('High sodium content');
            compatibility.suggestions.push('Reduce salt and use herbs/spices for flavor');
          }
          break;
        case 'low_sugar':
          if (nutrition.sugar > 15) {
            compatibility.compatible = false;
            compatibility.warnings.push('High sugar content');
            compatibility.suggestions.push('Use natural sweeteners or reduce sweet ingredients');
          }
          break;
        case 'high_protein':
          if (nutrition.macroDistribution.protein < 25) {
            compatibility.warnings.push('Could use more protein');
            compatibility.suggestions.push('Add lean protein sources like chicken, fish, or legumes');
          }
          break;
        case 'low_carb':
          if (nutrition.macroDistribution.carbs > 30) {
            compatibility.compatible = false;
            compatibility.warnings.push('High carbohydrate content');
            compatibility.suggestions.push('Replace grains with vegetables or reduce portion sizes');
          }
          break;
      }
    });

    return compatibility;
  }

  public async getHealthGoalAlignment(nutrition: NutritionData, goals: HealthGoal[]): Promise<any> {
    const alignment = {
      score: 0,
      recommendations: []
    };

    goals.forEach(goal => {
      switch (goal) {
        case 'weight_loss':
          if (nutrition.calories <= 500 && nutrition.fiber >= 5) {
            alignment.score += 25;
          } else {
            alignment.recommendations.push('Reduce calories and increase fiber for weight loss');
          }
          break;
        case 'muscle_gain':
          if (nutrition.macroDistribution.protein >= 25) {
            alignment.score += 25;
          } else {
            alignment.recommendations.push('Increase protein intake for muscle building');
          }
          break;
        case 'heart_health':
          if (nutrition.sodium <= 400 && nutrition.fat <= 10) {
            alignment.score += 25;
          } else {
            alignment.recommendations.push('Reduce sodium and saturated fat for heart health');
          }
          break;
        case 'energy_boost':
          if (nutrition.macroDistribution.carbs >= 45 && nutrition.macroDistribution.carbs <= 65) {
            alignment.score += 25;
          } else {
            alignment.recommendations.push('Balance carbohydrates for sustained energy');
          }
          break;
      }
    });

    return alignment;
  }
}
