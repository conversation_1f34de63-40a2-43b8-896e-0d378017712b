import React from 'react';

interface ErrorMessageProps {
  message: string;
  onDismiss?: () => void;
  type?: 'error' | 'warning' | 'info';
}

export const ErrorMessage: React.FC<ErrorMessageProps> = ({ 
  message, 
  onDismiss,
  type = 'error'
}) => {
  const getIcon = () => {
    switch (type) {
      case 'warning':
        return '⚠️';
      case 'info':
        return 'ℹ️';
      default:
        return '❌';
    }
  };

  const getClassName = () => {
    switch (type) {
      case 'warning':
        return 'warning-message';
      case 'info':
        return 'info-message';
      default:
        return 'error-message';
    }
  };

  return (
    <div className={getClassName()}>
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span className="error-icon">{getIcon()}</span>
          <span>{message}</span>
        </div>
        {onDismiss && (
          <button
            onClick={onDismiss}
            style={{
              background: 'none',
              border: 'none',
              color: 'inherit',
              cursor: 'pointer',
              padding: '0',
              fontSize: 'var(--font-size-lg)',
              opacity: 0.7
            }}
          >
            ×
          </button>
        )}
      </div>
    </div>
  );
};
