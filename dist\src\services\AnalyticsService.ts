import { Recipe, UserAnalytics, TrendData } from '../types';

interface AnalyticsEvent {
  id: string;
  type: 'recipe_generated' | 'recipe_viewed' | 'recipe_shared' | 'search_performed' | 'challenge_joined';
  timestamp: Date;
  data: any;
  userId?: string;
}

interface CookingInsight {
  id: string;
  type: 'ingredient_preference' | 'cooking_pattern' | 'skill_improvement' | 'dietary_trend';
  title: string;
  description: string;
  data: any;
  confidence: number; // 0-100
  actionable: boolean;
}

export class AnalyticsService {
  private static instance: AnalyticsService;
  private events: AnalyticsEvent[] = [];
  private insights: CookingInsight[] = [];

  private constructor() {
    this.loadAnalyticsData();
  }

  public static getInstance(): AnalyticsService {
    if (!AnalyticsService.instance) {
      AnalyticsService.instance = new AnalyticsService();
    }
    return AnalyticsService.instance;
  }

  private async loadAnalyticsData(): Promise<void> {
    try {
      const result = await chrome.storage.local.get(['analyticsEvents', 'cookingInsights']);
      this.events = result.analyticsEvents || [];
      this.insights = result.cookingInsights || [];
    } catch (error) {
      console.error('Failed to load analytics data:', error);
    }
  }

  private async saveAnalyticsData(): Promise<void> {
    try {
      await chrome.storage.local.set({
        analyticsEvents: this.events,
        cookingInsights: this.insights
      });
    } catch (error) {
      console.error('Failed to save analytics data:', error);
    }
  }

  // Event Tracking
  public async trackEvent(
    type: AnalyticsEvent['type'],
    data: any,
    userId?: string
  ): Promise<void> {
    const event: AnalyticsEvent = {
      id: `event-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type,
      timestamp: new Date(),
      data,
      userId
    };

    this.events.push(event);
    
    // Keep only last 1000 events
    if (this.events.length > 1000) {
      this.events = this.events.slice(-1000);
    }

    await this.saveAnalyticsData();
    
    // Generate insights based on new event
    await this.generateInsights();
  }

  // User Analytics
  public async getUserAnalytics(userId?: string): Promise<UserAnalytics> {
    const userEvents = userId 
      ? this.events.filter(event => event.userId === userId)
      : this.events;

    const recipeEvents = userEvents.filter(event => event.type === 'recipe_generated');
    const searchEvents = userEvents.filter(event => event.type === 'search_performed');
    const shareEvents = userEvents.filter(event => event.type === 'recipe_shared');

    // Analyze favorite ingredients
    const ingredientCounts: { [ingredient: string]: number } = {};
    recipeEvents.forEach(event => {
      if (event.data.ingredients) {
        event.data.ingredients.forEach((ingredient: string) => {
          ingredientCounts[ingredient] = (ingredientCounts[ingredient] || 0) + 1;
        });
      }
    });

    // Analyze cooking frequency by day
    const cookingFrequency: { [day: string]: number } = {
      Monday: 0, Tuesday: 0, Wednesday: 0, Thursday: 0,
      Friday: 0, Saturday: 0, Sunday: 0
    };
    
    recipeEvents.forEach(event => {
      const dayName = event.timestamp.toLocaleDateString('en-US', { weekday: 'long' });
      cookingFrequency[dayName]++;
    });

    // Analyze preferred cooking times
    const cookingTimes: number[] = [];
    recipeEvents.forEach(event => {
      if (event.data.cookingTime) {
        cookingTimes.push(event.data.cookingTime);
      }
    });

    return {
      totalRecipesGenerated: recipeEvents.length,
      favoriteIngredients: ingredientCounts,
      cookingFrequency,
      preferredCookingTimes: cookingTimes,
      successfulRecipes: recipeEvents.filter(event => event.data.rating >= 4).length,
      sharedRecipes: shareEvents.length,
      challengesParticipated: userEvents.filter(event => event.type === 'challenge_joined').length
    };
  }

  // Recipe Analytics
  public async getRecipeAnalytics(recipeId: string): Promise<any> {
    const recipeEvents = this.events.filter(event => 
      event.data.recipeId === recipeId
    );

    const views = recipeEvents.filter(event => event.type === 'recipe_viewed').length;
    const shares = recipeEvents.filter(event => event.type === 'recipe_shared').length;

    // Mock additional data for demonstration
    return {
      views,
      shares,
      saves: Math.floor(views * 0.3), // Estimate saves as 30% of views
      rating: 4.2 + Math.random() * 0.8,
      comments: Math.floor(views * 0.1),
      popularityTrend: shares > views * 0.1 ? 'increasing' : 'stable',
      demographics: {
        ageGroups: {
          '18-25': 25,
          '26-35': 35,
          '36-45': 25,
          '46-55': 10,
          '55+': 5
        },
        regions: {
          'North America': 45,
          'Europe': 30,
          'Asia': 15,
          'Other': 10
        }
      }
    };
  }

  // Insights Generation
  private async generateInsights(): Promise<void> {
    const newInsights: CookingInsight[] = [];

    // Ingredient preference insights
    const ingredientInsight = await this.generateIngredientInsights();
    if (ingredientInsight) newInsights.push(ingredientInsight);

    // Cooking pattern insights
    const patternInsight = await this.generateCookingPatternInsights();
    if (patternInsight) newInsights.push(patternInsight);

    // Skill improvement insights
    const skillInsight = await this.generateSkillInsights();
    if (skillInsight) newInsights.push(skillInsight);

    // Dietary trend insights
    const dietaryInsight = await this.generateDietaryInsights();
    if (dietaryInsight) newInsights.push(dietaryInsight);

    // Add new insights and keep only recent ones
    this.insights.push(...newInsights);
    this.insights = this.insights.slice(-20); // Keep last 20 insights

    await this.saveAnalyticsData();
  }

  private async generateIngredientInsights(): Promise<CookingInsight | null> {
    const recentRecipes = this.events
      .filter(event => event.type === 'recipe_generated')
      .slice(-10);

    if (recentRecipes.length < 5) return null;

    const ingredientCounts: { [ingredient: string]: number } = {};
    recentRecipes.forEach(event => {
      if (event.data.ingredients) {
        event.data.ingredients.forEach((ingredient: string) => {
          ingredientCounts[ingredient] = (ingredientCounts[ingredient] || 0) + 1;
        });
      }
    });

    const topIngredient = Object.entries(ingredientCounts)
      .sort(([,a], [,b]) => b - a)[0];

    if (!topIngredient || topIngredient[1] < 3) return null;

    return {
      id: `insight-ingredient-${Date.now()}`,
      type: 'ingredient_preference',
      title: `You love cooking with ${topIngredient[0]}!`,
      description: `You've used ${topIngredient[0]} in ${topIngredient[1]} of your last ${recentRecipes.length} recipes. Consider exploring new recipes that highlight this ingredient.`,
      data: { ingredient: topIngredient[0], count: topIngredient[1] },
      confidence: 85,
      actionable: true
    };
  }

  private async generateCookingPatternInsights(): Promise<CookingInsight | null> {
    const recentEvents = this.events
      .filter(event => event.type === 'recipe_generated')
      .slice(-20);

    if (recentEvents.length < 10) return null;

    // Analyze cooking times
    const cookingTimes = recentEvents
      .map(event => event.data.cookingTime)
      .filter(time => time);

    if (cookingTimes.length === 0) return null;

    const avgCookingTime = cookingTimes.reduce((a, b) => a + b, 0) / cookingTimes.length;

    let insight: string;
    let actionable = true;

    if (avgCookingTime < 30) {
      insight = `You prefer quick meals with an average cooking time of ${Math.round(avgCookingTime)} minutes. Consider meal prep strategies to save even more time.`;
    } else if (avgCookingTime > 60) {
      insight = `You enjoy elaborate cooking with an average time of ${Math.round(avgCookingTime)} minutes. You might enjoy cooking challenges or complex cuisines.`;
    } else {
      insight = `You have a balanced approach to cooking with moderate preparation times.`;
      actionable = false;
    }

    return {
      id: `insight-pattern-${Date.now()}`,
      type: 'cooking_pattern',
      title: 'Your Cooking Time Pattern',
      description: insight,
      data: { averageCookingTime: avgCookingTime },
      confidence: 75,
      actionable
    };
  }

  private async generateSkillInsights(): Promise<CookingInsight | null> {
    const recentRecipes = this.events
      .filter(event => event.type === 'recipe_generated')
      .slice(-15);

    if (recentRecipes.length < 8) return null;

    const difficulties = recentRecipes
      .map(event => event.data.difficulty)
      .filter(diff => diff);

    const difficultyScores = difficulties.map(diff => {
      switch (diff) {
        case 'Easy': return 1;
        case 'Medium': return 2;
        case 'Hard': return 3;
        default: return 1;
      }
    });

    if (difficultyScores.length === 0) return null;

    const avgDifficulty = difficultyScores.reduce((a, b) => a + b, 0) / difficultyScores.length;
    const recentAvg = difficultyScores.slice(-5).reduce((a, b) => a + b, 0) / Math.min(5, difficultyScores.length);

    if (recentAvg > avgDifficulty + 0.3) {
      return {
        id: `insight-skill-${Date.now()}`,
        type: 'skill_improvement',
        title: 'Your Cooking Skills Are Improving!',
        description: `You've been tackling more challenging recipes lately. Your recent recipes are more complex than your average, showing great progress in your culinary journey.`,
        data: { averageDifficulty: avgDifficulty, recentAverage: recentAvg },
        confidence: 80,
        actionable: true
      };
    }

    return null;
  }

  private async generateDietaryInsights(): Promise<CookingInsight | null> {
    const recentRecipes = this.events
      .filter(event => event.type === 'recipe_generated')
      .slice(-12);

    if (recentRecipes.length < 6) return null;

    const dietaryRestrictions = recentRecipes
      .flatMap(event => event.data.dietaryRestrictions || []);

    if (dietaryRestrictions.length === 0) return null;

    const restrictionCounts: { [restriction: string]: number } = {};
    dietaryRestrictions.forEach(restriction => {
      restrictionCounts[restriction] = (restrictionCounts[restriction] || 0) + 1;
    });

    const topRestriction = Object.entries(restrictionCounts)
      .sort(([,a], [,b]) => b - a)[0];

    if (topRestriction[1] >= 3) {
      return {
        id: `insight-dietary-${Date.now()}`,
        type: 'dietary_trend',
        title: `Trending Towards ${topRestriction[0]} Recipes`,
        description: `You've been exploring ${topRestriction[0]} recipes frequently. This dietary choice is becoming a pattern in your cooking habits.`,
        data: { restriction: topRestriction[0], count: topRestriction[1] },
        confidence: 70,
        actionable: true
      };
    }

    return null;
  }

  // Public Methods for Insights
  public getCookingInsights(): CookingInsight[] {
    return [...this.insights].sort((a, b) => b.confidence - a.confidence);
  }

  public async dismissInsight(insightId: string): Promise<void> {
    this.insights = this.insights.filter(insight => insight.id !== insightId);
    await this.saveAnalyticsData();
  }

  // Trend Analysis
  public async getPersonalTrends(timeframe: 'week' | 'month' | 'year' = 'month'): Promise<any> {
    const now = new Date();
    const timeframeDays = timeframe === 'week' ? 7 : timeframe === 'month' ? 30 : 365;
    const startDate = new Date(now.getTime() - timeframeDays * 24 * 60 * 60 * 1000);

    const relevantEvents = this.events.filter(event => 
      event.timestamp >= startDate
    );

    const recipeEvents = relevantEvents.filter(event => event.type === 'recipe_generated');
    
    return {
      recipesGenerated: recipeEvents.length,
      averagePerDay: recipeEvents.length / timeframeDays,
      mostActiveDay: this.getMostActiveDay(recipeEvents),
      topCuisines: this.getTopCuisines(recipeEvents),
      difficultyProgression: this.getDifficultyProgression(recipeEvents),
      ingredientTrends: this.getIngredientTrends(recipeEvents)
    };
  }

  private getMostActiveDay(events: AnalyticsEvent[]): string {
    const dayCounts: { [day: string]: number } = {};
    events.forEach(event => {
      const day = event.timestamp.toLocaleDateString('en-US', { weekday: 'long' });
      dayCounts[day] = (dayCounts[day] || 0) + 1;
    });

    return Object.entries(dayCounts)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'No data';
  }

  private getTopCuisines(events: AnalyticsEvent[]): Array<{ cuisine: string; count: number }> {
    const cuisineCounts: { [cuisine: string]: number } = {};
    events.forEach(event => {
      if (event.data.cuisine) {
        cuisineCounts[event.data.cuisine] = (cuisineCounts[event.data.cuisine] || 0) + 1;
      }
    });

    return Object.entries(cuisineCounts)
      .map(([cuisine, count]) => ({ cuisine, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);
  }

  private getDifficultyProgression(events: AnalyticsEvent[]): Array<{ date: string; difficulty: number }> {
    return events
      .filter(event => event.data.difficulty)
      .map(event => ({
        date: event.timestamp.toISOString().split('T')[0],
        difficulty: event.data.difficulty === 'Easy' ? 1 : 
                   event.data.difficulty === 'Medium' ? 2 : 3
      }))
      .sort((a, b) => a.date.localeCompare(b.date));
  }

  private getIngredientTrends(events: AnalyticsEvent[]): Array<{ ingredient: string; trend: 'rising' | 'stable' | 'declining' }> {
    // Simplified trend analysis
    const recentEvents = events.slice(-10);
    const olderEvents = events.slice(-20, -10);

    const recentIngredients = this.getIngredientCounts(recentEvents);
    const olderIngredients = this.getIngredientCounts(olderEvents);

    return Object.keys(recentIngredients).map(ingredient => {
      const recentCount = recentIngredients[ingredient] || 0;
      const olderCount = olderIngredients[ingredient] || 0;
      
      let trend: 'rising' | 'stable' | 'declining' = 'stable';
      if (recentCount > olderCount) trend = 'rising';
      else if (recentCount < olderCount) trend = 'declining';

      return { ingredient, trend };
    }).slice(0, 10);
  }

  private getIngredientCounts(events: AnalyticsEvent[]): { [ingredient: string]: number } {
    const counts: { [ingredient: string]: number } = {};
    events.forEach(event => {
      if (event.data.ingredients) {
        event.data.ingredients.forEach((ingredient: string) => {
          counts[ingredient] = (counts[ingredient] || 0) + 1;
        });
      }
    });
    return counts;
  }

  // Data Export
  public async exportAnalyticsData(): Promise<string> {
    const data = {
      events: this.events,
      insights: this.insights,
      exportDate: new Date().toISOString(),
      version: '1.0'
    };

    return JSON.stringify(data, null, 2);
  }

  // Privacy Controls
  public async clearAnalyticsData(): Promise<void> {
    this.events = [];
    this.insights = [];
    await chrome.storage.local.remove(['analyticsEvents', 'cookingInsights']);
  }
}
