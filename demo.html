<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChefAI Extension Demo</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        
        h1 {
            font-size: 3em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .subtitle {
            font-size: 1.3em;
            margin-bottom: 40px;
            opacity: 0.9;
        }
        
        .demo-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 40px;
            margin: 30px 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.15);
        }
        
        .feature-icon {
            font-size: 3em;
            margin-bottom: 15px;
            display: block;
        }
        
        .feature-title {
            font-size: 1.3em;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .feature-description {
            opacity: 0.9;
            line-height: 1.5;
        }
        
        .instructions {
            background: rgba(255, 255, 255, 0.1);
            border-left: 4px solid #FFD700;
            padding: 20px;
            margin: 30px 0;
            border-radius: 0 10px 10px 0;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #FFD700;
        }
        
        .step {
            margin: 15px 0;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .step:last-child {
            border-bottom: none;
        }
        
        .step-number {
            display: inline-block;
            width: 30px;
            height: 30px;
            background: #FFD700;
            color: #333;
            border-radius: 50%;
            text-align: center;
            line-height: 30px;
            font-weight: bold;
            margin-right: 15px;
        }
        
        .api-providers {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 30px 0;
        }
        
        .provider-card {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }
        
        .provider-name {
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .provider-models {
            font-size: 0.9em;
            opacity: 0.8;
        }
        
        .floating-button-demo {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            border: 2px solid rgba(255, 255, 255, 0.2);
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 1000;
        }
        
        .floating-button-demo:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 25px rgba(0, 0, 0, 0.4);
        }
        
        .note {
            background: rgba(255, 152, 0, 0.2);
            border: 1px solid rgba(255, 152, 0, 0.5);
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.5);
        }
        
        code {
            background: rgba(0, 0, 0, 0.3);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <div class="floating-button-demo" title="This is how the ChefAI button appears on any website">
        🍳
    </div>
    
    <div class="container">
        <h1>🍳 ChefAI Extension</h1>
        <p class="subtitle">AI-Powered Recipe Generation with Real LLM Integration</p>
        
        <div class="demo-card">
            <h2>🚀 Extension Features</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <span class="feature-icon">🤖</span>
                    <div class="feature-title">Multiple LLM Providers</div>
                    <div class="feature-description">OpenRouter, Gemini, OpenAI, Anthropic support with real API integration</div>
                </div>
                
                <div class="feature-card">
                    <span class="feature-icon">📱</span>
                    <div class="feature-title">Browser Sidebar</div>
                    <div class="feature-description">Beautiful glassmorphism sidebar interface accessible from any website</div>
                </div>
                
                <div class="feature-card">
                    <span class="feature-icon">✨</span>
                    <div class="feature-title">Smart Recipe Generation</div>
                    <div class="feature-description">Generate recipes from ingredients, dietary restrictions, and preferences</div>
                </div>
                
                <div class="feature-card">
                    <span class="feature-icon">⚙️</span>
                    <div class="feature-title">Configurable Settings</div>
                    <div class="feature-description">Customize AI provider, model, temperature, and system prompts</div>
                </div>
                
                <div class="feature-card">
                    <span class="feature-icon">📚</span>
                    <div class="feature-title">Recipe History</div>
                    <div class="feature-description">Save and view all AI-generated recipes with full details</div>
                </div>
                
                <div class="feature-card">
                    <span class="feature-icon">🔧</span>
                    <div class="feature-title">Smart Device Integration</div>
                    <div class="feature-description">Connect with smart kitchen appliances and automation</div>
                </div>
            </div>
        </div>
        
        <div class="demo-card">
            <h2>🔗 Supported LLM Providers</h2>
            <div class="api-providers">
                <div class="provider-card">
                    <div class="provider-name">🌐 OpenRouter</div>
                    <div class="provider-models">Claude 3.5 Sonnet, GPT-4 Turbo, Llama 3.1</div>
                </div>
                
                <div class="provider-card">
                    <div class="provider-name">🧠 Google Gemini</div>
                    <div class="provider-models">Gemini 1.5 Pro, Gemini 1.5 Flash</div>
                </div>
                
                <div class="provider-card">
                    <div class="provider-name">🤖 OpenAI</div>
                    <div class="provider-models">GPT-4o, GPT-4o Mini</div>
                </div>
                
                <div class="provider-card">
                    <div class="provider-name">🎭 Anthropic</div>
                    <div class="provider-models">Claude 3.5 Sonnet, Claude 3 Haiku</div>
                </div>
            </div>
        </div>
        
        <div class="instructions">
            <h3>📋 How to Use the Extension</h3>
            
            <div class="step">
                <span class="step-number">1</span>
                <strong>Install the Extension:</strong> Load the extension in Chrome Developer Mode from the project folder
            </div>
            
            <div class="step">
                <span class="step-number">2</span>
                <strong>Configure LLM API:</strong> Click the floating 🍳 button and go to "LLM Settings" tab
            </div>
            
            <div class="step">
                <span class="step-number">3</span>
                <strong>Add API Key:</strong> Select your preferred provider and enter your API key
            </div>
            
            <div class="step">
                <span class="step-number">4</span>
                <strong>Test Connection:</strong> Use the "Test Connection" button to verify your setup
            </div>
            
            <div class="step">
                <span class="step-number">5</span>
                <strong>Generate Recipes:</strong> Go to "Generate Recipe" tab and specify your requirements
            </div>
            
            <div class="step">
                <span class="step-number">6</span>
                <strong>View Results:</strong> Check "Recent Recipes" tab to see your AI-generated recipes
            </div>
        </div>
        
        <div class="note">
            <strong>💡 Pro Tip:</strong> The floating 🍳 button appears on every website you visit. Click it to access ChefAI from anywhere!
        </div>
        
        <div class="note success">
            <strong>✅ Ready to Cook:</strong> The extension is now fully functional with real LLM integration. Start generating amazing recipes with AI!
        </div>
        
        <div class="demo-card">
            <h2>🛠️ Technical Implementation</h2>
            <p>This extension features:</p>
            <ul style="text-align: left; max-width: 600px; margin: 0 auto;">
                <li><strong>Real API Integration:</strong> Direct calls to LLM providers with proper authentication</li>
                <li><strong>Content Script Injection:</strong> Sidebar interface injected into any website</li>
                <li><strong>Chrome Storage API:</strong> Persistent configuration and recipe storage</li>
                <li><strong>Glassmorphism UI:</strong> Modern, beautiful interface with backdrop blur effects</li>
                <li><strong>Smart Device Services:</strong> Integration with kitchen appliances and automation</li>
                <li><strong>Voice Control:</strong> Natural language commands for hands-free cooking</li>
                <li><strong>Advanced Analytics:</strong> Comprehensive cooking insights and recommendations</li>
            </ul>
        </div>
        
        <div class="demo-card">
            <h2>🎯 Next Steps</h2>
            <p>To get your API keys:</p>
            <ul style="text-align: left; max-width: 500px; margin: 0 auto;">
                <li><strong>OpenRouter:</strong> Visit <code>openrouter.ai</code> and sign up</li>
                <li><strong>Google Gemini:</strong> Go to <code>ai.google.dev</code> and get API key</li>
                <li><strong>OpenAI:</strong> Visit <code>platform.openai.com</code> and create API key</li>
                <li><strong>Anthropic:</strong> Go to <code>console.anthropic.com</code> and get API key</li>
            </ul>
        </div>
    </div>
    
    <script>
        // Demo floating button interaction
        document.querySelector('.floating-button-demo').addEventListener('click', function() {
            alert('🍳 ChefAI Extension Demo\n\nThis floating button appears on every website when the extension is installed.\n\nClick it to open the AI recipe generation sidebar!\n\nTo see the real extension in action, load it in Chrome Developer Mode.');
        });
        
        // Add some interactive effects
        document.querySelectorAll('.feature-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    </script>
</body>
</html>
