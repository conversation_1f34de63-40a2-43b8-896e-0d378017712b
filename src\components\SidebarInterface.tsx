import React, { useState, useEffect } from 'react';
import { LLMService, RecipeGenerationRequest } from '../services/LLMService';
import { RecipeService } from '../services/RecipeService';
import { useI18n } from '../hooks/useI18n';
import { Recipe } from '../types';

interface SidebarInterfaceProps {
  isVisible: boolean;
  onClose: () => void;
  onRecipeGenerated: (recipe: Recipe) => void;
}

export const SidebarInterface: React.FC<SidebarInterfaceProps> = ({
  isVisible,
  onClose,
  onRecipeGenerated
}) => {
  const { t } = useI18n();
  const [activeTab, setActiveTab] = useState<'generate' | 'settings' | 'history'>('generate');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationRequest, setGenerationRequest] = useState<RecipeGenerationRequest>({
    ingredients: [],
    servings: 4,
    cookingTime: 30,
    difficulty: 'Medium',
    mealType: 'dinner'
  });
  const [ingredientInput, setIngredientInput] = useState('');
  const [recentRecipes, setRecentRecipes] = useState<Recipe[]>([]);
  const [llmConfig, setLlmConfig] = useState<any>(null);

  const llmService = LLMService.getInstance();
  const recipeService = RecipeService.getInstance();

  useEffect(() => {
    if (isVisible) {
      loadConfiguration();
      loadRecentRecipes();
    }
  }, [isVisible]);

  const loadConfiguration = async () => {
    const config = llmService.getConfiguration();
    setLlmConfig(config);
  };

  const loadRecentRecipes = async () => {
    try {
      const recipes = await recipeService.getRecipes();
      const aiRecipes = recipes.filter(r => r.source === 'AI Generated').slice(0, 5);
      setRecentRecipes(aiRecipes);
    } catch (error) {
      console.error('Failed to load recent recipes:', error);
    }
  };

  const handleAddIngredient = () => {
    if (ingredientInput.trim() && !generationRequest.ingredients?.includes(ingredientInput.trim())) {
      setGenerationRequest(prev => ({
        ...prev,
        ingredients: [...(prev.ingredients || []), ingredientInput.trim()]
      }));
      setIngredientInput('');
    }
  };

  const handleRemoveIngredient = (ingredient: string) => {
    setGenerationRequest(prev => ({
      ...prev,
      ingredients: prev.ingredients?.filter(ing => ing !== ingredient) || []
    }));
  };

  const handleGenerateRecipe = async () => {
    if (!llmConfig || !llmConfig.apiKey) {
      alert('Please configure your LLM API key in settings first.');
      setActiveTab('settings');
      return;
    }

    setIsGenerating(true);
    try {
      const response = await llmService.generateRecipe(generationRequest);
      
      if (response.success && response.data) {
        const recipe = response.data as Recipe;
        
        // Save the generated recipe
        await recipeService.saveRecipe(recipe);
        
        // Notify parent component
        onRecipeGenerated(recipe);
        
        // Refresh recent recipes
        await loadRecentRecipes();
        
        // Show success message
        alert('Recipe generated successfully!');
      } else {
        alert(`Failed to generate recipe: ${response.error}`);
      }
    } catch (error) {
      console.error('Recipe generation failed:', error);
      alert('An error occurred while generating the recipe. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleAddIngredient();
    }
  };

  if (!isVisible) return null;

  return (
    <div className="sidebar-interface">
      <div className="sidebar-overlay" onClick={onClose}></div>
      <div className="sidebar-content">
        <div className="sidebar-header">
          <h2>🍳 ChefAI Assistant</h2>
          <button className="close-btn" onClick={onClose}>×</button>
        </div>

        {/* Navigation Tabs */}
        <div className="sidebar-tabs">
          <button 
            className={`tab ${activeTab === 'generate' ? 'active' : ''}`}
            onClick={() => setActiveTab('generate')}
          >
            <span className="tab-icon">✨</span>
            Generate Recipe
          </button>
          <button 
            className={`tab ${activeTab === 'settings' ? 'active' : ''}`}
            onClick={() => setActiveTab('settings')}
          >
            <span className="tab-icon">⚙️</span>
            LLM Settings
          </button>
          <button 
            className={`tab ${activeTab === 'history' ? 'active' : ''}`}
            onClick={() => setActiveTab('history')}
          >
            <span className="tab-icon">📚</span>
            Recent Recipes
          </button>
        </div>

        {/* Generate Recipe Tab */}
        {activeTab === 'generate' && (
          <div className="tab-content generate-tab">
            <div className="form-section">
              <h3>🥘 Recipe Requirements</h3>
              
              {/* Ingredients Input */}
              <div className="input-group">
                <label>Available Ingredients</label>
                <div className="ingredient-input">
                  <input
                    type="text"
                    value={ingredientInput}
                    onChange={(e) => setIngredientInput(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="Enter an ingredient..."
                    className="glass-input"
                  />
                  <button 
                    onClick={handleAddIngredient}
                    className="add-btn glass-button"
                    disabled={!ingredientInput.trim()}
                  >
                    Add
                  </button>
                </div>
                
                {/* Ingredients List */}
                {generationRequest.ingredients && generationRequest.ingredients.length > 0 && (
                  <div className="ingredients-list">
                    {generationRequest.ingredients.map((ingredient, index) => (
                      <span key={index} className="ingredient-tag">
                        {ingredient}
                        <button 
                          onClick={() => handleRemoveIngredient(ingredient)}
                          className="remove-ingredient"
                        >
                          ×
                        </button>
                      </span>
                    ))}
                  </div>
                )}
              </div>

              {/* Recipe Parameters */}
              <div className="parameters-grid">
                <div className="input-group">
                  <label>Cuisine Type</label>
                  <select 
                    value={generationRequest.cuisineType || ''}
                    onChange={(e) => setGenerationRequest(prev => ({ ...prev, cuisineType: e.target.value }))}
                    className="glass-select"
                  >
                    <option value="">Any Cuisine</option>
                    <option value="Italian">Italian</option>
                    <option value="Asian">Asian</option>
                    <option value="Mexican">Mexican</option>
                    <option value="Mediterranean">Mediterranean</option>
                    <option value="Indian">Indian</option>
                    <option value="French">French</option>
                    <option value="American">American</option>
                    <option value="Thai">Thai</option>
                    <option value="Japanese">Japanese</option>
                    <option value="Middle Eastern">Middle Eastern</option>
                  </select>
                </div>

                <div className="input-group">
                  <label>Meal Type</label>
                  <select 
                    value={generationRequest.mealType || 'dinner'}
                    onChange={(e) => setGenerationRequest(prev => ({ ...prev, mealType: e.target.value }))}
                    className="glass-select"
                  >
                    <option value="breakfast">Breakfast</option>
                    <option value="lunch">Lunch</option>
                    <option value="dinner">Dinner</option>
                    <option value="snack">Snack</option>
                    <option value="dessert">Dessert</option>
                    <option value="appetizer">Appetizer</option>
                  </select>
                </div>

                <div className="input-group">
                  <label>Servings</label>
                  <input
                    type="number"
                    min="1"
                    max="12"
                    value={generationRequest.servings || 4}
                    onChange={(e) => setGenerationRequest(prev => ({ ...prev, servings: parseInt(e.target.value) }))}
                    className="glass-input"
                  />
                </div>

                <div className="input-group">
                  <label>Max Cooking Time (minutes)</label>
                  <input
                    type="number"
                    min="5"
                    max="240"
                    value={generationRequest.cookingTime || 30}
                    onChange={(e) => setGenerationRequest(prev => ({ ...prev, cookingTime: parseInt(e.target.value) }))}
                    className="glass-input"
                  />
                </div>

                <div className="input-group">
                  <label>Difficulty</label>
                  <select 
                    value={generationRequest.difficulty || 'Medium'}
                    onChange={(e) => setGenerationRequest(prev => ({ ...prev, difficulty: e.target.value }))}
                    className="glass-select"
                  >
                    <option value="Easy">Easy</option>
                    <option value="Medium">Medium</option>
                    <option value="Hard">Hard</option>
                  </select>
                </div>
              </div>

              {/* Dietary Restrictions */}
              <div className="input-group">
                <label>Dietary Restrictions</label>
                <div className="dietary-options">
                  {['Vegetarian', 'Vegan', 'Gluten-Free', 'Dairy-Free', 'Keto', 'Low-Carb', 'Paleo'].map(restriction => (
                    <label key={restriction} className="checkbox-label">
                      <input
                        type="checkbox"
                        checked={generationRequest.dietaryRestrictions?.includes(restriction) || false}
                        onChange={(e) => {
                          const restrictions = generationRequest.dietaryRestrictions || [];
                          if (e.target.checked) {
                            setGenerationRequest(prev => ({
                              ...prev,
                              dietaryRestrictions: [...restrictions, restriction]
                            }));
                          } else {
                            setGenerationRequest(prev => ({
                              ...prev,
                              dietaryRestrictions: restrictions.filter(r => r !== restriction)
                            }));
                          }
                        }}
                      />
                      {restriction}
                    </label>
                  ))}
                </div>
              </div>

              {/* Custom Prompt */}
              <div className="input-group">
                <label>Additional Requirements (Optional)</label>
                <textarea
                  value={generationRequest.customPrompt || ''}
                  onChange={(e) => setGenerationRequest(prev => ({ ...prev, customPrompt: e.target.value }))}
                  placeholder="Any specific requirements, cooking methods, or preferences..."
                  className="glass-textarea"
                  rows={3}
                />
              </div>

              {/* Generate Button */}
              <button 
                onClick={handleGenerateRecipe}
                disabled={isGenerating || !llmConfig?.apiKey}
                className="generate-btn glass-button primary"
              >
                {isGenerating ? (
                  <>
                    <span className="spinner"></span>
                    Generating Recipe...
                  </>
                ) : (
                  <>
                    <span className="btn-icon">✨</span>
                    Generate Recipe with AI
                  </>
                )}
              </button>

              {!llmConfig?.apiKey && (
                <div className="warning-message">
                  ⚠️ Please configure your LLM API key in settings to generate recipes.
                </div>
              )}
            </div>
          </div>
        )}

        {/* Settings Tab */}
        {activeTab === 'settings' && (
          <div className="tab-content settings-tab">
            <LLMSettingsPanel onConfigUpdate={loadConfiguration} />
          </div>
        )}

        {/* History Tab */}
        {activeTab === 'history' && (
          <div className="tab-content history-tab">
            <h3>📚 Recent AI Recipes</h3>
            {recentRecipes.length === 0 ? (
              <div className="empty-state">
                <p>No AI-generated recipes yet.</p>
                <p>Generate your first recipe to see it here!</p>
              </div>
            ) : (
              <div className="recipes-list">
                {recentRecipes.map((recipe) => (
                  <div key={recipe.id} className="recipe-item glass-card">
                    <h4>{recipe.title}</h4>
                    <p>{recipe.description}</p>
                    <div className="recipe-meta">
                      <span>🍽️ {recipe.servings} servings</span>
                      <span>⏱️ {recipe.cookTime} min</span>
                      <span>📊 {recipe.difficulty}</span>
                    </div>
                    <button 
                      onClick={() => onRecipeGenerated(recipe)}
                      className="view-recipe-btn glass-button"
                    >
                      View Recipe
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

// LLM Settings Panel Component
const LLMSettingsPanel: React.FC<{ onConfigUpdate: () => void }> = ({ onConfigUpdate }) => {
  const [config, setConfig] = useState<any>(null);
  const [providers, setProviders] = useState<any[]>([]);
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [testResult, setTestResult] = useState<string>('');

  const llmService = LLMService.getInstance();

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = () => {
    const currentConfig = llmService.getConfiguration();
    const availableProviders = llmService.getProviders();
    
    setConfig(currentConfig || {
      provider: 'openrouter',
      model: 'anthropic/claude-3.5-sonnet',
      apiKey: '',
      temperature: 0.7,
      maxTokens: 2048,
      systemPrompt: 'You are ChefAI, an expert culinary assistant.'
    });
    setProviders(availableProviders);
  };

  const handleSaveConfig = async () => {
    try {
      await llmService.saveConfiguration(config);
      onConfigUpdate();
      alert('Configuration saved successfully!');
    } catch (error) {
      alert('Failed to save configuration.');
    }
  };

  const handleTestConnection = async () => {
    setIsTestingConnection(true);
    setTestResult('');
    
    try {
      const result = await llmService.testConnection();
      setTestResult(result.success ? '✅ Connection successful!' : `❌ ${result.error}`);
    } catch (error) {
      setTestResult(`❌ Connection failed: ${error.message}`);
    } finally {
      setIsTestingConnection(false);
    }
  };

  if (!config) return <div>Loading...</div>;

  const selectedProvider = providers.find(p => p.id === config.provider);
  const availableModels = selectedProvider?.models || [];

  return (
    <div className="llm-settings">
      <h3>🤖 LLM Configuration</h3>
      
      <div className="settings-form">
        <div className="input-group">
          <label>AI Provider</label>
          <select 
            value={config.provider}
            onChange={(e) => {
              const newProvider = providers.find(p => p.id === e.target.value);
              setConfig(prev => ({
                ...prev,
                provider: e.target.value,
                model: newProvider?.models[0]?.id || ''
              }));
            }}
            className="glass-select"
          >
            {providers.map(provider => (
              <option key={provider.id} value={provider.id}>
                {provider.name}
              </option>
            ))}
          </select>
          {selectedProvider && (
            <small className="provider-description">{selectedProvider.description}</small>
          )}
        </div>

        <div className="input-group">
          <label>Model</label>
          <select 
            value={config.model}
            onChange={(e) => setConfig(prev => ({ ...prev, model: e.target.value }))}
            className="glass-select"
          >
            {availableModels.map(model => (
              <option key={model.id} value={model.id}>
                {model.name} - ${model.costPer1kTokens}/1k tokens
              </option>
            ))}
          </select>
        </div>

        <div className="input-group">
          <label>API Key</label>
          <input
            type="password"
            value={config.apiKey}
            onChange={(e) => setConfig(prev => ({ ...prev, apiKey: e.target.value }))}
            placeholder="Enter your API key..."
            className="glass-input"
          />
          <small>Get your API key from the provider's website</small>
        </div>

        <div className="parameters-row">
          <div className="input-group">
            <label>Temperature ({config.temperature})</label>
            <input
              type="range"
              min="0"
              max="2"
              step="0.1"
              value={config.temperature}
              onChange={(e) => setConfig(prev => ({ ...prev, temperature: parseFloat(e.target.value) }))}
              className="glass-range"
            />
            <small>Higher = more creative, Lower = more focused</small>
          </div>

          <div className="input-group">
            <label>Max Tokens</label>
            <input
              type="number"
              min="100"
              max="8192"
              value={config.maxTokens}
              onChange={(e) => setConfig(prev => ({ ...prev, maxTokens: parseInt(e.target.value) }))}
              className="glass-input"
            />
          </div>
        </div>

        <div className="input-group">
          <label>System Prompt</label>
          <textarea
            value={config.systemPrompt}
            onChange={(e) => setConfig(prev => ({ ...prev, systemPrompt: e.target.value }))}
            className="glass-textarea"
            rows={4}
          />
        </div>

        <div className="settings-actions">
          <button 
            onClick={handleTestConnection}
            disabled={isTestingConnection || !config.apiKey}
            className="test-btn glass-button"
          >
            {isTestingConnection ? 'Testing...' : 'Test Connection'}
          </button>
          
          <button 
            onClick={handleSaveConfig}
            className="save-btn glass-button primary"
          >
            Save Configuration
          </button>
        </div>

        {testResult && (
          <div className={`test-result ${testResult.includes('✅') ? 'success' : 'error'}`}>
            {testResult}
          </div>
        )}
      </div>
    </div>
  );
};
