import { ThemeSettings } from '../types';

export class ThemeService {
  private static instance: ThemeService;
  private currentTheme: ThemeSettings = {
    mode: 'auto',
    primaryColor: '#667eea',
    accentColor: '#f093fb',
    glassmorphismIntensity: 50,
    fontSize: 'medium',
    artMode: false
  };

  private constructor() {
    this.loadTheme();
    this.setupSystemThemeListener();
  }

  public static getInstance(): ThemeService {
    if (!ThemeService.instance) {
      ThemeService.instance = new ThemeService();
    }
    return ThemeService.instance;
  }

  private async loadTheme(): Promise<void> {
    try {
      const result = await chrome.storage.sync.get(['themeSettings']);
      if (result.themeSettings) {
        this.currentTheme = { ...this.currentTheme, ...result.themeSettings };
      }
      this.applyTheme();
    } catch (error) {
      console.error('Failed to load theme:', error);
    }
  }

  public async updateTheme(updates: Partial<ThemeSettings>): Promise<void> {
    try {
      this.currentTheme = { ...this.currentTheme, ...updates };
      await chrome.storage.sync.set({ themeSettings: this.currentTheme });
      this.applyTheme();
    } catch (error) {
      console.error('Failed to update theme:', error);
    }
  }

  public getCurrentTheme(): ThemeSettings {
    return { ...this.currentTheme };
  }

  private applyTheme(): void {
    const root = document.documentElement;
    
    // Apply color scheme
    this.applyColorScheme();
    
    // Apply custom colors
    root.style.setProperty('--primary-color', this.currentTheme.primaryColor);
    root.style.setProperty('--accent-color', this.currentTheme.accentColor);
    
    // Apply glassmorphism intensity
    this.applyGlassmorphismIntensity();
    
    // Apply font size
    this.applyFontSize();
    
    // Apply art mode
    this.applyArtMode();
    
    // Set theme attribute for CSS targeting
    root.setAttribute('data-theme', this.getEffectiveTheme());
    root.setAttribute('data-art-mode', this.currentTheme.artMode.toString());
  }

  private applyColorScheme(): void {
    const effectiveTheme = this.getEffectiveTheme();
    const root = document.documentElement;
    
    if (effectiveTheme === 'dark') {
      root.style.setProperty('--glass-bg', 'rgba(0, 0, 0, 0.2)');
      root.style.setProperty('--glass-bg-dark', 'rgba(255, 255, 255, 0.05)');
      root.style.setProperty('--glass-border', 'rgba(255, 255, 255, 0.1)');
      root.style.setProperty('--glass-shadow', '0 8px 32px 0 rgba(0, 0, 0, 0.5)');
    } else {
      root.style.setProperty('--glass-bg', 'rgba(255, 255, 255, 0.1)');
      root.style.setProperty('--glass-bg-dark', 'rgba(0, 0, 0, 0.1)');
      root.style.setProperty('--glass-border', 'rgba(255, 255, 255, 0.2)');
      root.style.setProperty('--glass-shadow', '0 8px 32px 0 rgba(31, 38, 135, 0.37)');
    }
  }

  private applyGlassmorphismIntensity(): void {
    const intensity = this.currentTheme.glassmorphismIntensity / 100;
    const root = document.documentElement;
    
    // Adjust backdrop blur based on intensity
    const blurValue = Math.round(4 + (intensity * 12)); // 4px to 16px
    root.style.setProperty('--glass-backdrop', `blur(${blurValue}px)`);
    
    // Adjust opacity based on intensity
    const baseOpacity = 0.05 + (intensity * 0.15); // 0.05 to 0.2
    root.style.setProperty('--glass-bg', `rgba(255, 255, 255, ${baseOpacity})`);
    
    // Adjust border opacity
    const borderOpacity = 0.1 + (intensity * 0.2); // 0.1 to 0.3
    root.style.setProperty('--glass-border', `rgba(255, 255, 255, ${borderOpacity})`);
  }

  private applyFontSize(): void {
    const root = document.documentElement;
    const sizeMap = {
      small: {
        base: '0.875rem',
        sm: '0.75rem',
        lg: '1rem',
        xl: '1.125rem',
        '2xl': '1.25rem',
        '3xl': '1.5rem'
      },
      medium: {
        base: '1rem',
        sm: '0.875rem',
        lg: '1.125rem',
        xl: '1.25rem',
        '2xl': '1.5rem',
        '3xl': '1.875rem'
      },
      large: {
        base: '1.125rem',
        sm: '1rem',
        lg: '1.25rem',
        xl: '1.375rem',
        '2xl': '1.625rem',
        '3xl': '2rem'
      }
    };

    const sizes = sizeMap[this.currentTheme.fontSize];
    Object.entries(sizes).forEach(([key, value]) => {
      root.style.setProperty(`--font-size-${key}`, value);
    });
  }

  private applyArtMode(): void {
    const body = document.body;
    
    if (this.currentTheme.artMode) {
      body.classList.add('art-mode');
      this.applyArtModeEffects();
    } else {
      body.classList.remove('art-mode');
      this.removeArtModeEffects();
    }
  }

  private applyArtModeEffects(): void {
    const root = document.documentElement;
    
    // Enhanced gradients for art mode
    const artGradient = `linear-gradient(135deg, 
      ${this.currentTheme.primaryColor}15 0%,
      ${this.currentTheme.accentColor}15 25%,
      rgba(240, 147, 251, 0.1) 50%,
      rgba(249, 168, 212, 0.1) 75%,
      rgba(196, 181, 253, 0.1) 100%
    )`;
    
    root.style.setProperty('--art-mode-bg', artGradient);
    
    // Add subtle animations
    root.style.setProperty('--art-mode-animation', 'artModeFloat 6s ease-in-out infinite');
  }

  private removeArtModeEffects(): void {
    const root = document.documentElement;
    root.style.removeProperty('--art-mode-bg');
    root.style.removeProperty('--art-mode-animation');
  }

  private getEffectiveTheme(): 'light' | 'dark' {
    if (this.currentTheme.mode === 'auto') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }
    return this.currentTheme.mode as 'light' | 'dark';
  }

  private setupSystemThemeListener(): void {
    if (this.currentTheme.mode === 'auto') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      mediaQuery.addEventListener('change', () => {
        if (this.currentTheme.mode === 'auto') {
          this.applyTheme();
        }
      });
    }
  }

  public generateColorPalette(baseColor: string): string[] {
    // Generate a complementary color palette based on the base color
    const hsl = this.hexToHsl(baseColor);
    const palette: string[] = [];
    
    // Generate variations
    for (let i = 0; i < 5; i++) {
      const hue = (hsl.h + (i * 72)) % 360; // 72 degrees apart for pentadic harmony
      const saturation = Math.max(0.3, hsl.s - (i * 0.1));
      const lightness = Math.min(0.8, hsl.l + (i * 0.05));
      
      palette.push(this.hslToHex(hue, saturation, lightness));
    }
    
    return palette;
  }

  private hexToHsl(hex: string): { h: number; s: number; l: number } {
    const r = parseInt(hex.slice(1, 3), 16) / 255;
    const g = parseInt(hex.slice(3, 5), 16) / 255;
    const b = parseInt(hex.slice(5, 7), 16) / 255;

    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h = 0;
    let s = 0;
    const l = (max + min) / 2;

    if (max !== min) {
      const d = max - min;
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
      
      switch (max) {
        case r: h = (g - b) / d + (g < b ? 6 : 0); break;
        case g: h = (b - r) / d + 2; break;
        case b: h = (r - g) / d + 4; break;
      }
      h /= 6;
    }

    return { h: h * 360, s, l };
  }

  private hslToHex(h: number, s: number, l: number): string {
    h /= 360;
    const a = s * Math.min(l, 1 - l);
    const f = (n: number) => {
      const k = (n + h * 12) % 12;
      const color = l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);
      return Math.round(255 * color).toString(16).padStart(2, '0');
    };
    return `#${f(0)}${f(8)}${f(4)}`;
  }

  public getPresetThemes(): Array<{ name: string; theme: Partial<ThemeSettings> }> {
    return [
      {
        name: 'Ocean Breeze',
        theme: {
          primaryColor: '#0ea5e9',
          accentColor: '#06b6d4',
          glassmorphismIntensity: 60
        }
      },
      {
        name: 'Sunset Glow',
        theme: {
          primaryColor: '#f59e0b',
          accentColor: '#f97316',
          glassmorphismIntensity: 70
        }
      },
      {
        name: 'Forest Green',
        theme: {
          primaryColor: '#10b981',
          accentColor: '#059669',
          glassmorphismIntensity: 55
        }
      },
      {
        name: 'Royal Purple',
        theme: {
          primaryColor: '#8b5cf6',
          accentColor: '#a855f7',
          glassmorphismIntensity: 65
        }
      },
      {
        name: 'Cherry Blossom',
        theme: {
          primaryColor: '#ec4899',
          accentColor: '#f472b6',
          glassmorphismIntensity: 50
        }
      }
    ];
  }

  public exportTheme(): string {
    return JSON.stringify(this.currentTheme, null, 2);
  }

  public async importTheme(themeJson: string): Promise<boolean> {
    try {
      const theme = JSON.parse(themeJson) as ThemeSettings;
      await this.updateTheme(theme);
      return true;
    } catch (error) {
      console.error('Failed to import theme:', error);
      return false;
    }
  }
}
