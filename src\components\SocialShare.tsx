import React, { useState } from 'react';
import { Recipe } from '../types';
import { SocialService } from '../services/SocialService';
import { useI18n } from '../hooks/useI18n';

interface SocialShareProps {
  recipe: Recipe;
  onClose?: () => void;
}

export const SocialShare: React.FC<SocialShareProps> = ({ recipe, onClose }) => {
  const { t } = useI18n();
  const [isSharing, setIsSharing] = useState(false);
  const [shareSuccess, setShareSuccess] = useState<string | null>(null);
  
  const socialService = SocialService.getInstance();

  const platforms = [
    {
      id: 'facebook',
      name: 'Facebook',
      icon: '📘',
      color: '#1877f2',
      description: t('social.facebook_description')
    },
    {
      id: 'twitter',
      name: 'Twitter',
      icon: '🐦',
      color: '#1da1f2',
      description: t('social.twitter_description')
    },
    {
      id: 'instagram',
      name: 'Instagram',
      icon: '📷',
      color: '#e4405f',
      description: t('social.instagram_description')
    },
    {
      id: 'pinterest',
      name: 'Pinterest',
      icon: '📌',
      color: '#bd081c',
      description: t('social.pinterest_description')
    },
    {
      id: 'whatsapp',
      name: 'WhatsApp',
      icon: '💬',
      color: '#25d366',
      description: t('social.whatsapp_description')
    }
  ];

  const handleShare = async (platform: string) => {
    try {
      setIsSharing(true);
      setShareSuccess(null);
      
      const success = await socialService.shareRecipe(recipe, platform as any);
      
      if (success) {
        setShareSuccess(platform);
        setTimeout(() => setShareSuccess(null), 3000);
      }
    } catch (error) {
      console.error('Failed to share recipe:', error);
    } finally {
      setIsSharing(false);
    }
  };

  const handleCopyLink = async () => {
    try {
      const baseUrl = chrome.runtime.getURL('options.html');
      const recipeUrl = `${baseUrl}#recipe/${recipe.id}`;
      await navigator.clipboard.writeText(recipeUrl);
      setShareSuccess('clipboard');
      setTimeout(() => setShareSuccess(null), 3000);
    } catch (error) {
      console.error('Failed to copy link:', error);
    }
  };

  const generateSharePreview = () => {
    return {
      title: recipe.title,
      description: recipe.description || t('social.default_description'),
      image: recipe.image || '/icons/icon128.png',
      stats: `⏱️ ${recipe.prepTime + recipe.cookTime} min | 👥 ${recipe.servings} servings | 📊 ${recipe.difficulty}`
    };
  };

  const preview = generateSharePreview();

  return (
    <div className="social-share-modal glass-modal-overlay">
      <div className="social-share-container glass-modal">
        <div className="social-share-header">
          <h2>{t('social.share_recipe')}</h2>
          {onClose && (
            <button className="modal-close" onClick={onClose}>×</button>
          )}
        </div>

        {/* Recipe Preview */}
        <div className="share-preview glass-card-sm">
          <div className="preview-image">
            {preview.image ? (
              <img src={preview.image} alt={preview.title} />
            ) : (
              <div className="preview-placeholder">🍳</div>
            )}
          </div>
          <div className="preview-content">
            <h3>{preview.title}</h3>
            <p>{preview.description}</p>
            <div className="preview-stats">{preview.stats}</div>
          </div>
        </div>

        {/* Success Message */}
        {shareSuccess && (
          <div className="share-success glass-card-sm">
            <span className="success-icon">✅</span>
            <span>
              {shareSuccess === 'clipboard' 
                ? t('social.link_copied') 
                : t('social.shared_successfully', { platform: shareSuccess })}
            </span>
          </div>
        )}

        {/* Social Platforms */}
        <div className="social-platforms">
          <h3>{t('social.choose_platform')}</h3>
          <div className="platforms-grid">
            {platforms.map((platform) => (
              <button
                key={platform.id}
                className="platform-button glass-card-sm"
                onClick={() => handleShare(platform.id)}
                disabled={isSharing}
                style={{ borderColor: platform.color }}
              >
                <div className="platform-icon" style={{ color: platform.color }}>
                  {platform.icon}
                </div>
                <div className="platform-info">
                  <div className="platform-name">{platform.name}</div>
                  <div className="platform-description">{platform.description}</div>
                </div>
                {isSharing && (
                  <div className="platform-loading">⏳</div>
                )}
              </button>
            ))}
          </div>
        </div>

        {/* Additional Options */}
        <div className="share-options">
          <button 
            className="glass-button copy-link-button"
            onClick={handleCopyLink}
          >
            🔗 {t('social.copy_link')}
          </button>
          
          <button 
            className="glass-button qr-code-button"
            onClick={() => {/* Generate QR code */}}
          >
            📱 {t('social.generate_qr')}
          </button>
        </div>

        {/* Recipe Stats */}
        <div className="recipe-stats glass-card-sm">
          <h4>{t('social.recipe_stats')}</h4>
          <div className="stats-grid">
            <div className="stat-item">
              <span className="stat-icon">👀</span>
              <span className="stat-label">{t('social.views')}</span>
              <span className="stat-value">0</span>
            </div>
            <div className="stat-item">
              <span className="stat-icon">📤</span>
              <span className="stat-label">{t('social.shares')}</span>
              <span className="stat-value">0</span>
            </div>
            <div className="stat-item">
              <span className="stat-icon">💾</span>
              <span className="stat-label">{t('social.saves')}</span>
              <span className="stat-value">0</span>
            </div>
            <div className="stat-item">
              <span className="stat-icon">⭐</span>
              <span className="stat-label">{t('social.rating')}</span>
              <span className="stat-value">{recipe.rating.toFixed(1)}</span>
            </div>
          </div>
        </div>

        {/* Hashtag Suggestions */}
        <div className="hashtag-suggestions glass-card-sm">
          <h4>{t('social.suggested_hashtags')}</h4>
          <div className="hashtags">
            {['ChefAI', 'Recipe', 'Cooking', 'AI', recipe.cuisine, recipe.difficulty, ...recipe.tags]
              .filter(Boolean)
              .slice(0, 8)
              .map((tag, index) => (
                <span key={index} className="hashtag">#{tag}</span>
              ))}
          </div>
        </div>
      </div>
    </div>
  );
};
