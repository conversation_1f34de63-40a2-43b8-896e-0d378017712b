import { Recipe, RecipeChallenge, ChallengeSubmission, Comment } from '../types';

export class SocialService {
  private static instance: SocialService;

  private constructor() {}

  public static getInstance(): SocialService {
    if (!SocialService.instance) {
      SocialService.instance = new SocialService();
    }
    return SocialService.instance;
  }

  // Recipe Sharing
  public async shareRecipe(recipe: Recipe, platform: 'facebook' | 'twitter' | 'instagram' | 'pinterest' | 'whatsapp'): Promise<boolean> {
    try {
      const shareData = this.prepareShareData(recipe);
      
      switch (platform) {
        case 'facebook':
          return this.shareToFacebook(shareData);
        case 'twitter':
          return this.shareToTwitter(shareData);
        case 'instagram':
          return this.shareToInstagram(shareData);
        case 'pinterest':
          return this.shareToPinterest(shareData);
        case 'whatsapp':
          return this.shareToWhatsApp(shareData);
        default:
          throw new Error('Unsupported platform');
      }
    } catch (error) {
      console.error('Failed to share recipe:', error);
      return false;
    }
  }

  private prepareShareData(recipe: Recipe) {
    const baseUrl = chrome.runtime.getURL('options.html');
    const recipeUrl = `${baseUrl}#recipe/${recipe.id}`;
    
    return {
      title: `🍳 ${recipe.title}`,
      description: recipe.description || 'Check out this amazing recipe I generated with ChefAI!',
      url: recipeUrl,
      image: recipe.image,
      hashtags: ['ChefAI', 'Recipe', 'Cooking', 'AI', ...recipe.tags],
      ingredients: recipe.ingredients.slice(0, 5).map(ing => ing.name),
      cookTime: recipe.prepTime + recipe.cookTime,
      servings: recipe.servings
    };
  }

  private async shareToFacebook(shareData: any): Promise<boolean> {
    const url = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareData.url)}&quote=${encodeURIComponent(shareData.title + ' - ' + shareData.description)}`;
    window.open(url, '_blank', 'width=600,height=400');
    return true;
  }

  private async shareToTwitter(shareData: any): Promise<boolean> {
    const text = `${shareData.title}\n\n${shareData.description}\n\n⏱️ ${shareData.cookTime} min | 👥 ${shareData.servings} servings\n\n${shareData.hashtags.map(tag => `#${tag}`).join(' ')}\n\n${shareData.url}`;
    const url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}`;
    window.open(url, '_blank', 'width=600,height=400');
    return true;
  }

  private async shareToInstagram(shareData: any): Promise<boolean> {
    // Instagram doesn't support direct URL sharing, so we copy to clipboard
    const text = `${shareData.title}\n\n${shareData.description}\n\nIngredients:\n${shareData.ingredients.map(ing => `• ${ing}`).join('\n')}\n\n⏱️ ${shareData.cookTime} min | 👥 ${shareData.servings} servings\n\n${shareData.hashtags.map(tag => `#${tag}`).join(' ')}\n\nGenerated with ChefAI Chrome Extension\n${shareData.url}`;
    
    await navigator.clipboard.writeText(text);
    alert('Recipe details copied to clipboard! You can now paste it in your Instagram post.');
    return true;
  }

  private async shareToPinterest(shareData: any): Promise<boolean> {
    const url = `https://pinterest.com/pin/create/button/?url=${encodeURIComponent(shareData.url)}&media=${encodeURIComponent(shareData.image || '')}&description=${encodeURIComponent(shareData.title + ' - ' + shareData.description)}`;
    window.open(url, '_blank', 'width=600,height=400');
    return true;
  }

  private async shareToWhatsApp(shareData: any): Promise<boolean> {
    const text = `🍳 *${shareData.title}*\n\n${shareData.description}\n\n*Ingredients:*\n${shareData.ingredients.map(ing => `• ${ing}`).join('\n')}\n\n⏱️ ${shareData.cookTime} min | 👥 ${shareData.servings} servings\n\nTry this recipe: ${shareData.url}`;
    const url = `https://wa.me/?text=${encodeURIComponent(text)}`;
    window.open(url, '_blank');
    return true;
  }

  // Recipe Challenges
  public async getChallenges(): Promise<RecipeChallenge[]> {
    // Mock data for demonstration
    return [
      {
        id: 'challenge-1',
        title: 'Summer Refreshing Challenge',
        description: 'Create the most refreshing summer recipe using seasonal ingredients',
        ingredients: ['watermelon', 'mint', 'lime', 'cucumber'],
        startDate: new Date('2024-07-01'),
        endDate: new Date('2024-07-31'),
        participants: 156,
        submissions: [],
        prizes: ['$100 Gift Card', 'Featured Recipe', 'ChefAI Premium']
      },
      {
        id: 'challenge-2',
        title: 'Healthy Breakfast Bowl',
        description: 'Design a nutritious and delicious breakfast bowl',
        ingredients: ['oats', 'berries', 'nuts', 'yogurt'],
        startDate: new Date('2024-07-15'),
        endDate: new Date('2024-08-15'),
        participants: 89,
        submissions: [],
        prizes: ['Kitchen Equipment Set', 'Recipe Book', 'ChefAI Badge']
      }
    ];
  }

  public async submitToChallenge(challengeId: string, recipeId: string): Promise<boolean> {
    try {
      // In a real implementation, this would submit to a backend service
      const submission: ChallengeSubmission = {
        id: `submission-${Date.now()}`,
        userId: 'current-user',
        recipeId,
        submittedAt: new Date(),
        votes: 0,
        comments: []
      };

      // Store locally for demo
      const submissions = await this.getChallengeSubmissions(challengeId);
      submissions.push(submission);
      await chrome.storage.local.set({ [`challenge-${challengeId}-submissions`]: submissions });

      return true;
    } catch (error) {
      console.error('Failed to submit to challenge:', error);
      return false;
    }
  }

  public async getChallengeSubmissions(challengeId: string): Promise<ChallengeSubmission[]> {
    try {
      const result = await chrome.storage.local.get([`challenge-${challengeId}-submissions`]);
      return result[`challenge-${challengeId}-submissions`] || [];
    } catch (error) {
      console.error('Failed to get challenge submissions:', error);
      return [];
    }
  }

  public async voteForSubmission(submissionId: string, vote: 'up' | 'down'): Promise<boolean> {
    try {
      // In a real implementation, this would update the backend
      // For demo, we'll just track locally
      const votes = await this.getUserVotes();
      votes[submissionId] = vote;
      await chrome.storage.local.set({ userVotes: votes });
      return true;
    } catch (error) {
      console.error('Failed to vote for submission:', error);
      return false;
    }
  }

  private async getUserVotes(): Promise<{ [submissionId: string]: 'up' | 'down' }> {
    try {
      const result = await chrome.storage.local.get(['userVotes']);
      return result.userVotes || {};
    } catch (error) {
      return {};
    }
  }

  // Comments and Feedback
  public async addComment(recipeId: string, content: string): Promise<Comment> {
    const comment: Comment = {
      id: `comment-${Date.now()}`,
      userId: 'current-user',
      content,
      createdAt: new Date(),
      likes: 0,
      aiSuggestions: await this.generateAISuggestions(content)
    };

    try {
      const comments = await this.getRecipeComments(recipeId);
      comments.push(comment);
      await chrome.storage.local.set({ [`recipe-${recipeId}-comments`]: comments });
      return comment;
    } catch (error) {
      console.error('Failed to add comment:', error);
      throw error;
    }
  }

  public async getRecipeComments(recipeId: string): Promise<Comment[]> {
    try {
      const result = await chrome.storage.local.get([`recipe-${recipeId}-comments`]);
      return result[`recipe-${recipeId}-comments`] || [];
    } catch (error) {
      console.error('Failed to get recipe comments:', error);
      return [];
    }
  }

  private async generateAISuggestions(comment: string): Promise<string[]> {
    // Mock AI suggestions based on comment content
    const suggestions: string[] = [];
    
    if (comment.toLowerCase().includes('too salty')) {
      suggestions.push('Try reducing salt by half and add lemon juice for flavor');
      suggestions.push('Consider using herbs like rosemary or thyme instead');
    }
    
    if (comment.toLowerCase().includes('dry')) {
      suggestions.push('Add a tablespoon of olive oil or butter');
      suggestions.push('Try covering with foil during cooking');
    }
    
    if (comment.toLowerCase().includes('bland')) {
      suggestions.push('Add garlic powder and onion powder');
      suggestions.push('Try marinating ingredients beforehand');
    }
    
    return suggestions;
  }

  // Community Features
  public async getPopularRecipes(timeframe: 'day' | 'week' | 'month' = 'week'): Promise<Recipe[]> {
    // Mock popular recipes data
    return [];
  }

  public async getTrendingIngredients(): Promise<string[]> {
    // Mock trending ingredients
    return [
      'avocado', 'quinoa', 'kale', 'sweet potato', 'coconut oil',
      'chia seeds', 'turmeric', 'ginger', 'cauliflower', 'chickpeas'
    ];
  }

  public async getRecipeRecommendations(userId: string): Promise<Recipe[]> {
    // Mock personalized recommendations
    return [];
  }

  // Social Media Integration
  public async searchSocialMediaRecipes(query: string, platforms: string[] = ['all']): Promise<any[]> {
    // Mock social media search results
    return [
      {
        id: 'social-1',
        title: 'Viral TikTok Pasta Recipe',
        platform: 'tiktok',
        author: '@foodie_chef',
        likes: 15600,
        shares: 2300,
        url: 'https://tiktok.com/example',
        thumbnail: 'https://example.com/thumb1.jpg',
        description: 'This pasta recipe went viral for a reason!'
      },
      {
        id: 'social-2',
        title: 'Instagram-worthy Smoothie Bowl',
        platform: 'instagram',
        author: '@healthy_eats',
        likes: 8900,
        shares: 1200,
        url: 'https://instagram.com/example',
        thumbnail: 'https://example.com/thumb2.jpg',
        description: 'Beautiful and nutritious smoothie bowl recipe'
      }
    ];
  }

  public async extractRecipeFromSocialPost(url: string): Promise<Recipe | null> {
    // Mock recipe extraction from social media posts
    // In a real implementation, this would use web scraping or APIs
    return null;
  }

  // Analytics and Insights
  public async getRecipeAnalytics(recipeId: string): Promise<any> {
    return {
      views: Math.floor(Math.random() * 1000),
      shares: Math.floor(Math.random() * 100),
      saves: Math.floor(Math.random() * 200),
      rating: 4.2 + Math.random() * 0.8,
      comments: Math.floor(Math.random() * 50),
      popularityTrend: 'increasing'
    };
  }

  public async getUserSocialStats(userId: string): Promise<any> {
    return {
      recipesShared: Math.floor(Math.random() * 50),
      totalLikes: Math.floor(Math.random() * 500),
      followers: Math.floor(Math.random() * 100),
      challengesWon: Math.floor(Math.random() * 5),
      rank: Math.floor(Math.random() * 1000) + 1
    };
  }
}
