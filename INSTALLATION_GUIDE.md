# 🚀 ChefAI Chrome Extension - Installation Guide

## 📋 Quick Installation Steps

### Method 1: Ready-to-Use Extension (Recommended)

The extension has been built and is ready to install! Follow these simple steps:

1. **Open Chrome Extensions Page**
   - Open Google Chrome
   - Navigate to `chrome://extensions/`
   - Or click the three dots menu → More tools → Extensions

2. **Enable Developer Mode**
   - Toggle the "Developer mode" switch in the top-right corner
   - This allows you to load unpacked extensions

3. **Load the Extension**
   - Click the "Load unpacked" button
   - Navigate to your project folder
   - Select the `dist` folder (this contains the built extension)
   - Click "Select Folder"

4. **Verify Installation**
   - You should see "ChefAI - Advanced Recipe Generator" in your extensions list
   - The extension icon (🍳) should appear in your Chrome toolbar
   - Click the icon to open the popup interface

### Method 2: Build from Source (For Developers)

If you want to modify the code or build from source:

1. **Prerequisites**
   ```bash
   # Ensure you have Node.js installed
   node --version  # Should be 16+ 
   npm --version   # Should be 8+
   ```

2. **Install Dependencies**
   ```bash
   npm install
   ```

3. **Build the Extension**
   ```bash
   # Simple build (recommended for demo)
   node build.js
   
   # Or full build (if dependencies are installed)
   npm run build
   ```

4. **Load in Chrome** (same as Method 1, steps 1-4)

## 🎮 Using the Extension

### Popup Interface
1. **Click the ChefAI icon** in your Chrome toolbar
2. **Quick Actions Available**:
   - **Generate**: Create recipes from ingredients or titles
   - **Search**: Find trending recipes (coming soon)
   - **Trending**: Discover popular recipes (coming soon)
   - **Settings**: Customize the extension

3. **Generate a Recipe**:
   - Enter ingredients you have available
   - Or enter a recipe title like "Summer Refreshing Dessert"
   - Click "Generate Recipe"
   - View your AI-generated recipe!

### Full Dashboard
1. **Click "Open Full App"** in the popup
2. **Explore Features**:
   - **Dashboard**: Overview and statistics
   - **My Recipes**: Saved recipe collection
   - **Generate**: Advanced recipe creation
   - **Settings**: Customization options

### Recipe Detection
1. **Visit any recipe website** (like AllRecipes, Food Network, etc.)
2. **Look for the floating 🍳 button** (appears on recipe pages)
3. **Click to extract** the recipe automatically
4. **Save or modify** the extracted recipe

## ⚙️ Configuration Options

### Language Settings
- **Switch Languages**: English ↔ Arabic
- **Automatic Detection**: Uses browser language by default
- **Easy Extension**: More languages can be added

### Theme Customization
- **Glassmorphism Intensity**: Adjust the frosted glass effect
- **Color Schemes**: Light, dark, or auto mode
- **Art Mode**: Alternative artistic interface

### AI Configuration (Optional)
- **OpenAI API Key**: For enhanced AI features
- **Without API Key**: Uses built-in mock data for demonstration
- **Privacy Settings**: Control data sharing preferences

## 🔧 Troubleshooting

### Common Issues

**Extension doesn't appear in toolbar**
- Check if it's enabled in `chrome://extensions/`
- Look for it in the extensions menu (puzzle piece icon)
- Pin it to the toolbar for easy access

**Popup doesn't open**
- Refresh the extension by clicking the reload button in `chrome://extensions/`
- Check browser console for any errors
- Ensure all files are in the `dist` folder

**Recipe generation not working**
- This is normal without an OpenAI API key
- The extension uses mock data for demonstration
- Add your API key in settings for full functionality

**Styles not loading properly**
- Clear browser cache and reload the extension
- Check that all CSS files are in the `dist/src/styles/` folder
- Refresh the page and try again

### Developer Issues

**Build fails**
- Use the simple build: `node build.js`
- Check Node.js version (should be 16+)
- Try deleting `node_modules` and running `npm install` again

**TypeScript errors**
- The extension works without TypeScript compilation for demo purposes
- For full development, ensure all dependencies are installed
- Use `npm run lint` to check for issues

## 🌟 Features Overview

### ✅ Currently Working
- **Modern Popup Interface**: Beautiful glassmorphism design
- **Recipe Generation**: AI-powered with mock data
- **Multi-language Support**: English and Arabic
- **Recipe Detection**: Automatic extraction from websites
- **Full Dashboard**: Comprehensive options page
- **Responsive Design**: Works on all screen sizes

### 🚧 Coming Soon
- **Smart Search**: Trending recipe discovery
- **Social Integration**: Recipe sharing features
- **Advanced AI**: Enhanced recipe generation
- **Smart Devices**: Kitchen device integration
- **Meal Planning**: Weekly meal organization

## 📱 Browser Compatibility

### Fully Supported
- **Google Chrome**: Version 88+
- **Microsoft Edge**: Version 88+
- **Brave Browser**: Latest version
- **Opera**: Version 74+

### Manifest V3 Features
- **Service Worker**: Background processing
- **Modern APIs**: Latest Chrome extension features
- **Enhanced Security**: Improved permission model
- **Better Performance**: Optimized resource usage

## 🎯 Next Steps

### For Users
1. **Explore the Interface**: Try all the features
2. **Generate Recipes**: Test with different ingredients
3. **Visit Recipe Sites**: See automatic detection in action
4. **Customize Settings**: Make it your own

### For Developers
1. **Study the Code**: Learn modern extension development
2. **Add Features**: Extend functionality
3. **Improve Design**: Enhance the UI/UX
4. **Contribute**: Submit improvements

## 📞 Support

### Getting Help
- **Check the README**: Comprehensive documentation
- **Review the Code**: Well-commented source files
- **Demo Guide**: Detailed feature explanations
- **GitHub Issues**: Report bugs or request features

### Resources
- **Chrome Extension Docs**: https://developer.chrome.com/docs/extensions/
- **React Documentation**: https://react.dev/
- **TypeScript Guide**: https://www.typescriptlang.org/docs/

---

## 🎉 Congratulations!

You've successfully installed ChefAI, a professional-grade Chrome extension that showcases:

- **Modern Web Development**: React, TypeScript, Vite
- **Beautiful Design**: Glassmorphism UI/UX
- **Chrome Extension Mastery**: Manifest V3, service workers
- **AI Integration**: OpenAI API ready
- **Internationalization**: Multi-language support
- **Professional Architecture**: Scalable, maintainable code

**Ready to start cooking with AI? Click that ChefAI icon and let's create something delicious! 🍳✨**
