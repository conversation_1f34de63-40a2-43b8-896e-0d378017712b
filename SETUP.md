# 🍳 ChefAI Extension - Setup Guide

## ✅ **Working Extension with Real LLM Integration**

This is a **fully functional** Chrome extension that generates real recipes using AI APIs like OpenRouter, Gemini, OpenAI, and Anthropic.

## 🚀 **Quick Setup**

### 1. **Install the Extension**
1. Open Chrome and go to `chrome://extensions/`
2. Enable **Developer mode** (toggle in top right)
3. Click **"Load unpacked"**
4. Select the ChefAI folder containing `manifest.json`
5. The extension should now appear in your toolbar

### 2. **Get an API Key**
Choose one of these providers:

#### **🌐 OpenRouter (Recommended)**
- Visit: https://openrouter.ai
- Sign up and get your API key
- Access to multiple models (Claude, GPT-4, Llama)
- Pay-per-use pricing

#### **🧠 Google Gemini**
- Visit: https://ai.google.dev
- Get your Gemini API key
- Free tier available

#### **🤖 OpenAI**
- Visit: https://platform.openai.com
- Get your OpenAI API key
- Access to GPT-4o models

### 3. **Configure the Extension**
1. Click the ChefAI extension icon in your toolbar
2. Enter your API key in the popup
3. Click **"Save"** then **"Test"** to verify
4. You should see "✅ Connection successful!"

### 4. **Test the Extension**
1. Open the `test-page.html` file in Chrome
2. You should see a floating 🍳 button
3. Click it to open the sidebar interface
4. Add ingredients and generate a recipe!

## 🎯 **How to Use**

### **Method 1: Floating Button**
- The 🍳 button appears on **every website**
- Click it to open the recipe generator sidebar
- Works on any webpage you visit

### **Method 2: Extension Popup**
- Click the ChefAI icon in your toolbar
- Use the popup for quick access
- Configure settings directly

### **Method 3: Keyboard Shortcut**
- The extension can be triggered via Chrome shortcuts
- Go to `chrome://extensions/shortcuts` to set up

## 🍳 **Generating Recipes**

1. **Add Ingredients**: Type ingredients and click "Add"
2. **Set Parameters**: Choose cuisine, servings, difficulty
3. **Dietary Restrictions**: Select any dietary needs
4. **Generate**: Click "Generate Recipe with AI"
5. **View Results**: Recipes are automatically saved

## 📁 **File Structure**

```
ChefAI/
├── manifest.json          # Extension configuration
├── background.js          # Background service worker
├── content-script.js      # Main sidebar functionality
├── popup.html            # Extension popup interface
├── popup.js              # Popup functionality
├── test-page.html        # Test page for the extension
├── demo.html             # Demo showcase page
└── icons/                # Extension icons
```

## 🔧 **Technical Features**

### **Real LLM Integration**
- ✅ OpenRouter API support
- ✅ Google Gemini API support  
- ✅ OpenAI API support
- ✅ Anthropic Claude support
- ✅ Configurable models and parameters

### **Beautiful Interface**
- ✅ Glassmorphism design
- ✅ Responsive sidebar (420px width)
- ✅ Floating button on all websites
- ✅ Modern CSS with backdrop blur

### **Smart Features**
- ✅ Ingredient management with tags
- ✅ Recipe parameter configuration
- ✅ Dietary restriction support
- ✅ Recipe history and storage
- ✅ API connection testing

### **Chrome Integration**
- ✅ Content script injection
- ✅ Chrome storage for persistence
- ✅ Background service worker
- ✅ Extension popup interface
- ✅ Cross-origin API requests

## 🐛 **Troubleshooting**

### **Extension Not Loading**
- Check that `manifest.json` is in the root folder
- Ensure Developer mode is enabled
- Look for errors in `chrome://extensions/`

### **Floating Button Not Appearing**
- Refresh the webpage after loading extension
- Check browser console (F12) for errors
- Verify content script is injected

### **API Errors**
- Verify your API key is correct
- Check that you have API credits/quota
- Test connection in the popup settings

### **Sidebar Not Opening**
- Check browser console for JavaScript errors
- Ensure content script loaded properly
- Try refreshing the page

## 🎉 **Success Indicators**

When everything works correctly:
- ✅ Floating 🍳 button appears on websites
- ✅ Sidebar opens with beautiful interface
- ✅ API connection test succeeds
- ✅ Recipes generate successfully
- ✅ Recipes save to browser storage

## 🔑 **API Key Sources**

| Provider | URL | Features |
|----------|-----|----------|
| OpenRouter | https://openrouter.ai | Multiple models, pay-per-use |
| Google Gemini | https://ai.google.dev | Free tier, fast responses |
| OpenAI | https://platform.openai.com | GPT-4o, reliable |
| Anthropic | https://console.anthropic.com | Claude models, safety-focused |

## 📞 **Support**

If you encounter issues:
1. Check the browser console (F12) for errors
2. Verify your API key and credits
3. Test on the included `test-page.html`
4. Check Chrome extension permissions

## 🎯 **Next Steps**

Once the extension is working:
1. Generate your first recipe
2. Explore different cuisine types
3. Try various dietary restrictions
4. Save and organize your recipes
5. Use on cooking websites for inspiration

---

**🍳 Happy Cooking with AI! 🤖**
