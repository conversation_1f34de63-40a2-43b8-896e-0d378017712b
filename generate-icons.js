const fs = require('fs');
const path = require('path');

// Simple icon generation using SVG (no external dependencies)
function generateSVGI<PERSON>(size) {
    const radius = size * 0.2;
    
    return `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#667eea"/>
            <stop offset="50%" style="stop-color:#764ba2"/>
            <stop offset="100%" style="stop-color:#f093fb"/>
        </linearGradient>
        <linearGradient id="highlight" x1="0%" y1="0%" x2="50%" y2="50%">
            <stop offset="0%" style="stop-color:rgba(255,255,255,0.3)"/>
            <stop offset="100%" style="stop-color:rgba(255,255,255,0)"/>
        </linearGradient>
    </defs>
    
    <!-- Background with rounded corners -->
    <rect width="${size}" height="${size}" rx="${radius}" ry="${radius}" fill="url(#bgGradient)"/>
    
    <!-- Chef hat base -->
    <rect x="${size * 0.15}" y="${size * 0.7}" width="${size * 0.7}" height="${size * 0.15}" fill="white"/>
    
    <!-- Chef hat main circle -->
    <circle cx="${size * 0.5}" cy="${size * 0.4}" r="${size * 0.25}" fill="white"/>
    
    ${size >= 32 ? `
    <!-- Chef hat puffs (for larger icons) -->
    <circle cx="${size * 0.35}" cy="${size * 0.32}" r="${size * 0.08}" fill="white"/>
    <circle cx="${size * 0.65}" cy="${size * 0.32}" r="${size * 0.08}" fill="white"/>
    <circle cx="${size * 0.5}" cy="${size * 0.22}" r="${size * 0.08}" fill="white"/>
    ` : ''}
    
    ${size >= 48 ? `
    <!-- Sparkles (for larger icons) -->
    <circle cx="${size * 0.75}" cy="${size * 0.25}" r="${size * 0.03}" fill="#FFD700"/>
    <circle cx="${size * 0.25}" cy="${size * 0.3}" r="${size * 0.02}" fill="#FFD700"/>
    <circle cx="${size * 0.8}" cy="${size * 0.6}" r="${size * 0.015}" fill="#FFD700"/>
    ` : ''}
    
    <!-- Highlight overlay -->
    <rect width="${size}" height="${size}" rx="${radius}" ry="${radius}" fill="url(#highlight)"/>
</svg>`;
}

function generatePNGFromSVG(svgContent, size, outputPath) {
    // For Node.js environments without canvas, we'll save as SVG first
    // and provide instructions for conversion
    const svgPath = outputPath.replace('.png', '.svg');
    fs.writeFileSync(svgPath, svgContent);
    
    console.log(`✅ Generated SVG: ${svgPath}`);
    return svgPath;
}

function generateAllIcons() {
    const iconSizes = [
        { size: 16, name: 'icon16.png', description: 'Toolbar Icon (16x16)' },
        { size: 32, name: 'icon32.png', description: 'Extension Management (32x32)' },
        { size: 48, name: 'icon48.png', description: 'Extension Management (48x48)' },
        { size: 128, name: 'icon128.png', description: 'Chrome Web Store (128x128)' }
    ];

    // Create icons directory
    const iconsDir = path.join(__dirname, 'icons');
    if (!fs.existsSync(iconsDir)) {
        fs.mkdirSync(iconsDir, { recursive: true });
    }

    console.log('🍳 Generating ChefAI Chrome Extension Icons...');
    console.log('=' .repeat(50));

    const generatedFiles = [];

    iconSizes.forEach(iconInfo => {
        console.log(`Creating ${iconInfo.description}...`);
        
        const svgContent = generateSVGIcon(iconInfo.size);
        const outputPath = path.join(iconsDir, iconInfo.name);
        
        // Generate SVG version
        const svgPath = generatePNGFromSVG(svgContent, iconInfo.size, outputPath);
        generatedFiles.push({
            svg: svgPath,
            size: iconInfo.size,
            description: iconInfo.description
        });
    });

    console.log('=' .repeat(50));
    console.log('🎉 Icons generated successfully!');
    console.log(`📁 Icons saved in: ${path.resolve(iconsDir)}`);
    console.log('\n📋 Generated files:');
    
    generatedFiles.forEach(file => {
        console.log(`  • ${path.basename(file.svg)} - ${file.description}`);
    });

    // Create a simple HTML converter
    createHTMLConverter(generatedFiles, iconsDir);
    
    console.log('\n🔄 To convert SVG to PNG:');
    console.log('  1. Open convert-icons.html in your browser');
    console.log('  2. Click "Convert All Icons" to download PNG versions');
    console.log('  3. Or use online tools like convertio.co or cloudconvert.com');
    console.log('  4. Or install ImageMagick and run: convert icon.svg icon.png');
}

function createHTMLConverter(files, iconsDir) {
    const htmlContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChefAI Icon Converter</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .icon-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            text-align: center;
        }
        .icon-preview {
            background: white;
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            display: inline-block;
        }
        button {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        .convert-all {
            background: #4CAF50;
            font-size: 16px;
            font-weight: bold;
            margin: 20px 0;
            padding: 15px 30px;
        }
        canvas {
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🍳 ChefAI Icon Converter</h1>
        <p>Convert SVG icons to PNG format for Chrome Extension</p>
        
        <button class="convert-all" onclick="convertAllIcons()">Convert All Icons to PNG</button>
        
        <div class="icon-grid" id="iconGrid">
            ${files.map(file => `
                <div class="icon-card">
                    <h3>${file.description}</h3>
                    <div class="icon-preview">
                        <div id="svg-${file.size}"></div>
                    </div>
                    <p>${file.size}x${file.size} pixels</p>
                    <button onclick="convertIcon(${file.size})">Convert to PNG</button>
                </div>
            `).join('')}
        </div>
    </div>

    <script>
        const iconSVGs = {
            ${files.map(file => {
                const svgContent = fs.readFileSync(file.svg, 'utf8');
                return `${file.size}: \`${svgContent.replace(/`/g, '\\`')}\``;
            }).join(',\n            ')}
        };

        function loadSVGs() {
            Object.keys(iconSVGs).forEach(size => {
                const container = document.getElementById(\`svg-\${size}\`);
                container.innerHTML = iconSVGs[size];
            });
        }

        function convertIcon(size) {
            const svgElement = document.querySelector(\`#svg-\${size} svg\`);
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = size;
            canvas.height = size;
            
            const svgData = new XMLSerializer().serializeToString(svgElement);
            const svgBlob = new Blob([svgData], {type: 'image/svg+xml;charset=utf-8'});
            const svgUrl = URL.createObjectURL(svgBlob);
            
            const img = new Image();
            img.onload = function() {
                ctx.drawImage(img, 0, 0);
                URL.revokeObjectURL(svgUrl);
                
                canvas.toBlob(function(blob) {
                    const link = document.createElement('a');
                    link.download = \`icon\${size}.png\`;
                    link.href = URL.createObjectURL(blob);
                    link.click();
                    URL.revokeObjectURL(link.href);
                });
            };
            img.src = svgUrl;
        }

        function convertAllIcons() {
            const sizes = [16, 32, 48, 128];
            sizes.forEach((size, index) => {
                setTimeout(() => convertIcon(size), index * 500);
            });
        }

        // Load SVGs when page loads
        window.onload = loadSVGs;
    </script>
</body>
</html>`;

    const htmlPath = path.join(iconsDir, 'convert-icons.html');
    fs.writeFileSync(htmlPath, htmlContent);
    console.log(`📄 Created converter: ${htmlPath}`);
}

// Run the generator
if (require.main === module) {
    try {
        generateAllIcons();
    } catch (error) {
        console.error('❌ Error generating icons:', error.message);
        console.log('\n🔄 Alternative methods:');
        console.log('  1. Open create-icons.html in your browser');
        console.log('  2. Use online SVG to PNG converters');
        console.log('  3. Install Python with Pillow: pip install Pillow && python generate_icons.py');
    }
}

module.exports = { generateAllIcons, generateSVGIcon };
