import { Recipe, CookingMode, UserPreferences, NutritionData } from '../types';
import { NutritionService } from './NutritionService';

interface CookingModeConfig {
  name: string;
  description: string;
  icon: string;
  maxCookTime: number;
  preferredDifficulty: string[];
  nutritionTargets: Partial<NutritionData>;
  ingredientPreferences: string[];
  cookingMethods: string[];
  mealTypes: string[];
}

interface ModeRecommendation {
  mode: CookingMode;
  confidence: number;
  reasons: string[];
  adaptations: string[];
}

export class CookingModesService {
  private static instance: CookingModesService;
  private nutritionService: NutritionService;
  private modeConfigs: { [key in CookingMode]: CookingModeConfig };

  private constructor() {
    this.nutritionService = NutritionService.getInstance();
    this.initializeModeConfigs();
  }

  public static getInstance(): CookingModesService {
    if (!CookingModesService.instance) {
      CookingModesService.instance = new CookingModesService();
    }
    return CookingModesService.instance;
  }

  private initializeModeConfigs(): void {
    this.modeConfigs = {
      quickCook: {
        name: 'Quick Cook',
        description: 'Fast, efficient meals for busy schedules',
        icon: '⚡',
        maxCookTime: 30,
        preferredDifficulty: ['Easy', 'Medium'],
        nutritionTargets: {
          calories: 400,
          protein: 20,
          carbs: 45,
          fat: 15
        },
        ingredientPreferences: [
          'pre-cooked', 'canned', 'frozen', 'pasta', 'rice', 'eggs',
          'chicken breast', 'ground meat', 'vegetables', 'cheese'
        ],
        cookingMethods: ['stir-fry', 'sauté', 'microwave', 'boil', 'steam'],
        mealTypes: ['lunch', 'dinner', 'snack']
      },
      healthyEating: {
        name: 'Healthy Eating',
        description: 'Nutritious, balanced meals for wellness',
        icon: '🥗',
        maxCookTime: 60,
        preferredDifficulty: ['Easy', 'Medium', 'Hard'],
        nutritionTargets: {
          calories: 350,
          protein: 25,
          carbs: 40,
          fat: 12,
          fiber: 8,
          sodium: 400
        },
        ingredientPreferences: [
          'vegetables', 'fruits', 'lean protein', 'whole grains',
          'legumes', 'nuts', 'seeds', 'herbs', 'spices'
        ],
        cookingMethods: ['steam', 'grill', 'bake', 'roast', 'poach', 'raw'],
        mealTypes: ['breakfast', 'lunch', 'dinner', 'snack']
      },
      weekendFeasts: {
        name: 'Weekend Feasts',
        description: 'Elaborate, impressive dishes for special occasions',
        icon: '🍽️',
        maxCookTime: 180,
        preferredDifficulty: ['Medium', 'Hard'],
        nutritionTargets: {
          calories: 600,
          protein: 30,
          carbs: 50,
          fat: 25
        },
        ingredientPreferences: [
          'premium cuts', 'specialty ingredients', 'wine', 'cream',
          'exotic spices', 'artisanal products', 'seasonal ingredients'
        ],
        cookingMethods: ['braise', 'roast', 'sous vide', 'confit', 'flambé', 'smoke'],
        mealTypes: ['dinner', 'brunch', 'dessert']
      },
      experimental: {
        name: 'Experimental',
        description: 'Creative fusion and innovative cooking techniques',
        icon: '🧪',
        maxCookTime: 90,
        preferredDifficulty: ['Medium', 'Hard'],
        nutritionTargets: {
          calories: 450,
          protein: 22,
          carbs: 48,
          fat: 18
        },
        ingredientPreferences: [
          'unusual combinations', 'international ingredients',
          'molecular gastronomy', 'fermented foods', 'exotic fruits'
        ],
        cookingMethods: ['fusion', 'molecular', 'fermentation', 'smoking', 'curing'],
        mealTypes: ['dinner', 'appetizer', 'dessert']
      }
    };
  }

  public async recommendCookingMode(
    availableIngredients: string[],
    timeConstraint: number,
    userPreferences: UserPreferences,
    mealType: string
  ): Promise<ModeRecommendation[]> {
    const recommendations: ModeRecommendation[] = [];

    for (const [mode, config] of Object.entries(this.modeConfigs)) {
      const confidence = this.calculateModeConfidence(
        mode as CookingMode,
        config,
        availableIngredients,
        timeConstraint,
        userPreferences,
        mealType
      );

      if (confidence > 0.3) {
        const reasons = this.generateRecommendationReasons(
          mode as CookingMode,
          config,
          availableIngredients,
          timeConstraint,
          userPreferences
        );

        const adaptations = this.generateModeAdaptations(
          mode as CookingMode,
          config,
          availableIngredients,
          userPreferences
        );

        recommendations.push({
          mode: mode as CookingMode,
          confidence,
          reasons,
          adaptations
        });
      }
    }

    return recommendations.sort((a, b) => b.confidence - a.confidence);
  }

  private calculateModeConfidence(
    mode: CookingMode,
    config: CookingModeConfig,
    ingredients: string[],
    timeConstraint: number,
    preferences: UserPreferences,
    mealType: string
  ): number {
    let confidence = 0.5; // Base confidence

    // Time constraint factor
    if (timeConstraint <= config.maxCookTime) {
      confidence += 0.3;
    } else {
      confidence -= 0.2;
    }

    // Ingredient compatibility
    const compatibleIngredients = ingredients.filter(ingredient =>
      config.ingredientPreferences.some(pref =>
        ingredient.toLowerCase().includes(pref.toLowerCase()) ||
        pref.toLowerCase().includes(ingredient.toLowerCase())
      )
    );
    confidence += (compatibleIngredients.length / ingredients.length) * 0.2;

    // Meal type compatibility
    if (config.mealTypes.includes(mealType.toLowerCase())) {
      confidence += 0.15;
    }

    // User skill level
    const userSkill = preferences.cookingSkillLevel || 'Medium';
    if (config.preferredDifficulty.includes(userSkill)) {
      confidence += 0.1;
    }

    // Dietary preferences alignment
    if (preferences.dietaryRestrictions) {
      if (mode === 'healthyEating' && preferences.dietaryRestrictions.includes('vegetarian')) {
        confidence += 0.1;
      }
      if (mode === 'quickCook' && preferences.dietaryRestrictions.includes('busy_lifestyle')) {
        confidence += 0.15;
      }
    }

    return Math.min(1, Math.max(0, confidence));
  }

  private generateRecommendationReasons(
    mode: CookingMode,
    config: CookingModeConfig,
    ingredients: string[],
    timeConstraint: number,
    preferences: UserPreferences
  ): string[] {
    const reasons: string[] = [];

    if (timeConstraint <= config.maxCookTime) {
      reasons.push(`Perfect for your ${timeConstraint}-minute time limit`);
    }

    const compatibleIngredients = ingredients.filter(ingredient =>
      config.ingredientPreferences.some(pref =>
        ingredient.toLowerCase().includes(pref.toLowerCase())
      )
    );

    if (compatibleIngredients.length > 0) {
      reasons.push(`Great match for ingredients like ${compatibleIngredients.slice(0, 3).join(', ')}`);
    }

    switch (mode) {
      case 'quickCook':
        reasons.push('Efficient cooking methods for busy schedules');
        break;
      case 'healthyEating':
        reasons.push('Focuses on nutritional balance and wellness');
        break;
      case 'weekendFeasts':
        reasons.push('Perfect for special occasions and entertaining');
        break;
      case 'experimental':
        reasons.push('Encourages creativity and culinary exploration');
        break;
    }

    return reasons;
  }

  private generateModeAdaptations(
    mode: CookingMode,
    config: CookingModeConfig,
    ingredients: string[],
    preferences: UserPreferences
  ): string[] {
    const adaptations: string[] = [];

    switch (mode) {
      case 'quickCook':
        adaptations.push('Use pre-cut vegetables to save time');
        adaptations.push('Consider one-pot or sheet pan recipes');
        adaptations.push('Prep ingredients in advance when possible');
        break;
      case 'healthyEating':
        adaptations.push('Steam or grill instead of frying');
        adaptations.push('Use herbs and spices instead of salt');
        adaptations.push('Include a variety of colorful vegetables');
        break;
      case 'weekendFeasts':
        adaptations.push('Allow extra time for complex techniques');
        adaptations.push('Consider wine pairings');
        adaptations.push('Focus on presentation and plating');
        break;
      case 'experimental':
        adaptations.push('Try unusual ingredient combinations');
        adaptations.push('Experiment with different cooking methods');
        adaptations.push('Document your creations for future reference');
        break;
    }

    // Add dietary restriction adaptations
    if (preferences.dietaryRestrictions) {
      if (preferences.dietaryRestrictions.includes('vegetarian')) {
        adaptations.push('Substitute plant-based proteins');
      }
      if (preferences.dietaryRestrictions.includes('gluten_free')) {
        adaptations.push('Use gluten-free alternatives for grains');
      }
      if (preferences.dietaryRestrictions.includes('low_sodium')) {
        adaptations.push('Enhance flavor with citrus and herbs');
      }
    }

    return adaptations;
  }

  public async adaptRecipeForMode(recipe: Recipe, mode: CookingMode): Promise<Recipe> {
    const config = this.modeConfigs[mode];
    const adaptedRecipe = { ...recipe };

    // Adapt cooking time
    if (mode === 'quickCook' && recipe.cookTime > config.maxCookTime) {
      adaptedRecipe.instructions = this.optimizeForSpeed(recipe.instructions);
      adaptedRecipe.cookTime = Math.min(recipe.cookTime, config.maxCookTime);
    }

    // Adapt for health
    if (mode === 'healthyEating') {
      adaptedRecipe.ingredients = this.makeHealthier(recipe.ingredients);
      adaptedRecipe.instructions = this.optimizeForHealth(recipe.instructions);
    }

    // Adapt for weekend feasts
    if (mode === 'weekendFeasts') {
      adaptedRecipe.instructions = this.enhanceForPresentation(recipe.instructions);
      adaptedRecipe.ingredients = this.upgradeIngredients(recipe.ingredients);
    }

    // Adapt for experimental
    if (mode === 'experimental') {
      adaptedRecipe.ingredients = this.addCreativeElements(recipe.ingredients);
      adaptedRecipe.instructions = this.addInnovativeTechniques(recipe.instructions);
    }

    // Recalculate nutrition
    adaptedRecipe.nutrition = await this.nutritionService.analyzeRecipeNutrition(adaptedRecipe);

    return adaptedRecipe;
  }

  private optimizeForSpeed(instructions: any[]): any[] {
    return instructions.map(instruction => ({
      ...instruction,
      description: instruction.description
        .replace(/simmer for \d+ minutes/g, 'simmer for 5-10 minutes')
        .replace(/bake for \d+ minutes/g, 'bake at higher temperature for less time')
        .replace(/marinate overnight/g, 'marinate for 15 minutes')
    }));
  }

  private makeHealthier(ingredients: any[]): any[] {
    return ingredients.map(ingredient => {
      let healthierIngredient = { ...ingredient };
      
      // Substitute healthier alternatives
      if (ingredient.name.toLowerCase().includes('white rice')) {
        healthierIngredient.name = ingredient.name.replace(/white rice/gi, 'brown rice');
      }
      if (ingredient.name.toLowerCase().includes('butter')) {
        healthierIngredient.name = ingredient.name.replace(/butter/gi, 'olive oil');
        healthierIngredient.amount = Math.round(ingredient.amount * 0.75); // Reduce amount
      }
      if (ingredient.name.toLowerCase().includes('cream')) {
        healthierIngredient.name = ingredient.name.replace(/cream/gi, 'Greek yogurt');
      }
      
      return healthierIngredient;
    });
  }

  private optimizeForHealth(instructions: any[]): any[] {
    return instructions.map(instruction => ({
      ...instruction,
      description: instruction.description
        .replace(/fry/g, 'sauté with minimal oil')
        .replace(/deep fry/g, 'bake or air fry')
        .replace(/add salt/g, 'season with herbs and spices')
    }));
  }

  private enhanceForPresentation(instructions: any[]): any[] {
    const enhanced = [...instructions];
    enhanced.push({
      step: enhanced.length + 1,
      description: 'Plate elegantly and garnish with fresh herbs or microgreens',
      duration: 5
    });
    return enhanced;
  }

  private upgradeIngredients(ingredients: any[]): any[] {
    return ingredients.map(ingredient => {
      let upgradedIngredient = { ...ingredient };
      
      // Upgrade to premium versions
      if (ingredient.name.toLowerCase().includes('chicken')) {
        upgradedIngredient.name = ingredient.name.replace(/chicken/gi, 'organic free-range chicken');
      }
      if (ingredient.name.toLowerCase().includes('olive oil')) {
        upgradedIngredient.name = ingredient.name.replace(/olive oil/gi, 'extra virgin olive oil');
      }
      if (ingredient.name.toLowerCase().includes('cheese')) {
        upgradedIngredient.name = ingredient.name.replace(/cheese/gi, 'artisanal aged cheese');
      }
      
      return upgradedIngredient;
    });
  }

  private addCreativeElements(ingredients: any[]): any[] {
    const creative = [...ingredients];
    
    // Add experimental ingredients
    creative.push({
      name: 'exotic spice blend',
      amount: 1,
      unit: 'tsp',
      category: 'Spices'
    });
    
    creative.push({
      name: 'microgreens for garnish',
      amount: 1,
      unit: 'handful',
      category: 'Vegetables'
    });
    
    return creative;
  }

  private addInnovativeTechniques(instructions: any[]): any[] {
    const innovative = [...instructions];
    
    // Add creative cooking techniques
    innovative.splice(Math.floor(innovative.length / 2), 0, {
      step: Math.floor(innovative.length / 2) + 1,
      description: 'Apply molecular gastronomy technique or fusion cooking method',
      duration: 10
    });
    
    return innovative.map((instruction, index) => ({
      ...instruction,
      step: index + 1
    }));
  }

  public getModeConfig(mode: CookingMode): CookingModeConfig {
    return this.modeConfigs[mode];
  }

  public getAllModes(): CookingModeConfig[] {
    return Object.values(this.modeConfigs);
  }

  public async getModeStatistics(userId?: string): Promise<any> {
    // This would integrate with AnalyticsService to get actual usage stats
    return {
      quickCook: { usage: 45, satisfaction: 4.2, avgTime: 22 },
      healthyEating: { usage: 30, satisfaction: 4.5, avgTime: 35 },
      weekendFeasts: { usage: 15, satisfaction: 4.8, avgTime: 95 },
      experimental: { usage: 10, satisfaction: 4.0, avgTime: 60 }
    };
  }
}
