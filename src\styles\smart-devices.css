/* Smart Device Dashboard Styles */
.smart-device-dashboard {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 30px;
}

.dashboard-header h2 {
  font-size: 2.5em;
  margin: 0 0 10px;
  background: linear-gradient(45deg, #fff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.dashboard-header p {
  font-size: 1.1em;
  opacity: 0.8;
  margin: 0 0 20px;
}

.quick-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.stat-item {
  text-align: center;
  padding: 15px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-value {
  display: block;
  font-size: 2em;
  font-weight: bold;
  color: var(--accent-color);
  margin-bottom: 5px;
}

.stat-label {
  font-size: 0.9em;
  opacity: 0.8;
}

.dashboard-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  overflow-x: auto;
}

.dashboard-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  border-radius: 8px 8px 0 0;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.dashboard-tab.active {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-bottom: 2px solid var(--accent-color);
}

.dashboard-tab:hover {
  background: rgba(255, 255, 255, 0.05);
  color: white;
}

.tab-icon {
  font-size: 1.2em;
}

.tab-name {
  font-weight: 500;
}

/* Section Headers */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  margin: 0;
  font-size: 1.5em;
}

.discover-btn {
  padding: 10px 20px;
  background: linear-gradient(45deg, var(--accent-color), var(--secondary-color));
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.discover-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.discover-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Device Cards */
.devices-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.device-card {
  padding: 20px;
  transition: all 0.3s ease;
}

.device-card:hover {
  transform: translateY(-5px);
}

.device-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.device-icon {
  font-size: 2.5em;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
}

.device-info {
  flex: 1;
}

.device-info h4 {
  margin: 0 0 5px;
  font-size: 1.2em;
}

.device-info p {
  margin: 0;
  opacity: 0.8;
  font-size: 0.9em;
}

.device-status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8em;
  font-weight: 500;
  color: white;
  text-transform: uppercase;
}

.device-details {
  margin: 15px 0;
}

.device-capabilities {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-bottom: 10px;
}

.capability-tag {
  padding: 2px 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  font-size: 0.8em;
  opacity: 0.8;
}

.device-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9em;
  opacity: 0.8;
}

.connection-type {
  background: rgba(255, 255, 255, 0.1);
  padding: 2px 8px;
  border-radius: 8px;
  font-size: 0.8em;
}

.battery-level {
  display: flex;
  align-items: center;
  gap: 5px;
}

.device-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.device-action-btn {
  flex: 1;
  padding: 10px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.device-action-btn.connect {
  background: linear-gradient(45deg, #4CAF50, #45a049);
  color: white;
}

.device-action-btn.disconnect {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
}

.device-action-btn:hover {
  transform: translateY(-1px);
}

/* Timer Cards */
.timers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.timer-card {
  padding: 20px;
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
}

.timer-card.status-running {
  border-left-color: #4CAF50;
}

.timer-card.status-paused {
  border-left-color: #FF9800;
}

.timer-card.status-completed {
  border-left-color: #2196F3;
}

.timer-card:hover {
  transform: translateY(-3px);
}

.timer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.timer-header h4 {
  margin: 0;
  font-size: 1.1em;
}

.timer-status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8em;
  font-weight: 500;
  text-transform: uppercase;
}

.timer-status.running {
  background: rgba(76, 175, 80, 0.3);
  color: #4CAF50;
}

.timer-status.paused {
  background: rgba(255, 152, 0, 0.3);
  color: #FF9800;
}

.timer-status.completed {
  background: rgba(33, 150, 243, 0.3);
  color: #2196F3;
}

.timer-display {
  text-align: center;
  margin: 20px 0;
}

.timer-time {
  font-size: 2.5em;
  font-weight: bold;
  font-family: 'Courier New', monospace;
  color: var(--accent-color);
  margin-bottom: 10px;
}

.timer-progress {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.timer-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--accent-color), var(--secondary-color));
  border-radius: 3px;
  transition: width 0.3s ease;
}

.timer-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 15px 0;
  font-size: 0.9em;
  opacity: 0.8;
}

.timer-actions {
  display: flex;
  gap: 8px;
  margin-top: 15px;
}

.timer-action-btn {
  flex: 1;
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  font-size: 0.9em;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.timer-action-btn.start {
  background: linear-gradient(45deg, #4CAF50, #45a049);
  color: white;
}

.timer-action-btn.pause,
.timer-action-btn.stop,
.timer-action-btn.reset {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
}

.timer-action-btn:hover {
  transform: translateY(-1px);
}

/* Automation Cards */
.automations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.automation-card {
  padding: 20px;
  transition: all 0.3s ease;
}

.automation-card:hover {
  transform: translateY(-3px);
}

.automation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.automation-header h4 {
  margin: 0;
  font-size: 1.2em;
}

.automation-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9em;
}

.automation-toggle input[type="checkbox"] {
  width: 16px;
  height: 16px;
}

.automation-details {
  margin: 15px 0;
}

.automation-details > div {
  margin-bottom: 10px;
}

.automation-details strong {
  display: block;
  margin-bottom: 5px;
  font-size: 0.9em;
  opacity: 0.8;
}

.device-list {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.device-tag {
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  font-size: 0.8em;
  display: flex;
  align-items: center;
  gap: 4px;
}

.trigger-tag {
  padding: 2px 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  font-size: 0.8em;
  margin-right: 5px;
}

.automation-last-run {
  font-size: 0.8em;
  opacity: 0.7;
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Reminder Cards */
.reminders-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.reminder-card {
  padding: 20px;
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
}

.reminder-card.status-scheduled {
  border-left-color: #2196F3;
}

.reminder-card.status-sent {
  border-left-color: #4CAF50;
}

.reminder-card.status-snoozed {
  border-left-color: #FF9800;
}

.reminder-card:hover {
  transform: translateY(-2px);
}

.reminder-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.reminder-header h4 {
  margin: 0;
  font-size: 1.1em;
}

.reminder-status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8em;
  font-weight: 500;
  text-transform: uppercase;
}

.reminder-status.scheduled {
  background: rgba(33, 150, 243, 0.3);
  color: #2196F3;
}

.reminder-status.sent {
  background: rgba(76, 175, 80, 0.3);
  color: #4CAF50;
}

.reminder-content p {
  margin: 0 0 10px;
  line-height: 1.5;
}

.reminder-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  font-size: 0.9em;
  opacity: 0.8;
}

.reminder-meta span {
  display: flex;
  align-items: center;
  gap: 5px;
}

/* Empty States */
.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-state h3 {
  margin: 0 0 10px;
  font-size: 1.5em;
  opacity: 0.8;
}

.empty-state p {
  margin: 0 0 20px;
  opacity: 0.6;
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
  .smart-device-dashboard {
    padding: 15px;
  }
  
  .dashboard-tabs {
    flex-wrap: wrap;
  }
  
  .devices-grid,
  .timers-grid,
  .automations-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .section-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .timer-time {
    font-size: 2em;
  }
  
  .device-header {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }
  
  .device-actions {
    flex-direction: column;
  }
  
  .timer-actions {
    flex-wrap: wrap;
  }
  
  .timer-action-btn {
    min-width: 80px;
  }
}
