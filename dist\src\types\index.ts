// Core Recipe Types
export interface Recipe {
  id: string;
  title: string;
  description: string;
  ingredients: Ingredient[];
  instructions: Instruction[];
  prepTime: number; // in minutes
  cookTime: number; // in minutes
  servings: number;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  cuisine: string;
  tags: string[];
  nutritionalInfo: NutritionalInfo;
  image?: string;
  source: 'AI_Generated' | 'Social_Media' | 'Web_Search' | 'User_Created';
  popularity: number;
  rating: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface Ingredient {
  id: string;
  name: string;
  amount: number;
  unit: string;
  category: IngredientCategory;
  isOptional?: boolean;
  substitutes?: string[];
}

export interface Instruction {
  id: string;
  step: number;
  description: string;
  duration?: number; // in minutes
  temperature?: number; // in celsius
  tips?: string[];
}

export interface NutritionalInfo {
  calories: number;
  protein: number; // in grams
  carbs: number; // in grams
  fat: number; // in grams
  fiber: number; // in grams
  sugar: number; // in grams
  sodium: number; // in mg
  vitamins: { [key: string]: number };
  minerals: { [key: string]: number };
  allergens: string[];
}

export type IngredientCategory = 
  | 'Protein' 
  | 'Vegetables' 
  | 'Fruits' 
  | 'Grains' 
  | 'Dairy' 
  | 'Spices' 
  | 'Herbs' 
  | 'Oils' 
  | 'Condiments' 
  | 'Beverages' 
  | 'Other';

// AI Generation Types
export interface AIRecipeRequest {
  ingredients?: string[];
  title?: string;
  cuisine?: string;
  dietaryRestrictions?: DietaryRestriction[];
  cookingTime?: number;
  difficulty?: Recipe['difficulty'];
  servings?: number;
  mealType?: MealType;
}

export type DietaryRestriction = 
  | 'Vegetarian' 
  | 'Vegan' 
  | 'Gluten-Free' 
  | 'Dairy-Free' 
  | 'Nut-Free' 
  | 'Low-Carb' 
  | 'Keto' 
  | 'Paleo' 
  | 'Halal' 
  | 'Kosher';

export type MealType = 
  | 'Breakfast' 
  | 'Lunch' 
  | 'Dinner' 
  | 'Snack' 
  | 'Dessert' 
  | 'Appetizer' 
  | 'Beverage';

// Search & Social Media Types
export interface SearchResult {
  id: string;
  title: string;
  description: string;
  url: string;
  source: SearchSource;
  thumbnail?: string;
  rating?: number;
  popularity: number;
  tags: string[];
  createdAt: Date;
}

export type SearchSource = 
  | 'Facebook' 
  | 'Instagram' 
  | 'Reddit' 
  | 'YouTube' 
  | 'Pinterest' 
  | 'TikTok' 
  | 'Google' 
  | 'Food_Blog';

// User & Customization Types
export interface UserPreferences {
  language: string;
  theme: ThemeSettings;
  dietaryRestrictions: DietaryRestriction[];
  favoriteIngredients: string[];
  dislikedIngredients: string[];
  cookingSkillLevel: Recipe['difficulty'];
  preferredCuisines: string[];
  notifications: NotificationSettings;
  privacy: PrivacySettings;
}

export interface ThemeSettings {
  mode: 'light' | 'dark' | 'auto';
  primaryColor: string;
  accentColor: string;
  glassmorphismIntensity: number; // 0-100
  fontSize: 'small' | 'medium' | 'large';
  artMode: boolean;
  customCSS?: string;
}

export interface NotificationSettings {
  recipeReminders: boolean;
  trendingRecipes: boolean;
  socialUpdates: boolean;
  cookingTimers: boolean;
}

export interface PrivacySettings {
  shareRecipes: boolean;
  allowDataCollection: boolean;
  showInLeaderboards: boolean;
}

// Social & Community Types
export interface RecipeChallenge {
  id: string;
  title: string;
  description: string;
  ingredients: string[];
  startDate: Date;
  endDate: Date;
  participants: number;
  submissions: ChallengeSubmission[];
  prizes: string[];
}

export interface ChallengeSubmission {
  id: string;
  userId: string;
  recipeId: string;
  submittedAt: Date;
  votes: number;
  comments: Comment[];
}

export interface Comment {
  id: string;
  userId: string;
  content: string;
  createdAt: Date;
  likes: number;
  aiSuggestions?: string[];
}

// Smart Device Integration Types
export interface SmartDevice {
  id: string;
  name: string;
  type: DeviceType;
  brand: string;
  capabilities: DeviceCapability[];
  isConnected: boolean;
}

export type DeviceType = 
  | 'Smart_Oven' 
  | 'Smart_Microwave' 
  | 'Smart_Refrigerator' 
  | 'Smart_Scale' 
  | 'Smart_Thermometer' 
  | 'Voice_Assistant';

export type DeviceCapability = 
  | 'Temperature_Control' 
  | 'Timer_Setting' 
  | 'Recipe_Display' 
  | 'Voice_Commands' 
  | 'Ingredient_Detection' 
  | 'Cooking_Monitoring';

// Analytics & Trends Types
export interface TrendData {
  id: string;
  keyword: string;
  popularity: number;
  growth: number; // percentage
  region: string;
  timeframe: 'daily' | 'weekly' | 'monthly';
  relatedKeywords: string[];
  sources: SearchSource[];
}

export interface UserAnalytics {
  totalRecipesGenerated: number;
  favoriteIngredients: { [ingredient: string]: number };
  cookingFrequency: { [day: string]: number };
  preferredCookingTimes: number[];
  successfulRecipes: number;
  sharedRecipes: number;
  challengesParticipated: number;
}

// Extension State Types
export interface ExtensionState {
  isInitialized: boolean;
  currentUser?: UserPreferences;
  recentRecipes: Recipe[];
  savedRecipes: Recipe[];
  searchHistory: string[];
  connectedDevices: SmartDevice[];
  activeTimers: Timer[];
}

export interface Timer {
  id: string;
  name: string;
  duration: number; // in seconds
  remainingTime: number; // in seconds
  isActive: boolean;
  recipeId?: string;
  stepId?: string;
}

// API Response Types
export interface APIResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}
