// ChefAI Content Script
console.log('ChefAI Content Script loaded on:', window.location.href);

// Recipe detection functionality
class ChefAIContentScript {
  constructor() {
    this.init();
  }

  init() {
    this.detectRecipes();
    this.setupMessageListener();
  }

  detectRecipes() {
    // Check for structured data (JSON-LD)
    const jsonLdScripts = document.querySelectorAll('script[type="application/ld+json"]');
    let recipeFound = false;

    for (const script of jsonLdScripts) {
      try {
        const data = JSON.parse(script.textContent || '');
        if (this.isRecipeData(data)) {
          recipeFound = true;
          console.log('Recipe detected via JSON-LD');
          break;
        }
      } catch (error) {
        // Ignore parsing errors
      }
    }

    // Check for microdata
    if (!recipeFound && document.querySelector('[itemtype*="Recipe"]')) {
      recipeFound = true;
      console.log('Recipe detected via microdata');
    }

    // Check for common recipe selectors
    if (!recipeFound) {
      const recipeSelectors = [
        '.recipe',
        '.recipe-card',
        '[class*="recipe"]',
        '.entry-content',
        '.post-content'
      ];

      for (const selector of recipeSelectors) {
        if (document.querySelector(selector)) {
          const element = document.querySelector(selector);
          if (element && this.containsRecipeKeywords(element.textContent || '')) {
            recipeFound = true;
            console.log('Recipe detected via heuristics');
            break;
          }
        }
      }
    }

    if (recipeFound) {
      this.showRecipeIndicator();
    }
  }

  isRecipeData(data) {
    if (Array.isArray(data)) {
      return data.some(item => this.isRecipeData(item));
    }
    return data['@type'] === 'Recipe' ||
           (data['@graph'] && data['@graph'].some(item => item['@type'] === 'Recipe'));
  }

  containsRecipeKeywords(text) {
    const keywords = ['ingredients', 'instructions', 'recipe', 'cooking', 'preparation', 'serves', 'prep time'];
    const lowerText = text.toLowerCase();
    return keywords.filter(keyword => lowerText.includes(keyword)).length >= 3;
  }

  showRecipeIndicator() {
    // Create floating action button
    const fab = document.createElement('div');
    fab.id = 'chefai-fab';
    fab.innerHTML = '🍳';
    fab.style.cssText = `
      position: fixed !important;
      bottom: 20px !important;
      right: 20px !important;
      width: 56px !important;
      height: 56px !important;
      background: linear-gradient(135deg, #667eea, #764ba2) !important;
      border-radius: 50% !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      font-size: 24px !important;
      cursor: pointer !important;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
      z-index: 10000 !important;
      transition: transform 0.2s ease !important;
      border: none !important;
      outline: none !important;
    `;

    fab.addEventListener('mouseenter', () => {
      fab.style.transform = 'scale(1.1)';
    });

    fab.addEventListener('mouseleave', () => {
      fab.style.transform = 'scale(1)';
    });

    fab.addEventListener('click', () => {
      this.extractRecipe();
    });

    document.body.appendChild(fab);
  }

  extractRecipe() {
    console.log('Extracting recipe from page...');
    // Send message to background script
    chrome.runtime.sendMessage({
      type: 'RECIPE_EXTRACTED',
      url: window.location.href,
      title: document.title
    });
  }

  setupMessageListener() {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      switch (message.type) {
        case 'EXTRACT_RECIPE':
          this.extractRecipe();
          sendResponse({ success: true });
          break;
        default:
          sendResponse({ success: false, error: 'Unknown message type' });
      }
      return true;
    });
  }
}

// Initialize content script
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new ChefAIContentScript();
  });
} else {
  new ChefAIContentScript();
}