import React, { useState, useEffect } from 'react';
import { Recipe, AIRecipeRequest, UserPreferences } from '../types';
import { RecipeGenerator } from '../components/RecipeGenerator';
import { QuickActions } from '../components/QuickActions';
import { RecentRecipes } from '../components/RecentRecipes';
import { LoadingOverlay } from '../components/LoadingOverlay';
import { ErrorMessage } from '../components/ErrorMessage';
import { useRecipeGeneration } from '../hooks/useRecipeGeneration';
import { useStorage } from '../hooks/useStorage';
import { useI18n } from '../hooks/useI18n';

const PopupApp: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentView, setCurrentView] = useState<'home' | 'generate' | 'search' | 'settings'>('home');
  
  const { generateRecipe, isGenerating } = useRecipeGeneration();
  const { data: userPreferences, updateData: updatePreferences } = useStorage<UserPreferences>('userPreferences');
  const { data: recentRecipes, updateData: updateRecentRecipes } = useStorage<Recipe[]>('recentRecipes', []);
  const { t, currentLanguage, changeLanguage } = useI18n();

  useEffect(() => {
    // Initialize extension on popup open
    initializeExtension();
  }, []);

  const initializeExtension = async () => {
    try {
      setIsLoading(true);
      
      // Load user preferences and recent recipes
      const preferences = await chrome.storage.sync.get(['userPreferences']);
      const recipes = await chrome.storage.local.get(['recentRecipes']);
      
      if (preferences.userPreferences) {
        updatePreferences(preferences.userPreferences);
      }
      
      if (recipes.recentRecipes) {
        updateRecentRecipes(recipes.recentRecipes);
      }
      
      setError(null);
    } catch (err) {
      setError(t('errors.initialization_failed'));
      console.error('Failed to initialize extension:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRecipeGeneration = async (request: AIRecipeRequest) => {
    try {
      setError(null);
      const recipe = await generateRecipe(request);
      
      if (recipe) {
        // Add to recent recipes
        const updatedRecipes = [recipe, ...(recentRecipes || [])].slice(0, 10);
        updateRecentRecipes(updatedRecipes);
        
        // Save to storage
        await chrome.storage.local.set({ recentRecipes: updatedRecipes });
        
        // Show success message
        chrome.notifications.create({
          type: 'basic',
          iconUrl: 'icons/icon48.png',
          title: t('notifications.recipe_generated'),
          message: t('notifications.recipe_generated_message', { title: recipe.title })
        });
      }
    } catch (err) {
      setError(t('errors.recipe_generation_failed'));
      console.error('Recipe generation failed:', err);
    }
  };

  const handleQuickAction = (action: string) => {
    switch (action) {
      case 'generate':
        setCurrentView('generate');
        break;
      case 'search':
        setCurrentView('search');
        break;
      case 'trending':
        // Open trending recipes in new tab
        chrome.tabs.create({ url: chrome.runtime.getURL('options.html#trending') });
        break;
      case 'settings':
        setCurrentView('settings');
        break;
      default:
        break;
    }
  };

  const handleRecipeClick = (recipe: Recipe) => {
    // Open recipe details in new tab
    chrome.tabs.create({ 
      url: chrome.runtime.getURL(`options.html#recipe/${recipe.id}`) 
    });
  };

  const handleOpenFullApp = () => {
    chrome.tabs.create({ url: chrome.runtime.getURL('options.html') });
  };

  if (isLoading) {
    return <LoadingOverlay message={t('loading.initializing')} />;
  }

  return (
    <div className="popup-container">
      {/* Header */}
      <header className="popup-header">
        <div className="popup-logo">🍳</div>
        <h1 className="popup-title">{t('app.title')}</h1>
        <p className="popup-subtitle">{t('app.subtitle')}</p>
      </header>

      {/* Main Content */}
      <main className="popup-content">
        {error && (
          <ErrorMessage 
            message={error} 
            onDismiss={() => setError(null)} 
          />
        )}

        {currentView === 'home' && (
          <>
            <QuickActions onAction={handleQuickAction} />
            
            <section className="recipe-input-section">
              <h2 className="section-title">
                <span className="section-icon">✨</span>
                {t('sections.quick_generate')}
              </h2>
              <RecipeGenerator 
                onGenerate={handleRecipeGeneration}
                isGenerating={isGenerating}
                compact={true}
              />
            </section>

            {recentRecipes && recentRecipes.length > 0 && (
              <section className="recent-recipes">
                <h2 className="section-title">
                  <span className="section-icon">📚</span>
                  {t('sections.recent_recipes')}
                </h2>
                <RecentRecipes 
                  recipes={recentRecipes.slice(0, 3)}
                  onRecipeClick={handleRecipeClick}
                  compact={true}
                />
              </section>
            )}
          </>
        )}

        {currentView === 'generate' && (
          <section className="recipe-input-section">
            <h2 className="section-title">
              <span className="section-icon">🎯</span>
              {t('sections.advanced_generate')}
            </h2>
            <RecipeGenerator 
              onGenerate={handleRecipeGeneration}
              isGenerating={isGenerating}
              compact={false}
            />
          </section>
        )}

        {currentView === 'search' && (
          <section className="search-section">
            <h2 className="section-title">
              <span className="section-icon">🔍</span>
              {t('sections.smart_search')}
            </h2>
            <div className="glass-card-sm">
              <p style={{ margin: 0, opacity: 0.8, fontSize: 'var(--font-size-sm)' }}>
                {t('messages.search_coming_soon')}
              </p>
            </div>
          </section>
        )}

        {currentView === 'settings' && (
          <section className="settings-section">
            <h2 className="section-title">
              <span className="section-icon">⚙️</span>
              {t('sections.quick_settings')}
            </h2>
            <div className="glass-card-sm">
              <div className="input-group">
                <label className="input-label">{t('settings.language')}</label>
                <select 
                  className="glass-input"
                  value={currentLanguage}
                  onChange={(e) => changeLanguage(e.target.value)}
                >
                  <option value="en">English</option>
                  <option value="ar">العربية</option>
                  <option value="fr">Français</option>
                  <option value="es">Español</option>
                </select>
              </div>
            </div>
          </section>
        )}
      </main>

      {/* Footer */}
      <footer className="popup-footer">
        <div className="footer-actions">
          {currentView !== 'home' && (
            <button 
              className="footer-button"
              onClick={() => setCurrentView('home')}
            >
              ← {t('actions.back')}
            </button>
          )}
          
          <button 
            className="footer-button-primary"
            onClick={handleOpenFullApp}
          >
            {t('actions.open_full_app')} →
          </button>
        </div>
      </footer>

      {/* Loading Overlay */}
      {isGenerating && (
        <LoadingOverlay message={t('loading.generating_recipe')} />
      )}
    </div>
  );
};

export default PopupApp;
