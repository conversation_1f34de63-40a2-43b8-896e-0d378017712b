const fs = require('fs');
const path = require('path');

// Simple TypeScript to JavaScript transpiler for the sidebar injector
function transpileTypeScript(tsCode) {
  // Remove TypeScript-specific syntax
  let jsCode = tsCode
    // Remove import statements and replace with require-like functionality
    .replace(/import\s+{[^}]+}\s+from\s+['"][^'"]+['"];?\s*/g, '')
    .replace(/import\s+\*\s+as\s+\w+\s+from\s+['"][^'"]+['"];?\s*/g, '')
    .replace(/import\s+\w+\s+from\s+['"][^'"]+['"];?\s*/g, '')
    
    // Remove interface definitions
    .replace(/interface\s+\w+\s*{[^}]*}/gs, '')
    .replace(/export\s+interface\s+\w+\s*{[^}]*}/gs, '')
    
    // Remove type annotations
    .replace(/:\s*[A-Za-z_][A-Za-z0-9_<>[\]|&\s]*(?=\s*[=,;)])/g, '')
    .replace(/:\s*[A-Za-z_][A-Za-z0-9_<>[\]|&\s]*(?=\s*{)/g, '')
    
    // Remove type parameters
    .replace(/<[A-Za-z_][A-Za-z0-9_<>[\]|&\s,]*>/g, '')
    
    // Remove export keywords
    .replace(/export\s+/g, '')
    
    // Remove access modifiers
    .replace(/\b(private|public|protected)\s+/g, '')
    
    // Remove readonly keyword
    .replace(/\breadonly\s+/g, '')
    
    // Remove type assertions
    .replace(/\s+as\s+[A-Za-z_][A-Za-z0-9_<>[\]|&\s]*/g, '')
    
    // Clean up extra whitespace
    .replace(/\n\s*\n\s*\n/g, '\n\n')
    .trim();

  return jsCode;
}

// Create a simplified version of the services for the content script
function createSimplifiedServices() {
  const llmServiceCode = `
// Simplified LLM Service for content script
class LLMService {
  static instance = null;
  config = null;
  providers = new Map();

  static getInstance() {
    if (!LLMService.instance) {
      LLMService.instance = new LLMService();
    }
    return LLMService.instance;
  }

  constructor() {
    this.initializeProviders();
    this.loadConfiguration();
  }

  initializeProviders() {
    const providers = [
      {
        id: 'openrouter',
        name: 'OpenRouter',
        description: 'Access to multiple AI models through OpenRouter API',
        apiEndpoint: 'https://openrouter.ai/api/v1/chat/completions',
        requiresApiKey: true,
        maxTokens: 4096,
        models: [
          {
            id: 'anthropic/claude-3.5-sonnet',
            name: 'Claude 3.5 Sonnet',
            description: 'Anthropic\\'s most capable model',
            contextLength: 200000,
            costPer1kTokens: 0.003
          },
          {
            id: 'openai/gpt-4-turbo',
            name: 'GPT-4 Turbo',
            description: 'OpenAI\\'s most capable model',
            contextLength: 128000,
            costPer1kTokens: 0.01
          }
        ]
      },
      {
        id: 'gemini',
        name: 'Google Gemini',
        description: 'Google\\'s native Gemini API',
        apiEndpoint: 'https://generativelanguage.googleapis.com/v1beta/models',
        requiresApiKey: true,
        maxTokens: 8192,
        models: [
          {
            id: 'gemini-1.5-pro',
            name: 'Gemini 1.5 Pro',
            description: 'Google\\'s most capable model',
            contextLength: 2000000,
            costPer1kTokens: 0.0035
          }
        ]
      }
    ];

    providers.forEach(provider => {
      this.providers.set(provider.id, provider);
    });
  }

  async loadConfiguration() {
    try {
      const result = await chrome.storage.local.get(['llmConfig']);
      if (result.llmConfig) {
        this.config = JSON.parse(result.llmConfig);
      } else {
        this.config = {
          provider: 'openrouter',
          model: 'anthropic/claude-3.5-sonnet',
          apiKey: '',
          temperature: 0.7,
          maxTokens: 2048,
          systemPrompt: 'You are ChefAI, an expert culinary assistant.'
        };
      }
    } catch (error) {
      console.error('Failed to load LLM configuration:', error);
    }
  }

  async saveConfiguration(config) {
    if (config) {
      this.config = config;
    }
    
    try {
      await chrome.storage.local.set({
        llmConfig: JSON.stringify(this.config)
      });
    } catch (error) {
      console.error('Failed to save LLM configuration:', error);
    }
  }

  getConfiguration() {
    return this.config;
  }

  getProviders() {
    return Array.from(this.providers.values());
  }

  async generateRecipe(request) {
    if (!this.config || !this.config.apiKey) {
      return {
        success: false,
        error: 'LLM configuration not set. Please configure your API key in settings.'
      };
    }

    const provider = this.providers.get(this.config.provider);
    if (!provider) {
      return {
        success: false,
        error: 'Invalid LLM provider configured.'
      };
    }

    try {
      const prompt = this.buildRecipePrompt(request);
      const response = await this.callLLMAPI(prompt);
      
      if (response.success && response.data) {
        const recipe = this.parseRecipeResponse(response.data);
        return {
          success: true,
          data: recipe,
          usage: response.usage
        };
      }
      
      return response;
    } catch (error) {
      console.error('Recipe generation failed:', error);
      return {
        success: false,
        error: 'Failed to generate recipe: ' + error.message
      };
    }
  }

  buildRecipePrompt(request) {
    let prompt = 'Generate a detailed recipe with the following requirements:\\n\\n';

    if (request.ingredients && request.ingredients.length > 0) {
      prompt += 'Available ingredients: ' + request.ingredients.join(', ') + '\\n';
    }

    if (request.cuisineType) {
      prompt += 'Cuisine type: ' + request.cuisineType + '\\n';
    }

    if (request.dietaryRestrictions && request.dietaryRestrictions.length > 0) {
      prompt += 'Dietary restrictions: ' + request.dietaryRestrictions.join(', ') + '\\n';
    }

    if (request.servings) {
      prompt += 'Servings: ' + request.servings + '\\n';
    }

    if (request.cookingTime) {
      prompt += 'Maximum cooking time: ' + request.cookingTime + ' minutes\\n';
    }

    if (request.difficulty) {
      prompt += 'Difficulty level: ' + request.difficulty + '\\n';
    }

    if (request.mealType) {
      prompt += 'Meal type: ' + request.mealType + '\\n';
    }

    if (request.customPrompt) {
      prompt += 'Additional requirements: ' + request.customPrompt + '\\n';
    }

    prompt += '\\nPlease provide the recipe in the following JSON format:\\n{\\n  "title": "Recipe Name",\\n  "description": "Brief description",\\n  "servings": number,\\n  "prepTime": number,\\n  "cookTime": number,\\n  "difficulty": "Easy|Medium|Hard",\\n  "cuisine": "cuisine type",\\n  "ingredients": [{"name": "ingredient", "amount": number, "unit": "unit", "category": "category"}],\\n  "instructions": [{"step": number, "description": "instruction", "duration": number}],\\n  "tips": ["tip1", "tip2"],\\n  "nutrition": {"calories": number, "protein": number, "carbs": number, "fat": number}\\n}';

    return prompt;
  }

  async callLLMAPI(prompt) {
    if (!this.config) {
      return { success: false, error: 'No configuration available' };
    }

    const provider = this.providers.get(this.config.provider);
    if (!provider) {
      return { success: false, error: 'Provider not found' };
    }

    try {
      let response;
      let requestBody;

      switch (this.config.provider) {
        case 'openrouter':
          requestBody = {
            model: this.config.model,
            messages: [
              { role: 'system', content: this.config.systemPrompt },
              { role: 'user', content: prompt }
            ],
            temperature: this.config.temperature,
            max_tokens: this.config.maxTokens
          };

          response = await fetch(provider.apiEndpoint, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer ' + this.config.apiKey,
              'HTTP-Referer': chrome.runtime.getURL(''),
              'X-Title': 'ChefAI Extension'
            },
            body: JSON.stringify(requestBody)
          });
          break;

        case 'gemini':
          const geminiUrl = provider.apiEndpoint + '/' + this.config.model + ':generateContent?key=' + this.config.apiKey;
          requestBody = {
            contents: [{
              parts: [{
                text: this.config.systemPrompt + '\\n\\n' + prompt
              }]
            }],
            generationConfig: {
              temperature: this.config.temperature,
              maxOutputTokens: this.config.maxTokens
            }
          };

          response = await fetch(geminiUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
          });
          break;

        default:
          return { success: false, error: 'Unsupported provider' };
      }

      if (!response.ok) {
        const errorText = await response.text();
        return {
          success: false,
          error: 'API request failed: ' + response.status + ' ' + response.statusText + ' - ' + errorText
        };
      }

      const data = await response.json();
      return this.parseAPIResponse(data, this.config.provider);

    } catch (error) {
      console.error('LLM API call failed:', error);
      return {
        success: false,
        error: 'Network error: ' + error.message
      };
    }
  }

  parseAPIResponse(data, provider) {
    try {
      let content;
      let usage = {};

      switch (provider) {
        case 'openrouter':
          content = data.choices[0].message.content;
          usage = {
            promptTokens: data.usage?.prompt_tokens || 0,
            completionTokens: data.usage?.completion_tokens || 0,
            totalTokens: data.usage?.total_tokens || 0
          };
          break;

        case 'gemini':
          content = data.candidates[0].content.parts[0].text;
          usage = {
            promptTokens: data.usageMetadata?.promptTokenCount || 0,
            completionTokens: data.usageMetadata?.candidatesTokenCount || 0,
            totalTokens: data.usageMetadata?.totalTokenCount || 0
          };
          break;

        default:
          return { success: false, error: 'Unknown provider response format' };
      }

      return {
        success: true,
        data: content,
        usage
      };

    } catch (error) {
      return {
        success: false,
        error: 'Failed to parse API response: ' + error.message
      };
    }
  }

  parseRecipeResponse(content) {
    try {
      const jsonMatch = content.match(/{[\\s\\S]*}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }

      const recipeData = JSON.parse(jsonMatch[0]);
      
      const recipe = {
        id: 'llm-recipe-' + Date.now(),
        title: recipeData.title || 'Generated Recipe',
        description: recipeData.description || '',
        servings: recipeData.servings || 4,
        prepTime: recipeData.prepTime || 15,
        cookTime: recipeData.cookTime || 30,
        difficulty: recipeData.difficulty || 'Medium',
        cuisine: recipeData.cuisine || 'International',
        ingredients: recipeData.ingredients?.map((ing, index) => ({
          id: 'ing-' + index,
          name: ing.name,
          amount: ing.amount,
          unit: ing.unit,
          category: ing.category || 'Other'
        })) || [],
        instructions: recipeData.instructions?.map((inst) => ({
          step: inst.step,
          description: inst.description,
          duration: inst.duration || 0
        })) || [],
        tags: recipeData.tags || [],
        nutrition: recipeData.nutrition || {
          calories: 0,
          protein: 0,
          carbs: 0,
          fat: 0,
          fiber: 0,
          sugar: 0,
          sodium: 0
        },
        tips: recipeData.tips || [],
        createdAt: new Date(),
        source: 'AI Generated'
      };

      return recipe;

    } catch (error) {
      console.error('Failed to parse recipe response:', error);
      return null;
    }
  }

  async testConnection() {
    if (!this.config || !this.config.apiKey) {
      return {
        success: false,
        error: 'No API key configured'
      };
    }

    try {
      const testPrompt = 'Respond with "Connection successful" if you can read this message.';
      const response = await this.callLLMAPI(testPrompt);
      
      if (response.success) {
        return {
          success: true,
          data: 'Connection test successful',
          usage: response.usage
        };
      }
      
      return response;
    } catch (error) {
      return {
        success: false,
        error: 'Connection test failed: ' + error.message
      };
    }
  }
}

// Simplified Recipe Service
class RecipeService {
  static instance = null;

  static getInstance() {
    if (!RecipeService.instance) {
      RecipeService.instance = new RecipeService();
    }
    return RecipeService.instance;
  }

  async saveRecipe(recipe) {
    try {
      const result = await chrome.storage.local.get(['recipes']);
      const recipes = result.recipes ? JSON.parse(result.recipes) : [];
      
      recipes.unshift(recipe);
      
      await chrome.storage.local.set({
        recipes: JSON.stringify(recipes)
      });
      
      return recipe.id;
    } catch (error) {
      console.error('Failed to save recipe:', error);
      throw error;
    }
  }

  async getRecipes() {
    try {
      const result = await chrome.storage.local.get(['recipes']);
      return result.recipes ? JSON.parse(result.recipes) : [];
    } catch (error) {
      console.error('Failed to load recipes:', error);
      return [];
    }
  }
}
`;

  return llmServiceCode;
}

// Read the TypeScript file
const tsFilePath = path.join(__dirname, 'src', 'content', 'sidebar-injector.ts');
const tsCode = fs.readFileSync(tsFilePath, 'utf8');

// Transpile to JavaScript
const jsCode = transpileTypeScript(tsCode);

// Add the simplified services
const servicesCode = createSimplifiedServices();

// Combine everything
const finalCode = servicesCode + '\n\n' + jsCode;

// Ensure dist directory exists
const distDir = path.join(__dirname, 'dist', 'content');
if (!fs.existsSync(distDir)) {
  fs.mkdirSync(distDir, { recursive: true });
}

// Write the JavaScript file
const jsFilePath = path.join(distDir, 'sidebar-injector.js');
fs.writeFileSync(jsFilePath, finalCode);

console.log('✅ Sidebar injector built successfully!');
console.log('📁 Output:', jsFilePath);
