import React, { useState, useEffect } from 'react';
import { RecipeChallenge, ChallengeSubmission, Recipe } from '../types';
import { SocialService } from '../services/SocialService';
import { useI18n } from '../hooks/useI18n';
import { formatDistanceToNow } from 'date-fns';

interface RecipeChallengesProps {
  userRecipes?: Recipe[];
}

export const RecipeChallenges: React.FC<RecipeChallengesProps> = ({ userRecipes = [] }) => {
  const { t } = useI18n();
  const [challenges, setChallenges] = useState<RecipeChallenge[]>([]);
  const [selectedChallenge, setSelectedChallenge] = useState<RecipeChallenge | null>(null);
  const [submissions, setSubmissions] = useState<ChallengeSubmission[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'active' | 'completed' | 'my-submissions'>('active');

  const socialService = SocialService.getInstance();

  useEffect(() => {
    loadChallenges();
  }, []);

  const loadChallenges = async () => {
    try {
      setIsLoading(true);
      const challengeData = await socialService.getChallenges();
      setChallenges(challengeData);
    } catch (error) {
      console.error('Failed to load challenges:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadSubmissions = async (challengeId: string) => {
    try {
      const submissionData = await socialService.getChallengeSubmissions(challengeId);
      setSubmissions(submissionData);
    } catch (error) {
      console.error('Failed to load submissions:', error);
    }
  };

  const handleChallengeClick = (challenge: RecipeChallenge) => {
    setSelectedChallenge(challenge);
    loadSubmissions(challenge.id);
  };

  const handleSubmitRecipe = async (challengeId: string, recipeId: string) => {
    try {
      const success = await socialService.submitToChallenge(challengeId, recipeId);
      if (success) {
        alert(t('challenges.submission_success'));
        loadSubmissions(challengeId);
      }
    } catch (error) {
      console.error('Failed to submit recipe:', error);
      alert(t('challenges.submission_failed'));
    }
  };

  const handleVote = async (submissionId: string, vote: 'up' | 'down') => {
    try {
      await socialService.voteForSubmission(submissionId, vote);
      // Reload submissions to show updated votes
      if (selectedChallenge) {
        loadSubmissions(selectedChallenge.id);
      }
    } catch (error) {
      console.error('Failed to vote:', error);
    }
  };

  const getActiveChallenges = () => {
    const now = new Date();
    return challenges.filter(challenge => 
      challenge.startDate <= now && challenge.endDate >= now
    );
  };

  const getCompletedChallenges = () => {
    const now = new Date();
    return challenges.filter(challenge => challenge.endDate < now);
  };

  const getChallengeStatus = (challenge: RecipeChallenge) => {
    const now = new Date();
    if (challenge.startDate > now) return 'upcoming';
    if (challenge.endDate < now) return 'completed';
    return 'active';
  };

  const getTimeRemaining = (endDate: Date) => {
    return formatDistanceToNow(endDate, { addSuffix: true });
  };

  if (isLoading) {
    return (
      <div className="challenges-loading">
        <div className="glass-spinner"></div>
        <p>{t('loading.challenges')}</p>
      </div>
    );
  }

  if (selectedChallenge) {
    return (
      <div className="challenge-detail">
        <div className="challenge-header glass-card">
          <button 
            className="back-button glass-button"
            onClick={() => setSelectedChallenge(null)}
          >
            ← {t('actions.back')}
          </button>
          
          <div className="challenge-info">
            <h2>{selectedChallenge.title}</h2>
            <p>{selectedChallenge.description}</p>
            
            <div className="challenge-meta">
              <div className="meta-item">
                <span className="meta-icon">👥</span>
                <span>{selectedChallenge.participants} {t('challenges.participants')}</span>
              </div>
              <div className="meta-item">
                <span className="meta-icon">⏰</span>
                <span>{t('challenges.ends')} {getTimeRemaining(selectedChallenge.endDate)}</span>
              </div>
              <div className="meta-item">
                <span className="meta-icon">🏆</span>
                <span>{selectedChallenge.prizes.length} {t('challenges.prizes')}</span>
              </div>
            </div>

            <div className="required-ingredients">
              <h4>{t('challenges.required_ingredients')}</h4>
              <div className="ingredients-list">
                {selectedChallenge.ingredients.map((ingredient, index) => (
                  <span key={index} className="ingredient-tag">{ingredient}</span>
                ))}
              </div>
            </div>

            <div className="challenge-prizes">
              <h4>{t('challenges.prizes')}</h4>
              <ul>
                {selectedChallenge.prizes.map((prize, index) => (
                  <li key={index}>{prize}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* Submit Recipe Section */}
        {getChallengeStatus(selectedChallenge) === 'active' && userRecipes.length > 0 && (
          <div className="submit-recipe glass-card">
            <h3>{t('challenges.submit_recipe')}</h3>
            <div className="recipe-selection">
              {userRecipes.map((recipe) => (
                <div key={recipe.id} className="recipe-option glass-card-sm">
                  <div className="recipe-info">
                    <h4>{recipe.title}</h4>
                    <p>{recipe.description}</p>
                    <div className="recipe-meta">
                      <span>⏱️ {recipe.prepTime + recipe.cookTime}min</span>
                      <span>👥 {recipe.servings}</span>
                      <span>📊 {recipe.difficulty}</span>
                    </div>
                  </div>
                  <button
                    className="glass-button-primary"
                    onClick={() => handleSubmitRecipe(selectedChallenge.id, recipe.id)}
                  >
                    {t('challenges.submit')}
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Submissions */}
        <div className="challenge-submissions glass-card">
          <h3>{t('challenges.submissions')} ({submissions.length})</h3>
          {submissions.length === 0 ? (
            <div className="no-submissions">
              <p>{t('challenges.no_submissions')}</p>
            </div>
          ) : (
            <div className="submissions-list">
              {submissions.map((submission) => (
                <div key={submission.id} className="submission-item glass-card-sm">
                  <div className="submission-info">
                    <h4>{t('challenges.recipe_submission')}</h4>
                    <p>{t('challenges.submitted')} {formatDistanceToNow(submission.submittedAt, { addSuffix: true })}</p>
                    <div className="submission-stats">
                      <span>💬 {submission.comments.length} {t('challenges.comments')}</span>
                      <span>👍 {submission.votes} {t('challenges.votes')}</span>
                    </div>
                  </div>
                  <div className="submission-actions">
                    <button
                      className="vote-button glass-button"
                      onClick={() => handleVote(submission.id, 'up')}
                    >
                      👍
                    </button>
                    <button
                      className="vote-button glass-button"
                      onClick={() => handleVote(submission.id, 'down')}
                    >
                      👎
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="recipe-challenges">
      <div className="challenges-header glass-card">
        <h2>{t('challenges.title')}</h2>
        <p>{t('challenges.description')}</p>
      </div>

      <div className="challenges-tabs">
        <button
          className={`tab-button ${activeTab === 'active' ? 'active' : ''}`}
          onClick={() => setActiveTab('active')}
        >
          🔥 {t('challenges.active')} ({getActiveChallenges().length})
        </button>
        <button
          className={`tab-button ${activeTab === 'completed' ? 'active' : ''}`}
          onClick={() => setActiveTab('completed')}
        >
          ✅ {t('challenges.completed')} ({getCompletedChallenges().length})
        </button>
        <button
          className={`tab-button ${activeTab === 'my-submissions' ? 'active' : ''}`}
          onClick={() => setActiveTab('my-submissions')}
        >
          📝 {t('challenges.my_submissions')}
        </button>
      </div>

      <div className="challenges-content">
        {activeTab === 'active' && (
          <div className="challenges-grid">
            {getActiveChallenges().map((challenge) => (
              <div
                key={challenge.id}
                className="challenge-card glass-card"
                onClick={() => handleChallengeClick(challenge)}
              >
                <div className="challenge-status active">
                  {t('challenges.active')}
                </div>
                <h3>{challenge.title}</h3>
                <p>{challenge.description}</p>
                
                <div className="challenge-ingredients">
                  {challenge.ingredients.slice(0, 3).map((ingredient, index) => (
                    <span key={index} className="ingredient-tag">{ingredient}</span>
                  ))}
                  {challenge.ingredients.length > 3 && (
                    <span className="ingredient-tag">+{challenge.ingredients.length - 3}</span>
                  )}
                </div>

                <div className="challenge-footer">
                  <div className="challenge-stats">
                    <span>👥 {challenge.participants}</span>
                    <span>🏆 {challenge.prizes.length}</span>
                  </div>
                  <div className="challenge-deadline">
                    {t('challenges.ends')} {getTimeRemaining(challenge.endDate)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {activeTab === 'completed' && (
          <div className="challenges-grid">
            {getCompletedChallenges().map((challenge) => (
              <div
                key={challenge.id}
                className="challenge-card glass-card completed"
                onClick={() => handleChallengeClick(challenge)}
              >
                <div className="challenge-status completed">
                  {t('challenges.completed')}
                </div>
                <h3>{challenge.title}</h3>
                <p>{challenge.description}</p>
                
                <div className="challenge-footer">
                  <div className="challenge-stats">
                    <span>👥 {challenge.participants}</span>
                    <span>📝 {challenge.submissions.length}</span>
                  </div>
                  <div className="challenge-deadline">
                    {t('challenges.ended')} {formatDistanceToNow(challenge.endDate, { addSuffix: true })}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {activeTab === 'my-submissions' && (
          <div className="my-submissions">
            <div className="empty-state">
              <div className="empty-icon">📝</div>
              <h3>{t('challenges.no_submissions_yet')}</h3>
              <p>{t('challenges.no_submissions_description')}</p>
              <button
                className="glass-button-primary"
                onClick={() => setActiveTab('active')}
              >
                {t('challenges.browse_challenges')}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
