<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChefAI Extension Test Page</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        
        h1 {
            font-size: 3em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .test-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 40px;
            margin: 30px 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
        }
        
        .instructions {
            text-align: left;
            line-height: 1.6;
        }
        
        .step {
            margin: 15px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            border-left: 4px solid #FFD700;
        }
        
        .step-number {
            display: inline-block;
            width: 30px;
            height: 30px;
            background: #FFD700;
            color: #333;
            border-radius: 50%;
            text-align: center;
            line-height: 30px;
            font-weight: bold;
            margin-right: 15px;
        }
        
        .highlight {
            background: rgba(255, 215, 0, 0.3);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .success {
            background: rgba(76, 175, 80, 0.3);
            border: 1px solid rgba(76, 175, 80, 0.5);
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .warning {
            background: rgba(255, 152, 0, 0.3);
            border: 1px solid rgba(255, 152, 0, 0.5);
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .floating-demo {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            border: 2px solid rgba(255, 255, 255, 0.2);
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 1000;
            animation: pulse 2s infinite;
        }
        
        .floating-demo:hover {
            transform: scale(1.1);
        }
        
        @keyframes pulse {
            0% { box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3); }
            50% { box-shadow: 0 4px 30px rgba(255, 215, 0, 0.5); }
            100% { box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3); }
        }
        
        .api-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .api-provider {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 15px;
            margin: 5px;
            border-radius: 20px;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="floating-demo" title="This is how the ChefAI button appears on every website">
        🍳
    </div>
    
    <div class="container">
        <h1>🍳 ChefAI Extension Test</h1>
        <p style="font-size: 1.2em; opacity: 0.9;">Test the ChefAI extension functionality on this page</p>
        
        <div class="test-card">
            <h2>🧪 Extension Testing Instructions</h2>
            <div class="instructions">
                <div class="step">
                    <span class="step-number">1</span>
                    <strong>Load the Extension:</strong> Open Chrome, go to <span class="highlight">chrome://extensions/</span>, enable Developer Mode, and click "Load unpacked" to select the ChefAI folder.
                </div>
                
                <div class="step">
                    <span class="step-number">2</span>
                    <strong>Check Installation:</strong> You should see the ChefAI extension icon in your browser toolbar and a floating <span class="highlight">🍳</span> button on this page.
                </div>
                
                <div class="step">
                    <span class="step-number">3</span>
                    <strong>Configure API:</strong> Click the extension icon in the toolbar, enter your OpenRouter API key, and click "Save" then "Test".
                </div>
                
                <div class="step">
                    <span class="step-number">4</span>
                    <strong>Test Sidebar:</strong> Click the floating <span class="highlight">🍳</span> button or the extension popup to open the sidebar interface.
                </div>
                
                <div class="step">
                    <span class="step-number">5</span>
                    <strong>Generate Recipe:</strong> Add some ingredients, configure settings, and click "Generate Recipe with AI" to test the LLM integration.
                </div>
            </div>
        </div>
        
        <div class="test-card">
            <h2>🔑 API Key Setup</h2>
            <p>To test recipe generation, you'll need an API key from one of these providers:</p>
            
            <div class="api-info">
                <h3>Recommended: OpenRouter</h3>
                <p>Visit <strong>openrouter.ai</strong> to get your API key</p>
                <div class="api-provider">✅ Multiple Models</div>
                <div class="api-provider">💰 Pay-per-use</div>
                <div class="api-provider">🚀 Easy Setup</div>
            </div>
            
            <div class="warning">
                <strong>⚠️ Important:</strong> The extension requires a valid API key to generate recipes. Without it, you can test the interface but not the AI functionality.
            </div>
        </div>
        
        <div class="test-card">
            <h2>✅ What Should Work</h2>
            <ul style="text-align: left; line-height: 1.8;">
                <li><strong>Floating Button:</strong> Appears on every website</li>
                <li><strong>Sidebar Interface:</strong> Opens with glassmorphism design</li>
                <li><strong>Ingredient Management:</strong> Add/remove ingredients with tags</li>
                <li><strong>Recipe Parameters:</strong> Cuisine, servings, dietary restrictions</li>
                <li><strong>API Configuration:</strong> Save and test API keys</li>
                <li><strong>Recipe Generation:</strong> Real AI-powered recipe creation</li>
                <li><strong>Recipe Storage:</strong> Persistent storage in Chrome</li>
                <li><strong>Popup Interface:</strong> Quick access from toolbar</li>
            </ul>
        </div>
        
        <div class="test-card">
            <h2>🐛 Troubleshooting</h2>
            <div class="instructions">
                <div class="step">
                    <span class="step-number">!</span>
                    <strong>No Floating Button:</strong> Refresh the page after loading the extension
                </div>
                
                <div class="step">
                    <span class="step-number">!</span>
                    <strong>Sidebar Won't Open:</strong> Check browser console for errors (F12)
                </div>
                
                <div class="step">
                    <span class="step-number">!</span>
                    <strong>API Errors:</strong> Verify your API key is correct and has credits
                </div>
                
                <div class="step">
                    <span class="step-number">!</span>
                    <strong>Recipe Generation Fails:</strong> Check network tab for API request errors
                </div>
            </div>
        </div>
        
        <div class="success">
            <h3>🎉 Success Indicators</h3>
            <p>If everything works correctly, you should be able to:</p>
            <ul>
                <li>See the floating 🍳 button on this page</li>
                <li>Open the sidebar with a beautiful interface</li>
                <li>Configure your API key and test the connection</li>
                <li>Generate actual recipes using AI</li>
                <li>View saved recipes in the history</li>
            </ul>
        </div>
    </div>
    
    <script>
        // Demo floating button interaction
        document.querySelector('.floating-demo').addEventListener('click', function() {
            alert('🍳 ChefAI Extension Demo\n\nThis is a demo of the floating button.\n\nThe real extension button should appear when you load the extension in Chrome.\n\nClick the real button to open the AI recipe generator!');
        });
        
        // Log extension detection
        if (window.chefAI) {
            console.log('✅ ChefAI Extension detected!');
        } else {
            console.log('❌ ChefAI Extension not detected. Make sure it\'s loaded.');
        }
        
        // Check for extension after a delay
        setTimeout(() => {
            if (window.chefAI) {
                console.log('🍳 ChefAI Extension is working!');
            }
        }, 2000);
    </script>
</body>
</html>
