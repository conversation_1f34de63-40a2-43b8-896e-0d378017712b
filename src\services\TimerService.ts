import { Recipe, CookingStep } from '../types';

export interface CookingTimer {
  id: string;
  name: string;
  duration: number; // in seconds
  remainingTime: number; // in seconds
  status: TimerStatus;
  type: TimerType;
  recipeId?: string;
  stepId?: string;
  createdAt: Date;
  startedAt?: Date;
  pausedAt?: Date;
  completedAt?: Date;
  notificationSettings: NotificationSettings;
}

export type TimerStatus = 'created' | 'running' | 'paused' | 'completed' | 'cancelled';
export type TimerType = 'cooking' | 'prep' | 'rest' | 'marinate' | 'chill' | 'custom';

export interface NotificationSettings {
  enabled: boolean;
  sound: boolean;
  vibration: boolean;
  intervals: number[]; // notification intervals in seconds before completion
  customMessage?: string;
}

export interface CookingReminder {
  id: string;
  title: string;
  message: string;
  scheduledTime: Date;
  type: ReminderType;
  recipeId?: string;
  stepId?: string;
  isRecurring: boolean;
  recurringPattern?: RecurringPattern;
  status: ReminderStatus;
  createdAt: Date;
  notificationSettings: NotificationSettings;
}

export type ReminderType = 'prep' | 'shopping' | 'cooking' | 'cleanup' | 'meal_planning' | 'custom';
export type ReminderStatus = 'scheduled' | 'sent' | 'dismissed' | 'snoozed' | 'cancelled';

export interface RecurringPattern {
  frequency: 'daily' | 'weekly' | 'monthly';
  interval: number; // every N days/weeks/months
  daysOfWeek?: number[]; // 0-6, Sunday = 0
  endDate?: Date;
}

export class TimerService {
  private static instance: TimerService;
  private timers: Map<string, CookingTimer> = new Map();
  private reminders: Map<string, CookingReminder> = new Map();
  private timerIntervals: Map<string, NodeJS.Timeout> = new Map();
  private reminderTimeouts: Map<string, NodeJS.Timeout> = new Map();
  private timerListeners: Function[] = [];
  private reminderListeners: Function[] = [];

  private constructor() {
    this.initializeService();
  }

  public static getInstance(): TimerService {
    if (!TimerService.instance) {
      TimerService.instance = new TimerService();
    }
    return TimerService.instance;
  }

  private async initializeService(): Promise<void> {
    await this.loadTimersFromStorage();
    await this.loadRemindersFromStorage();
    this.restoreActiveTimers();
    this.scheduleReminders();
  }

  private async loadTimersFromStorage(): Promise<void> {
    try {
      const result = await chrome.storage.local.get(['cookingTimers']);
      if (result.cookingTimers) {
        const timers = JSON.parse(result.cookingTimers);
        timers.forEach((timer: CookingTimer) => {
          // Convert date strings back to Date objects
          timer.createdAt = new Date(timer.createdAt);
          if (timer.startedAt) timer.startedAt = new Date(timer.startedAt);
          if (timer.pausedAt) timer.pausedAt = new Date(timer.pausedAt);
          if (timer.completedAt) timer.completedAt = new Date(timer.completedAt);
          
          this.timers.set(timer.id, timer);
        });
      }
    } catch (error) {
      console.error('Failed to load timers from storage:', error);
    }
  }

  private async loadRemindersFromStorage(): Promise<void> {
    try {
      const result = await chrome.storage.local.get(['cookingReminders']);
      if (result.cookingReminders) {
        const reminders = JSON.parse(result.cookingReminders);
        reminders.forEach((reminder: CookingReminder) => {
          // Convert date strings back to Date objects
          reminder.scheduledTime = new Date(reminder.scheduledTime);
          reminder.createdAt = new Date(reminder.createdAt);
          if (reminder.recurringPattern?.endDate) {
            reminder.recurringPattern.endDate = new Date(reminder.recurringPattern.endDate);
          }
          
          this.reminders.set(reminder.id, reminder);
        });
      }
    } catch (error) {
      console.error('Failed to load reminders from storage:', error);
    }
  }

  private async saveTimersToStorage(): Promise<void> {
    try {
      const timers = Array.from(this.timers.values());
      await chrome.storage.local.set({
        cookingTimers: JSON.stringify(timers)
      });
    } catch (error) {
      console.error('Failed to save timers to storage:', error);
    }
  }

  private async saveRemindersToStorage(): Promise<void> {
    try {
      const reminders = Array.from(this.reminders.values());
      await chrome.storage.local.set({
        cookingReminders: JSON.stringify(reminders)
      });
    } catch (error) {
      console.error('Failed to save reminders to storage:', error);
    }
  }

  private restoreActiveTimers(): void {
    this.timers.forEach(timer => {
      if (timer.status === 'running' && timer.startedAt) {
        const elapsed = Math.floor((Date.now() - timer.startedAt.getTime()) / 1000);
        timer.remainingTime = Math.max(0, timer.duration - elapsed);
        
        if (timer.remainingTime > 0) {
          this.startTimerCountdown(timer.id);
        } else {
          this.completeTimer(timer.id);
        }
      }
    });
  }

  private scheduleReminders(): void {
    this.reminders.forEach(reminder => {
      if (reminder.status === 'scheduled') {
        this.scheduleReminderNotification(reminder.id);
      }
    });
  }

  // Timer Management
  public async createTimer(timerData: Omit<CookingTimer, 'id' | 'createdAt' | 'remainingTime' | 'status'>): Promise<string> {
    const id = `timer-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const timer: CookingTimer = {
      ...timerData,
      id,
      remainingTime: timerData.duration,
      status: 'created',
      createdAt: new Date()
    };

    this.timers.set(id, timer);
    await this.saveTimersToStorage();
    this.notifyTimerListeners('timer_created', timer);

    return id;
  }

  public async startTimer(timerId: string): Promise<boolean> {
    const timer = this.timers.get(timerId);
    if (!timer || timer.status === 'completed') return false;

    timer.status = 'running';
    timer.startedAt = new Date();
    
    if (timer.pausedAt) {
      // Resume from pause
      timer.pausedAt = undefined;
    }

    this.timers.set(timerId, timer);
    await this.saveTimersToStorage();
    
    this.startTimerCountdown(timerId);
    this.notifyTimerListeners('timer_started', timer);

    return true;
  }

  public async pauseTimer(timerId: string): Promise<boolean> {
    const timer = this.timers.get(timerId);
    if (!timer || timer.status !== 'running') return false;

    timer.status = 'paused';
    timer.pausedAt = new Date();

    this.timers.set(timerId, timer);
    await this.saveTimersToStorage();
    
    this.stopTimerCountdown(timerId);
    this.notifyTimerListeners('timer_paused', timer);

    return true;
  }

  public async stopTimer(timerId: string): Promise<boolean> {
    const timer = this.timers.get(timerId);
    if (!timer) return false;

    timer.status = 'cancelled';
    this.timers.set(timerId, timer);
    await this.saveTimersToStorage();
    
    this.stopTimerCountdown(timerId);
    this.notifyTimerListeners('timer_stopped', timer);

    return true;
  }

  public async resetTimer(timerId: string): Promise<boolean> {
    const timer = this.timers.get(timerId);
    if (!timer) return false;

    timer.status = 'created';
    timer.remainingTime = timer.duration;
    timer.startedAt = undefined;
    timer.pausedAt = undefined;
    timer.completedAt = undefined;

    this.timers.set(timerId, timer);
    await this.saveTimersToStorage();
    
    this.stopTimerCountdown(timerId);
    this.notifyTimerListeners('timer_reset', timer);

    return true;
  }

  private startTimerCountdown(timerId: string): void {
    const timer = this.timers.get(timerId);
    if (!timer) return;

    // Clear existing interval
    this.stopTimerCountdown(timerId);

    const interval = setInterval(() => {
      const currentTimer = this.timers.get(timerId);
      if (!currentTimer || currentTimer.status !== 'running') {
        clearInterval(interval);
        return;
      }

      currentTimer.remainingTime = Math.max(0, currentTimer.remainingTime - 1);
      this.timers.set(timerId, currentTimer);

      // Check for notification intervals
      this.checkTimerNotifications(currentTimer);

      // Update listeners
      this.notifyTimerListeners('timer_tick', currentTimer);

      // Complete timer when time runs out
      if (currentTimer.remainingTime <= 0) {
        this.completeTimer(timerId);
        clearInterval(interval);
      }
    }, 1000);

    this.timerIntervals.set(timerId, interval);
  }

  private stopTimerCountdown(timerId: string): void {
    const interval = this.timerIntervals.get(timerId);
    if (interval) {
      clearInterval(interval);
      this.timerIntervals.delete(timerId);
    }
  }

  private async completeTimer(timerId: string): Promise<void> {
    const timer = this.timers.get(timerId);
    if (!timer) return;

    timer.status = 'completed';
    timer.completedAt = new Date();
    timer.remainingTime = 0;

    this.timers.set(timerId, timer);
    await this.saveTimersToStorage();
    
    this.stopTimerCountdown(timerId);
    this.sendTimerCompletionNotification(timer);
    this.notifyTimerListeners('timer_completed', timer);
  }

  private checkTimerNotifications(timer: CookingTimer): void {
    if (!timer.notificationSettings.enabled) return;

    const intervals = timer.notificationSettings.intervals;
    if (intervals.includes(timer.remainingTime)) {
      this.sendTimerIntervalNotification(timer);
    }
  }

  private async sendTimerCompletionNotification(timer: CookingTimer): Promise<void> {
    const title = `Timer Complete: ${timer.name}`;
    const message = timer.notificationSettings.customMessage || 'Your cooking timer has finished!';

    await this.sendNotification(title, {
      body: message,
      icon: '/icons/icon48.png',
      tag: `timer-${timer.id}`,
      requireInteraction: true
    });

    // Play sound if enabled
    if (timer.notificationSettings.sound) {
      this.playNotificationSound();
    }

    // Vibrate if enabled and supported
    if (timer.notificationSettings.vibration && 'vibrate' in navigator) {
      navigator.vibrate([200, 100, 200]);
    }
  }

  private async sendTimerIntervalNotification(timer: CookingTimer): Promise<void> {
    const minutes = Math.floor(timer.remainingTime / 60);
    const seconds = timer.remainingTime % 60;
    const timeString = minutes > 0 ? `${minutes}:${seconds.toString().padStart(2, '0')}` : `${seconds}s`;

    await this.sendNotification(`${timer.name}`, {
      body: `${timeString} remaining`,
      icon: '/icons/icon48.png',
      tag: `timer-interval-${timer.id}`
    });
  }

  // Reminder Management
  public async createReminder(reminderData: Omit<CookingReminder, 'id' | 'createdAt' | 'status'>): Promise<string> {
    const id = `reminder-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const reminder: CookingReminder = {
      ...reminderData,
      id,
      status: 'scheduled',
      createdAt: new Date()
    };

    this.reminders.set(id, reminder);
    await this.saveRemindersToStorage();
    
    this.scheduleReminderNotification(id);
    this.notifyReminderListeners('reminder_created', reminder);

    return id;
  }

  public async updateReminder(reminderId: string, updates: Partial<CookingReminder>): Promise<boolean> {
    const reminder = this.reminders.get(reminderId);
    if (!reminder) return false;

    const updatedReminder = { ...reminder, ...updates };
    this.reminders.set(reminderId, updatedReminder);
    await this.saveRemindersToStorage();

    // Reschedule if time changed
    if (updates.scheduledTime) {
      this.cancelReminderNotification(reminderId);
      this.scheduleReminderNotification(reminderId);
    }

    this.notifyReminderListeners('reminder_updated', updatedReminder);
    return true;
  }

  public async deleteReminder(reminderId: string): Promise<boolean> {
    const reminder = this.reminders.get(reminderId);
    if (!reminder) return false;

    this.cancelReminderNotification(reminderId);
    this.reminders.delete(reminderId);
    await this.saveRemindersToStorage();

    this.notifyReminderListeners('reminder_deleted', reminder);
    return true;
  }

  private scheduleReminderNotification(reminderId: string): void {
    const reminder = this.reminders.get(reminderId);
    if (!reminder || reminder.status !== 'scheduled') return;

    const now = new Date();
    const delay = reminder.scheduledTime.getTime() - now.getTime();

    if (delay > 0) {
      const timeout = setTimeout(() => {
        this.sendReminderNotification(reminderId);
      }, delay);

      this.reminderTimeouts.set(reminderId, timeout);
    } else if (delay > -60000) { // Within last minute
      // Send immediately if just missed
      this.sendReminderNotification(reminderId);
    }
  }

  private cancelReminderNotification(reminderId: string): void {
    const timeout = this.reminderTimeouts.get(reminderId);
    if (timeout) {
      clearTimeout(timeout);
      this.reminderTimeouts.delete(reminderId);
    }
  }

  private async sendReminderNotification(reminderId: string): Promise<void> {
    const reminder = this.reminders.get(reminderId);
    if (!reminder) return;

    reminder.status = 'sent';
    this.reminders.set(reminderId, reminder);
    await this.saveRemindersToStorage();

    await this.sendNotification(reminder.title, {
      body: reminder.message,
      icon: '/icons/icon48.png',
      tag: `reminder-${reminder.id}`,
      requireInteraction: true
    });

    // Play sound if enabled
    if (reminder.notificationSettings.sound) {
      this.playNotificationSound();
    }

    this.notifyReminderListeners('reminder_sent', reminder);

    // Schedule next occurrence if recurring
    if (reminder.isRecurring && reminder.recurringPattern) {
      this.scheduleNextRecurrence(reminder);
    }
  }

  private async scheduleNextRecurrence(reminder: CookingReminder): Promise<void> {
    if (!reminder.recurringPattern) return;

    const nextTime = this.calculateNextRecurrence(reminder.scheduledTime, reminder.recurringPattern);
    if (!nextTime) return;

    const nextReminder: Omit<CookingReminder, 'id' | 'createdAt' | 'status'> = {
      ...reminder,
      scheduledTime: nextTime
    };

    await this.createReminder(nextReminder);
  }

  private calculateNextRecurrence(currentTime: Date, pattern: RecurringPattern): Date | null {
    const next = new Date(currentTime);

    switch (pattern.frequency) {
      case 'daily':
        next.setDate(next.getDate() + pattern.interval);
        break;
      case 'weekly':
        next.setDate(next.getDate() + (pattern.interval * 7));
        break;
      case 'monthly':
        next.setMonth(next.getMonth() + pattern.interval);
        break;
    }

    // Check if past end date
    if (pattern.endDate && next > pattern.endDate) {
      return null;
    }

    return next;
  }

  // Recipe Integration
  public async createRecipeTimers(recipe: Recipe): Promise<string[]> {
    const timerIds: string[] = [];

    for (const instruction of recipe.instructions) {
      const timers = this.extractTimersFromInstruction(instruction, recipe.id);
      
      for (const timerData of timers) {
        const timerId = await this.createTimer(timerData);
        timerIds.push(timerId);
      }
    }

    return timerIds;
  }

  private extractTimersFromInstruction(instruction: CookingStep, recipeId: string): Array<Omit<CookingTimer, 'id' | 'createdAt' | 'remainingTime' | 'status'>> {
    const timers: Array<Omit<CookingTimer, 'id' | 'createdAt' | 'remainingTime' | 'status'>> = [];
    const description = instruction.description.toLowerCase();

    // Extract time patterns
    const timePatterns = [
      /(\d+)\s*minutes?/g,
      /(\d+)\s*mins?/g,
      /(\d+)\s*hours?/g,
      /(\d+)\s*hrs?/g,
      /(\d+)-(\d+)\s*minutes?/g
    ];

    timePatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(description)) !== null) {
        let duration: number;
        let name: string;

        if (match[2]) {
          // Range pattern (e.g., "5-7 minutes")
          duration = parseInt(match[2]) * 60; // Use upper bound
          name = `${instruction.description.substring(0, 50)}... (${match[1]}-${match[2]} min)`;
        } else {
          const value = parseInt(match[1]);
          const unit = match[0].includes('hour') || match[0].includes('hr') ? 'hours' : 'minutes';
          duration = unit === 'hours' ? value * 3600 : value * 60;
          name = `${instruction.description.substring(0, 50)}... (${value} ${unit})`;
        }

        // Determine timer type based on context
        let type: TimerType = 'cooking';
        if (description.includes('marinate')) type = 'marinate';
        else if (description.includes('chill') || description.includes('refrigerate')) type = 'chill';
        else if (description.includes('rest') || description.includes('stand')) type = 'rest';
        else if (description.includes('prep')) type = 'prep';

        timers.push({
          name,
          duration,
          type,
          recipeId,
          stepId: instruction.step.toString(),
          notificationSettings: {
            enabled: true,
            sound: true,
            vibration: true,
            intervals: [300, 60, 30, 10] // 5 min, 1 min, 30 sec, 10 sec warnings
          }
        });
      }
    });

    return timers;
  }

  // Utility Methods
  public getTimers(): CookingTimer[] {
    return Array.from(this.timers.values());
  }

  public getTimer(timerId: string): CookingTimer | undefined {
    return this.timers.get(timerId);
  }

  public getActiveTimers(): CookingTimer[] {
    return Array.from(this.timers.values()).filter(timer => 
      timer.status === 'running' || timer.status === 'paused'
    );
  }

  public getReminders(): CookingReminder[] {
    return Array.from(this.reminders.values());
  }

  public getReminder(reminderId: string): CookingReminder | undefined {
    return this.reminders.get(reminderId);
  }

  public getUpcomingReminders(hours: number = 24): CookingReminder[] {
    const cutoff = new Date(Date.now() + hours * 60 * 60 * 1000);
    return Array.from(this.reminders.values()).filter(reminder => 
      reminder.status === 'scheduled' && reminder.scheduledTime <= cutoff
    );
  }

  private async sendNotification(title: string, options: NotificationOptions = {}): Promise<void> {
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(title, {
        icon: '/icons/icon48.png',
        badge: '/icons/icon16.png',
        ...options
      });
    } else if (chrome.notifications) {
      chrome.notifications.create({
        type: 'basic',
        iconUrl: '/icons/icon48.png',
        title: title,
        message: options.body || ''
      });
    }
  }

  private playNotificationSound(): void {
    // Play notification sound
    try {
      const audio = new Audio('/sounds/timer-complete.mp3');
      audio.play().catch(() => {
        // Fallback to system beep
        console.beep?.();
      });
    } catch (error) {
      console.log('Could not play notification sound');
    }
  }

  // Event Listeners
  public addTimerListener(callback: Function): void {
    this.timerListeners.push(callback);
  }

  public removeTimerListener(callback: Function): void {
    const index = this.timerListeners.indexOf(callback);
    if (index > -1) {
      this.timerListeners.splice(index, 1);
    }
  }

  public addReminderListener(callback: Function): void {
    this.reminderListeners.push(callback);
  }

  public removeReminderListener(callback: Function): void {
    const index = this.reminderListeners.indexOf(callback);
    if (index > -1) {
      this.reminderListeners.splice(index, 1);
    }
  }

  private notifyTimerListeners(event: string, timer: CookingTimer): void {
    this.timerListeners.forEach(callback => {
      try {
        callback(event, timer);
      } catch (error) {
        console.error('Error in timer listener:', error);
      }
    });
  }

  private notifyReminderListeners(event: string, reminder: CookingReminder): void {
    this.reminderListeners.forEach(callback => {
      try {
        callback(event, reminder);
      } catch (error) {
        console.error('Error in reminder listener:', error);
      }
    });
  }
}
