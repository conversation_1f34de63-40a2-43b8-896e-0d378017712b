import React, { useState } from 'react';
import { useTheme } from '../hooks/useTheme';
import { useI18n } from '../hooks/useI18n';
import { ThemeSettings } from '../types';

interface SettingsPanelProps {
  onClose?: () => void;
}

export const SettingsPanel: React.FC<SettingsPanelProps> = ({ onClose }) => {
  const { theme, updateTheme, resetTheme, toggleArtMode, getPresetThemes, exportTheme, importTheme } = useTheme();
  const { t, currentLanguage, changeLanguage, getAvailableLanguages } = useI18n();
  const [activeTab, setActiveTab] = useState<'appearance' | 'language' | 'ai' | 'privacy'>('appearance');
  const [apiKey, setApiKey] = useState('');

  const availableLanguages = getAvailableLanguages();
  const presetThemes = getPresetThemes();

  const handleColorChange = (type: 'primary' | 'accent', color: string) => {
    updateTheme({
      [type === 'primary' ? 'primaryColor' : 'accentColor']: color
    });
  };

  const handleExportTheme = () => {
    const themeData = exportTheme();
    const blob = new Blob([themeData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'chefai-theme.json';
    a.click();
    URL.revokeObjectURL(url);
  };

  const handleImportTheme = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = async (e) => {
        const content = e.target?.result as string;
        const success = await importTheme(content);
        if (success) {
          alert(t('settings.theme_imported_successfully'));
        } else {
          alert(t('settings.theme_import_failed'));
        }
      };
      reader.readAsText(file);
    }
  };

  const tabs = [
    { id: 'appearance', name: t('settings.appearance'), icon: '🎨' },
    { id: 'language', name: t('settings.language'), icon: '🌍' },
    { id: 'ai', name: t('settings.ai_configuration'), icon: '🤖' },
    { id: 'privacy', name: t('settings.privacy'), icon: '🔒' }
  ];

  return (
    <div className="settings-panel glass-container">
      <div className="settings-header">
        <h2 className="settings-title">{t('settings.title')}</h2>
        {onClose && (
          <button className="settings-close" onClick={onClose}>×</button>
        )}
      </div>

      <div className="settings-tabs">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            className={`settings-tab ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => setActiveTab(tab.id as any)}
          >
            <span className="tab-icon">{tab.icon}</span>
            <span className="tab-name">{tab.name}</span>
          </button>
        ))}
      </div>

      <div className="settings-content">
        {activeTab === 'appearance' && (
          <div className="settings-section">
            <h3>{t('settings.appearance')}</h3>
            
            {/* Theme Mode */}
            <div className="setting-item">
              <label>{t('settings.theme')}</label>
              <select
                className="glass-input"
                value={theme.mode}
                onChange={(e) => updateTheme({ mode: e.target.value as any })}
              >
                <option value="auto">{t('settings.theme_auto')}</option>
                <option value="light">{t('settings.theme_light')}</option>
                <option value="dark">{t('settings.theme_dark')}</option>
              </select>
            </div>

            {/* Primary Color */}
            <div className="setting-item">
              <label>{t('settings.primary_color')}</label>
              <div className="color-picker-container">
                <input
                  type="color"
                  value={theme.primaryColor}
                  onChange={(e) => handleColorChange('primary', e.target.value)}
                  className="color-picker"
                />
                <input
                  type="text"
                  value={theme.primaryColor}
                  onChange={(e) => handleColorChange('primary', e.target.value)}
                  className="glass-input color-input"
                  placeholder="#667eea"
                />
              </div>
            </div>

            {/* Accent Color */}
            <div className="setting-item">
              <label>{t('settings.accent_color')}</label>
              <div className="color-picker-container">
                <input
                  type="color"
                  value={theme.accentColor}
                  onChange={(e) => handleColorChange('accent', e.target.value)}
                  className="color-picker"
                />
                <input
                  type="text"
                  value={theme.accentColor}
                  onChange={(e) => handleColorChange('accent', e.target.value)}
                  className="glass-input color-input"
                  placeholder="#f093fb"
                />
              </div>
            </div>

            {/* Glassmorphism Intensity */}
            <div className="setting-item">
              <label>
                {t('settings.glassmorphism_intensity')} ({theme.glassmorphismIntensity}%)
              </label>
              <input
                type="range"
                min="0"
                max="100"
                value={theme.glassmorphismIntensity}
                onChange={(e) => updateTheme({ glassmorphismIntensity: Number(e.target.value) })}
                className="glass-slider"
              />
            </div>

            {/* Font Size */}
            <div className="setting-item">
              <label>{t('settings.font_size')}</label>
              <select
                className="glass-input"
                value={theme.fontSize}
                onChange={(e) => updateTheme({ fontSize: e.target.value as any })}
              >
                <option value="small">{t('settings.font_size_small')}</option>
                <option value="medium">{t('settings.font_size_medium')}</option>
                <option value="large">{t('settings.font_size_large')}</option>
              </select>
            </div>

            {/* Art Mode */}
            <div className="setting-item">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={theme.artMode}
                  onChange={toggleArtMode}
                />
                <span>{t('settings.art_mode')}</span>
              </label>
              <small>{t('settings.art_mode_description')}</small>
            </div>

            {/* Preset Themes */}
            <div className="setting-item">
              <label>{t('settings.preset_themes')}</label>
              <div className="preset-themes">
                {presetThemes.map((preset, index) => (
                  <button
                    key={index}
                    className="preset-theme-button glass-button"
                    onClick={() => updateTheme(preset.theme)}
                    style={{
                      background: `linear-gradient(135deg, ${preset.theme.primaryColor}, ${preset.theme.accentColor})`
                    }}
                  >
                    {preset.name}
                  </button>
                ))}
              </div>
            </div>

            {/* Theme Import/Export */}
            <div className="setting-item">
              <label>{t('settings.theme_backup')}</label>
              <div className="theme-backup-controls">
                <button className="glass-button" onClick={handleExportTheme}>
                  {t('settings.export_theme')}
                </button>
                <label className="glass-button import-button">
                  {t('settings.import_theme')}
                  <input
                    type="file"
                    accept=".json"
                    onChange={handleImportTheme}
                    style={{ display: 'none' }}
                  />
                </label>
              </div>
            </div>

            {/* Reset Theme */}
            <div className="setting-item">
              <button className="glass-button reset-button" onClick={resetTheme}>
                {t('settings.reset_theme')}
              </button>
            </div>
          </div>
        )}

        {activeTab === 'language' && (
          <div className="settings-section">
            <h3>{t('settings.language')}</h3>
            
            <div className="setting-item">
              <label>{t('settings.interface_language')}</label>
              <select
                className="glass-input"
                value={currentLanguage}
                onChange={(e) => changeLanguage(e.target.value)}
              >
                {availableLanguages.map((lang) => (
                  <option key={lang.code} value={lang.code}>
                    {lang.nativeName} ({lang.name})
                  </option>
                ))}
              </select>
            </div>

            <div className="setting-item">
              <label className="checkbox-label">
                <input type="checkbox" defaultChecked />
                <span>{t('settings.auto_detect_language')}</span>
              </label>
              <small>{t('settings.auto_detect_language_description')}</small>
            </div>

            <div className="setting-item">
              <label className="checkbox-label">
                <input type="checkbox" defaultChecked />
                <span>{t('settings.cultural_recipes')}</span>
              </label>
              <small>{t('settings.cultural_recipes_description')}</small>
            </div>
          </div>
        )}

        {activeTab === 'ai' && (
          <div className="settings-section">
            <h3>{t('settings.ai_configuration')}</h3>
            
            <div className="setting-item">
              <label>{t('settings.openai_api_key')}</label>
              <input
                type="password"
                className="glass-input"
                placeholder={t('settings.api_key_placeholder')}
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
              />
              <small>{t('settings.api_key_help')}</small>
            </div>

            <div className="setting-item">
              <label>{t('settings.ai_creativity_level')}</label>
              <input
                type="range"
                min="0"
                max="100"
                defaultValue="70"
                className="glass-slider"
              />
              <small>{t('settings.ai_creativity_description')}</small>
            </div>

            <div className="setting-item">
              <label className="checkbox-label">
                <input type="checkbox" defaultChecked />
                <span>{t('settings.enable_recipe_suggestions')}</span>
              </label>
            </div>

            <div className="setting-item">
              <label className="checkbox-label">
                <input type="checkbox" defaultChecked />
                <span>{t('settings.enable_nutritional_analysis')}</span>
              </label>
            </div>
          </div>
        )}

        {activeTab === 'privacy' && (
          <div className="settings-section">
            <h3>{t('settings.privacy')}</h3>
            
            <div className="setting-item">
              <label className="checkbox-label">
                <input type="checkbox" defaultChecked />
                <span>{t('settings.allow_data_collection')}</span>
              </label>
              <small>{t('settings.data_collection_description')}</small>
            </div>

            <div className="setting-item">
              <label className="checkbox-label">
                <input type="checkbox" defaultChecked />
                <span>{t('settings.share_anonymous_usage')}</span>
              </label>
              <small>{t('settings.anonymous_usage_description')}</small>
            </div>

            <div className="setting-item">
              <label className="checkbox-label">
                <input type="checkbox" />
                <span>{t('settings.enable_analytics')}</span>
              </label>
              <small>{t('settings.analytics_description')}</small>
            </div>

            <div className="setting-item">
              <button className="glass-button danger-button">
                {t('settings.clear_all_data')}
              </button>
              <small>{t('settings.clear_data_warning')}</small>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
