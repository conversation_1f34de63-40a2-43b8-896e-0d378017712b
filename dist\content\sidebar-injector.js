// Simplified LLM Service for content script
class LLMService {
  static instance = null;
  config = null;
  providers = new Map();

  static getInstance() {
    if (!LLMService.instance) {
      LLMService.instance = new LLMService();
    }
    return LLMService.instance;
  }

  constructor() {
    this.initializeProviders();
    this.loadConfiguration();
  }

  initializeProviders() {
    const providers = [
      {
        id: 'openrouter',
        name: 'OpenRouter',
        description: 'Access to multiple AI models through OpenRouter API',
        apiEndpoint: 'https://openrouter.ai/api/v1/chat/completions',
        requiresApiKey: true,
        maxTokens: 4096,
        models: [
          {
            id: 'anthropic/claude-3.5-sonnet',
            name: 'Claude 3.5 Sonnet',
            description: 'Anthropic\'s most capable model',
            contextLength: 200000,
            costPer1kTokens: 0.003
          },
          {
            id: 'openai/gpt-4-turbo',
            name: 'GPT-4 Turbo',
            description: 'OpenAI\'s most capable model',
            contextLength: 128000,
            costPer1kTokens: 0.01
          }
        ]
      },
      {
        id: 'gemini',
        name: 'Google Gemini',
        description: 'Google\'s native Gemini API',
        apiEndpoint: 'https://generativelanguage.googleapis.com/v1beta/models',
        requiresApiKey: true,
        maxTokens: 8192,
        models: [
          {
            id: 'gemini-1.5-pro',
            name: 'Gemini 1.5 Pro',
            description: 'Google\'s most capable model',
            contextLength: 2000000,
            costPer1kTokens: 0.0035
          }
        ]
      },
      {
        id: 'openai',
        name: 'OpenAI',
        description: 'Direct access to OpenAI\'s GPT models',
        apiEndpoint: 'https://api.openai.com/v1/chat/completions',
        requiresApiKey: true,
        maxTokens: 4096,
        models: [
          {
            id: 'gpt-4o',
            name: 'GPT-4o',
            description: 'OpenAI\'s flagship multimodal model',
            contextLength: 128000,
            costPer1kTokens: 0.005
          }
        ]
      },
      {
        id: 'anthropic',
        name: 'Anthropic',
        description: 'Direct access to Anthropic\'s Claude models',
        apiEndpoint: 'https://api.anthropic.com/v1/messages',
        requiresApiKey: true,
        maxTokens: 4096,
        models: [
          {
            id: 'claude-3-5-sonnet-20241022',
            name: 'Claude 3.5 Sonnet',
            description: 'Anthropic\'s most capable model',
            contextLength: 200000,
            costPer1kTokens: 0.003
          }
        ]
      }
    ];

    providers.forEach(provider => {
      this.providers.set(provider.id, provider);
    });
  }

  async loadConfiguration() {
    try {
      const result = await chrome.storage.local.get(['llmConfig']);
      if (result.llmConfig) {
        this.config = JSON.parse(result.llmConfig);
      } else {
        this.config = {
          provider: 'openrouter',
          model: 'anthropic/claude-3.5-sonnet',
          apiKey: '',
          temperature: 0.7,
          maxTokens: 2048,
          systemPrompt: 'You are ChefAI, an expert culinary assistant. Generate detailed, practical recipes with clear instructions, ingredient lists, and cooking tips. Focus on accuracy, safety, and delicious results.'
        };
      }
    } catch (error) {
      console.error('Failed to load LLM configuration:', error);
    }
  }

  async saveConfiguration(config) {
    if (config) {
      this.config = config;
    }
    
    try {
      await chrome.storage.local.set({
        llmConfig: JSON.stringify(this.config)
      });
    } catch (error) {
      console.error('Failed to save LLM configuration:', error);
    }
  }

  getConfiguration() {
    return this.config;
  }

  getProviders() {
    return Array.from(this.providers.values());
  }

  async generateRecipe(request) {
    if (!this.config || !this.config.apiKey) {
      return {
        success: false,
        error: 'LLM configuration not set. Please configure your API key in settings.'
      };
    }

    const provider = this.providers.get(this.config.provider);
    if (!provider) {
      return {
        success: false,
        error: 'Invalid LLM provider configured.'
      };
    }

    try {
      const prompt = this.buildRecipePrompt(request);
      const response = await this.callLLMAPI(prompt);
      
      if (response.success && response.data) {
        const recipe = this.parseRecipeResponse(response.data);
        return {
          success: true,
          data: recipe,
          usage: response.usage
        };
      }
      
      return response;
    } catch (error) {
      console.error('Recipe generation failed:', error);
      return {
        success: false,
        error: 'Failed to generate recipe: ' + error.message
      };
    }
  }

  buildRecipePrompt(request) {
    let prompt = 'Generate a detailed recipe with the following requirements:\n\n';

    if (request.ingredients && request.ingredients.length > 0) {
      prompt += 'Available ingredients: ' + request.ingredients.join(', ') + '\n';
    }

    if (request.cuisineType) {
      prompt += 'Cuisine type: ' + request.cuisineType + '\n';
    }

    if (request.dietaryRestrictions && request.dietaryRestrictions.length > 0) {
      prompt += 'Dietary restrictions: ' + request.dietaryRestrictions.join(', ') + '\n';
    }

    if (request.servings) {
      prompt += 'Servings: ' + request.servings + '\n';
    }

    if (request.cookingTime) {
      prompt += 'Maximum cooking time: ' + request.cookingTime + ' minutes\n';
    }

    if (request.difficulty) {
      prompt += 'Difficulty level: ' + request.difficulty + '\n';
    }

    if (request.mealType) {
      prompt += 'Meal type: ' + request.mealType + '\n';
    }

    if (request.customPrompt) {
      prompt += 'Additional requirements: ' + request.customPrompt + '\n';
    }

    prompt += '\nPlease provide the recipe in the following JSON format:\n{\n  "title": "Recipe Name",\n  "description": "Brief description",\n  "servings": number,\n  "prepTime": number,\n  "cookTime": number,\n  "difficulty": "Easy|Medium|Hard",\n  "cuisine": "cuisine type",\n  "ingredients": [{"name": "ingredient", "amount": number, "unit": "unit", "category": "category"}],\n  "instructions": [{"step": number, "description": "instruction", "duration": number}],\n  "tips": ["tip1", "tip2"],\n  "nutrition": {"calories": number, "protein": number, "carbs": number, "fat": number}\n}\n\nEnsure all measurements are precise, instructions are clear and detailed, and the recipe is practical and delicious.';

    return prompt;
  }

  async callLLMAPI(prompt) {
    if (!this.config) {
      return { success: false, error: 'No configuration available' };
    }

    const provider = this.providers.get(this.config.provider);
    if (!provider) {
      return { success: false, error: 'Provider not found' };
    }

    try {
      let response;
      let requestBody;

      switch (this.config.provider) {
        case 'openrouter':
          requestBody = {
            model: this.config.model,
            messages: [
              { role: 'system', content: this.config.systemPrompt },
              { role: 'user', content: prompt }
            ],
            temperature: this.config.temperature,
            max_tokens: this.config.maxTokens
          };

          response = await fetch(provider.apiEndpoint, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer ' + this.config.apiKey,
              'HTTP-Referer': chrome.runtime.getURL(''),
              'X-Title': 'ChefAI Extension'
            },
            body: JSON.stringify(requestBody)
          });
          break;

        case 'gemini':
          const geminiUrl = provider.apiEndpoint + '/' + this.config.model + ':generateContent?key=' + this.config.apiKey;
          requestBody = {
            contents: [{
              parts: [{
                text: this.config.systemPrompt + '\n\n' + prompt
              }]
            }],
            generationConfig: {
              temperature: this.config.temperature,
              maxOutputTokens: this.config.maxTokens
            }
          };

          response = await fetch(geminiUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
          });
          break;

        case 'openai':
          requestBody = {
            model: this.config.model,
            messages: [
              { role: 'system', content: this.config.systemPrompt },
              { role: 'user', content: prompt }
            ],
            temperature: this.config.temperature,
            max_tokens: this.config.maxTokens
          };

          response = await fetch(provider.apiEndpoint, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer ' + this.config.apiKey
            },
            body: JSON.stringify(requestBody)
          });
          break;

        case 'anthropic':
          requestBody = {
            model: this.config.model,
            max_tokens: this.config.maxTokens,
            temperature: this.config.temperature,
            system: this.config.systemPrompt,
            messages: [
              { role: 'user', content: prompt }
            ]
          };

          response = await fetch(provider.apiEndpoint, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'x-api-key': this.config.apiKey,
              'anthropic-version': '2023-06-01'
            },
            body: JSON.stringify(requestBody)
          });
          break;

        default:
          return { success: false, error: 'Unsupported provider' };
      }

      if (!response.ok) {
        const errorText = await response.text();
        return {
          success: false,
          error: 'API request failed: ' + response.status + ' ' + response.statusText + ' - ' + errorText
        };
      }

      const data = await response.json();
      return this.parseAPIResponse(data, this.config.provider);

    } catch (error) {
      console.error('LLM API call failed:', error);
      return {
        success: false,
        error: 'Network error: ' + error.message
      };
    }
  }

  parseAPIResponse(data, provider) {
    try {
      let content;
      let usage = {};

      switch (provider) {
        case 'openrouter':
        case 'openai':
          content = data.choices[0].message.content;
          usage = {
            promptTokens: data.usage?.prompt_tokens || 0,
            completionTokens: data.usage?.completion_tokens || 0,
            totalTokens: data.usage?.total_tokens || 0
          };
          break;

        case 'gemini':
          content = data.candidates[0].content.parts[0].text;
          usage = {
            promptTokens: data.usageMetadata?.promptTokenCount || 0,
            completionTokens: data.usageMetadata?.candidatesTokenCount || 0,
            totalTokens: data.usageMetadata?.totalTokenCount || 0
          };
          break;

        case 'anthropic':
          content = data.content[0].text;
          usage = {
            promptTokens: data.usage?.input_tokens || 0,
            completionTokens: data.usage?.output_tokens || 0,
            totalTokens: (data.usage?.input_tokens || 0) + (data.usage?.output_tokens || 0)
          };
          break;

        default:
          return { success: false, error: 'Unknown provider response format' };
      }

      return {
        success: true,
        data: content,
        usage
      };

    } catch (error) {
      return {
        success: false,
        error: 'Failed to parse API response: ' + error.message
      };
    }
  }

  parseRecipeResponse(content) {
    try {
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }

      const recipeData = JSON.parse(jsonMatch[0]);
      
      const recipe = {
        id: 'llm-recipe-' + Date.now(),
        title: recipeData.title || 'Generated Recipe',
        description: recipeData.description || '',
        servings: recipeData.servings || 4,
        prepTime: recipeData.prepTime || 15,
        cookTime: recipeData.cookTime || 30,
        difficulty: recipeData.difficulty || 'Medium',
        cuisine: recipeData.cuisine || 'International',
        ingredients: recipeData.ingredients?.map((ing, index) => ({
          id: 'ing-' + index,
          name: ing.name,
          amount: ing.amount,
          unit: ing.unit,
          category: ing.category || 'Other'
        })) || [],
        instructions: recipeData.instructions?.map((inst) => ({
          step: inst.step,
          description: inst.description,
          duration: inst.duration || 0
        })) || [],
        tags: recipeData.tags || [],
        nutrition: recipeData.nutrition || {
          calories: 0,
          protein: 0,
          carbs: 0,
          fat: 0,
          fiber: 0,
          sugar: 0,
          sodium: 0
        },
        tips: recipeData.tips || [],
        createdAt: new Date(),
        source: 'AI Generated'
      };

      return recipe;

    } catch (error) {
      console.error('Failed to parse recipe response:', error);
      return null;
    }
  }

  async testConnection() {
    if (!this.config || !this.config.apiKey) {
      return {
        success: false,
        error: 'No API key configured'
      };
    }

    try {
      const testPrompt = 'Respond with "Connection successful" if you can read this message.';
      const response = await this.callLLMAPI(testPrompt);
      
      if (response.success) {
        return {
          success: true,
          data: 'Connection test successful',
          usage: response.usage
        };
      }
      
      return response;
    } catch (error) {
      return {
        success: false,
        error: 'Connection test failed: ' + error.message
      };
    }
  }
}

// Simplified Recipe Service
class RecipeService {
  static instance = null;

  static getInstance() {
    if (!RecipeService.instance) {
      RecipeService.instance = new RecipeService();
    }
    return RecipeService.instance;
  }

  async saveRecipe(recipe) {
    try {
      const result = await chrome.storage.local.get(['recipes']);
      const recipes = result.recipes ? JSON.parse(result.recipes) : [];
      
      recipes.unshift(recipe);
      
      await chrome.storage.local.set({
        recipes: JSON.stringify(recipes)
      });
      
      return recipe.id;
    } catch (error) {
      console.error('Failed to save recipe:', error);
      throw error;
    }
  }

  async getRecipes() {
    try {
      const result = await chrome.storage.local.get(['recipes']);
      return result.recipes ? JSON.parse(result.recipes) : [];
    } catch (error) {
      console.error('Failed to load recipes:', error);
      return [];
    }
  }
}

// Sidebar Injector - Injects ChefAI sidebar into web pages
class SidebarInjector {
  constructor() {
    this.state = {
      isVisible: false,
      isInitialized: false,
      container: null
    };

    this.llmService = LLMService.getInstance();
    this.recipeService = RecipeService.getInstance();
    this.floatingButton = null;
    this.init();
  }

  async init() {
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setupSidebar());
    } else {
      this.setupSidebar();
    }

    // Listen for messages from extension
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      if (message.action === 'toggleSidebar') {
        this.toggleSidebar();
        sendResponse({ success: true });
      } else if (message.action === 'showSidebar') {
        this.showSidebar();
        sendResponse({ success: true });
      } else if (message.action === 'hideSidebar') {
        this.hideSidebar();
        sendResponse({ success: true });
      }
    });
  }

  setupSidebar() {
    if (this.state.isInitialized) return;

    // Create floating button
    this.createFloatingButton();

    // Create sidebar container
    this.createSidebarContainer();

    // Load CSS
    this.loadSidebarStyles();

    this.state.isInitialized = true;
  }

  createFloatingButton() {
    this.floatingButton = document.createElement('div');
    this.floatingButton.id = 'chefai-floating-button';
    this.floatingButton.innerHTML = '🍳';
    this.floatingButton.title = 'Open ChefAI Assistant';

    // Apply styles
    Object.assign(this.floatingButton.style, {
      position: 'fixed',
      top: '20px',
      right: '20px',
      width: '60px',
      height: '60px',
      borderRadius: '50%',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      color: 'white',
      fontSize: '24px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      cursor: 'pointer',
      zIndex: '9999',
      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
      transition: 'all 0.3s ease',
      border: '2px solid rgba(255, 255, 255, 0.2)',
      backdropFilter: 'blur(10px)'
    });

    // Add hover effects
    this.floatingButton.addEventListener('mouseenter', () => {
      if (this.floatingButton) {
        this.floatingButton.style.transform = 'scale(1.1)';
        this.floatingButton.style.boxShadow = '0 6px 25px rgba(0, 0, 0, 0.4)';
      }
    });

    this.floatingButton.addEventListener('mouseleave', () => {
      if (this.floatingButton) {
        this.floatingButton.style.transform = 'scale(1)';
        this.floatingButton.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.3)';
      }
    });

    // Add click handler
    this.floatingButton.addEventListener('click', () => {
      this.toggleSidebar();
    });

    document.body.appendChild(this.floatingButton);
  }

  createSidebarContainer() {
    this.state.container = document.createElement('div');
    this.state.container.id = 'chefai-sidebar-container';
    this.state.container.style.display = 'none';

    document.body.appendChild(this.state.container);
  }

  loadSidebarStyles() {
    // Check if styles are already loaded
    if (document.getElementById('chefai-sidebar-styles')) return;

    const link = document.createElement('link');
    link.id = 'chefai-sidebar-styles';
    link.rel = 'stylesheet';
    link.href = chrome.runtime.getURL('src/styles/sidebar.css');
    document.head.appendChild(link);
  }

  toggleSidebar() {
    if (this.state.isVisible) {
      this.hideSidebar();
    } else {
      this.showSidebar();
    }
  }

  showSidebar() {
    if (!this.state.container) return;

    this.state.isVisible = true;
    this.state.container.style.display = 'block';

    // Render the sidebar interface
    this.renderSidebarInterface();

    // Hide floating button when sidebar is open
    if (this.floatingButton) {
      this.floatingButton.style.display = 'none';
    }
  }

  hideSidebar() {
    if (!this.state.container) return;

    this.state.isVisible = false;
    this.state.container.style.display = 'none';
    this.state.container.innerHTML = '';

    // Show floating button when sidebar is closed
    if (this.floatingButton) {
      this.floatingButton.style.display = 'flex';
    }
  }

  renderSidebarInterface() {
    if (!this.state.container) return;

    // Create the sidebar HTML structure
    this.state.container.innerHTML = `
      <div class="sidebar-interface">
        <div class="sidebar-overlay"></div>
        <div class="sidebar-content">
          <div class="sidebar-header">
            <h2>🍳 ChefAI Assistant</h2>
            <button class="close-btn" id="chefai-close-btn">×</button>
          </div>

          <div class="sidebar-tabs">
            <button class="tab active" data-tab="generate">
              <span class="tab-icon">✨</span>
              Generate Recipe
            </button>
            <button class="tab" data-tab="settings">
              <span class="tab-icon">⚙️</span>
              LLM Settings
            </button>
            <button class="tab" data-tab="history">
              <span class="tab-icon">📚</span>
              Recent Recipes
            </button>
          </div>

          <div class="tab-content" id="chefai-tab-content">
            <!-- Tab content will be rendered here -->
          </div>
        </div>
      </div>
    `;

    // Set up event listeners
    this.setupSidebarEventListeners();

    // Render initial tab content
    this.renderTabContent('generate');
  }

  setupSidebarEventListeners() {
    if (!this.state.container) return;

    // Close button
    const closeBtn = this.state.container.querySelector('#chefai-close-btn');
    closeBtn?.addEventListener('click', () => this.hideSidebar());

    // Overlay click to close
    const overlay = this.state.container.querySelector('.sidebar-overlay');
    overlay?.addEventListener('click', () => this.hideSidebar());

    // Tab switching
    const tabs = this.state.container.querySelectorAll('.tab');
    tabs.forEach(tab => {
      tab.addEventListener('click', () => {
        const tabName = tab.getAttribute('data-tab');
        if (tabName) {
          // Update active tab
          tabs.forEach(t => t.classList.remove('active'));
          tab.classList.add('active');

          // Render tab content
          this.renderTabContent(tabName);
        }
      });
    });
  }

  renderTabContent(tabName) {
    const contentContainer = this.state.container?.querySelector('#chefai-tab-content');
    if (!contentContainer) return;

    switch (tabName) {
      case 'generate':
        this.renderGenerateTab(contentContainer);
        break;
      case 'settings':
        this.renderSettingsTab(contentContainer);
        break;
      case 'history':
        this.renderHistoryTab(contentContainer);
        break;
    }
  }

  renderGenerateTab(container) {
    container.innerHTML = `
      <div class="generate-tab">
        <div class="form-section">
          <h3>🥘 Recipe Requirements</h3>

          <div class="input-group">
            <label>Available Ingredients</label>
            <div class="ingredient-input">
              <input type="text" id="ingredient-input" placeholder="Enter an ingredient..." class="glass-input">
              <button id="add-ingredient-btn" class="add-btn glass-button">Add</button>
            </div>
            <div id="ingredients-list" class="ingredients-list"></div>
          </div>

          <div class="parameters-grid">
            <div class="input-group">
              <label>Cuisine Type</label>
              <select id="cuisine-select" class="glass-select">
                <option value="">Any Cuisine</option>
                <option value="Italian">Italian</option>
                <option value="Asian">Asian</option>
                <option value="Mexican">Mexican</option>
                <option value="Mediterranean">Mediterranean</option>
                <option value="Indian">Indian</option>
                <option value="French">French</option>
                <option value="American">American</option>
                <option value="Thai">Thai</option>
                <option value="Japanese">Japanese</option>
                <option value="Middle Eastern">Middle Eastern</option>
              </select>
            </div>

            <div class="input-group">
              <label>Meal Type</label>
              <select id="meal-type-select" class="glass-select">
                <option value="breakfast">Breakfast</option>
                <option value="lunch">Lunch</option>
                <option value="dinner" selected>Dinner</option>
                <option value="snack">Snack</option>
                <option value="dessert">Dessert</option>
                <option value="appetizer">Appetizer</option>
              </select>
            </div>

            <div class="input-group">
              <label>Servings</label>
              <input type="number" id="servings-input" min="1" max="12" value="4" class="glass-input">
            </div>

            <div class="input-group">
              <label>Max Cooking Time (minutes)</label>
              <input type="number" id="cook-time-input" min="5" max="240" value="30" class="glass-input">
            </div>

            <div class="input-group">
              <label>Difficulty</label>
              <select id="difficulty-select" class="glass-select">
                <option value="Easy">Easy</option>
                <option value="Medium" selected>Medium</option>
                <option value="Hard">Hard</option>
              </select>
            </div>
          </div>

          <div class="input-group">
            <label>Dietary Restrictions</label>
            <div class="dietary-options">
              <label class="checkbox-label">
                <input type="checkbox" value="Vegetarian"> Vegetarian
              </label>
              <label class="checkbox-label">
                <input type="checkbox" value="Vegan"> Vegan
              </label>
              <label class="checkbox-label">
                <input type="checkbox" value="Gluten-Free"> Gluten-Free
              </label>
              <label class="checkbox-label">
                <input type="checkbox" value="Dairy-Free"> Dairy-Free
              </label>
              <label class="checkbox-label">
                <input type="checkbox" value="Keto"> Keto
              </label>
              <label class="checkbox-label">
                <input type="checkbox" value="Low-Carb"> Low-Carb
              </label>
              <label class="checkbox-label">
                <input type="checkbox" value="Paleo"> Paleo
              </label>
            </div>
          </div>

          <div class="input-group">
            <label>Additional Requirements (Optional)</label>
            <textarea id="custom-prompt" placeholder="Any specific requirements, cooking methods, or preferences..." class="glass-textarea" rows="3"></textarea>
          </div>

          <button id="generate-recipe-btn" class="generate-btn glass-button primary">
            <span class="btn-icon">✨</span>
            Generate Recipe with AI
          </button>

          <div id="generation-status" class="warning-message" style="display: none;"></div>
        </div>
      </div>
    `;

    this.setupGenerateTabListeners();
  }

  setupGenerateTabListeners() {
    const ingredients = [];

    // Add ingredient functionality
    const ingredientInput = document.getElementById('ingredient-input');
    const addBtn = document.getElementById('add-ingredient-btn');
    const ingredientsList = document.getElementById('ingredients-list');

    const addIngredient = () => {
      const ingredient = ingredientInput?.value.trim();
      if (ingredient && !ingredients.includes(ingredient)) {
        ingredients.push(ingredient);
        this.updateIngredientsDisplay(ingredients, ingredientsList);
        if (ingredientInput) ingredientInput.value = '';
      }
    };

    addBtn?.addEventListener('click', addIngredient);
    ingredientInput?.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') addIngredient();
    });

    // Generate recipe functionality
    const generateBtn = document.getElementById('generate-recipe-btn');
    generateBtn?.addEventListener('click', () => this.handleGenerateRecipe(ingredients));
  }

  updateIngredientsDisplay(ingredients, container) {
    if (!container) return;

    container.innerHTML = ingredients.map(ingredient => `
      <span class="ingredient-tag">
        ${ingredient}
        <button class="remove-ingredient" data-ingredient="${ingredient}">×</button>
      </span>
    `).join('');

    // Add remove functionality
    container.querySelectorAll('.remove-ingredient').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const ingredient = e.target.getAttribute('data-ingredient');
        if (ingredient) {
          const index = ingredients.indexOf(ingredient);
          if (index > -1) {
            ingredients.splice(index, 1);
            this.updateIngredientsDisplay(ingredients, container);
          }
        }
      });
    });
  }

  async handleGenerateRecipe(ingredients) {
    const generateBtn = document.getElementById('generate-recipe-btn');
    const statusDiv = document.getElementById('generation-status');

    if (!generateBtn || !statusDiv) return;

    // Check if LLM is configured
    const config = this.llmService.getConfiguration();
    if (!config || !config.apiKey) {
      statusDiv.textContent = '⚠️ Please configure your LLM API key in settings first.';
      statusDiv.style.display = 'block';
      return;
    }

    // Disable button and show loading
    generateBtn.innerHTML = '<span class="spinner"></span> Generating Recipe...';
    generateBtn.disabled = true;
    statusDiv.style.display = 'none';

    try {
      // Collect form data
      const request = this.collectGenerationRequest(ingredients);

      // Generate recipe
      const response = await this.llmService.generateRecipe(request);

      if (response.success && response.data) {
        // Save recipe
        await this.recipeService.saveRecipe(response.data);

        // Show success and close sidebar
        statusDiv.textContent = '✅ Recipe generated successfully! Check your recipes.';
        statusDiv.style.display = 'block';
        statusDiv.style.background = 'rgba(76, 175, 80, 0.2)';
        statusDiv.style.borderColor = 'rgba(76, 175, 80, 0.5)';

        // Auto-close after 2 seconds
        setTimeout(() => this.hideSidebar(), 2000);
      } else {
        statusDiv.textContent = `❌ Failed to generate recipe: ${response.error}`;
        statusDiv.style.display = 'block';
      }
    } catch (error) {
      statusDiv.textContent = '❌ An error occurred while generating the recipe.';
      statusDiv.style.display = 'block';
    } finally {
      // Re-enable button
      generateBtn.innerHTML = '<span class="btn-icon">✨</span> Generate Recipe with AI';
      generateBtn.disabled = false;
    }
  }

  collectGenerationRequest(ingredients) {
    const cuisineSelect = document.getElementById('cuisine-select');
    const mealTypeSelect = document.getElementById('meal-type-select');
    const servingsInput = document.getElementById('servings-input');
    const cookTimeInput = document.getElementById('cook-time-input');
    const difficultySelect = document.getElementById('difficulty-select');
    const customPrompt = document.getElementById('custom-prompt');

    // Collect dietary restrictions
    const dietaryCheckboxes = document.querySelectorAll('.dietary-options input[type="checkbox"]:checked');
    const dietaryRestrictions = Array.from(dietaryCheckboxes).map(cb => cb.value);

    return {
      ingredients,
      cuisineType: cuisineSelect?.value || undefined,
      mealType: mealTypeSelect?.value || 'dinner',
      servings: parseInt(servingsInput?.value || '4'),
      cookingTime: parseInt(cookTimeInput?.value || '30'),
      difficulty: difficultySelect?.value || 'Medium',
      dietaryRestrictions: dietaryRestrictions.length > 0 ? dietaryRestrictions : undefined,
      customPrompt: customPrompt?.value || undefined
    };
  }

  renderSettingsTab(container) {
    container.innerHTML = `
      <div class="settings-tab">
        <div class="llm-settings">
          <h3>🤖 LLM Configuration</h3>
          <div id="settings-content">Loading settings...</div>
        </div>
      </div>
    `;

    this.loadLLMSettings();
  }

  async loadLLMSettings() {
    const settingsContent = document.getElementById('settings-content');
    if (!settingsContent) return;

    const config = this.llmService.getConfiguration();
    const providers = this.llmService.getProviders();

    settingsContent.innerHTML = `
      <div class="settings-form">
        <div class="input-group">
          <label>AI Provider</label>
          <select id="provider-select" class="glass-select">
            ${providers.map(p => `
              <option value="${p.id}" ${config?.provider === p.id ? 'selected' : ''}>
                ${p.name}
              </option>
            `).join('')}
          </select>
        </div>

        <div class="input-group">
          <label>API Key</label>
          <input type="password" id="api-key-input" value="${config?.apiKey || ''}" placeholder="Enter your API key..." class="glass-input">
          <small>Get your API key from the provider's website</small>
        </div>

        <div class="input-group">
          <label>Temperature (${config?.temperature || 0.7})</label>
          <input type="range" id="temperature-range" min="0" max="2" step="0.1" value="${config?.temperature || 0.7}" class="glass-range">
          <small>Higher = more creative, Lower = more focused</small>
        </div>

        <div class="settings-actions">
          <button id="test-connection-btn" class="test-btn glass-button">Test Connection</button>
          <button id="save-config-btn" class="save-btn glass-button primary">Save Configuration</button>
        </div>

        <div id="test-result" class="test-result" style="display: none;"></div>
      </div>
    `;

    this.setupSettingsListeners();
  }

  setupSettingsListeners() {
    const saveBtn = document.getElementById('save-config-btn');
    const testBtn = document.getElementById('test-connection-btn');

    saveBtn?.addEventListener('click', () => this.saveConfiguration());
    testBtn?.addEventListener('click', () => this.testConnection());
  }

  async saveConfiguration() {
    const providerSelect = document.getElementById('provider-select');
    const apiKeyInput = document.getElementById('api-key-input');
    const temperatureRange = document.getElementById('temperature-range');

    const config = {
      provider: providerSelect?.value || 'openrouter',
      model: 'anthropic/claude-3.5-sonnet', // Default model
      apiKey: apiKeyInput?.value || '',
      temperature: parseFloat(temperatureRange?.value || '0.7'),
      maxTokens: 2048,
      systemPrompt: 'You are ChefAI, an expert culinary assistant. Generate detailed, practical recipes with clear instructions, ingredient lists, and cooking tips.'
    };

    try {
      await this.llmService.saveConfiguration(config);
      alert('Configuration saved successfully!');
    } catch (error) {
      alert('Failed to save configuration.');
    }
  }

  async testConnection() {
    const testBtn = document.getElementById('test-connection-btn');
    const testResult = document.getElementById('test-result');

    if (!testBtn || !testResult) return;

    testBtn.textContent = 'Testing...';
    testBtn.disabled = true;
    testResult.style.display = 'none';

    try {
      const result = await this.llmService.testConnection();
      testResult.textContent = result.success ? '✅ Connection successful!' : `❌ ${result.error}`;
      testResult.className = `test-result ${result.success ? 'success' : 'error'}`;
      testResult.style.display = 'block';
    } catch (error) {
      testResult.textContent = `❌ Connection failed: ${error.message}`;
      testResult.className = 'test-result error';
      testResult.style.display = 'block';
    } finally {
      testBtn.textContent = 'Test Connection';
      testBtn.disabled = false;
    }
  }

  async renderHistoryTab(container) {
    container.innerHTML = `
      <div class="history-tab">
        <h3>📚 Recent AI Recipes</h3>
        <div id="recipes-list">Loading recipes...</div>
      </div>
    `;

    try {
      const recipes = await this.recipeService.getRecipes();
      const aiRecipes = recipes.filter(r => r.source === 'AI Generated').slice(0, 10);

      const recipesList = document.getElementById('recipes-list');
      if (!recipesList) return;

      if (aiRecipes.length === 0) {
        recipesList.innerHTML = `
          <div class="empty-state">
            <p>No AI-generated recipes yet.</p>
            <p>Generate your first recipe to see it here!</p>
          </div>
        `;
      } else {
        recipesList.innerHTML = aiRecipes.map(recipe => `
          <div class="recipe-item glass-card">
            <h4>${recipe.title}</h4>
            <p>${recipe.description}</p>
            <div class="recipe-meta">
              <span>🍽️ ${recipe.servings} servings</span>
              <span>⏱️ ${recipe.cookTime} min</span>
              <span>📊 ${recipe.difficulty}</span>
            </div>
          </div>
        `).join('');
      }
    } catch (error) {
      const recipesList = document.getElementById('recipes-list');
      if (recipesList) {
        recipesList.innerHTML = '<div class="empty-state"><p>Failed to load recipes.</p></div>';
      }
    }
  }
}

// Initialize the sidebar injector
new SidebarInjector();
