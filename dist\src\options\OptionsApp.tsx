import React, { useState, useEffect } from 'react';
import { Recipe, UserPreferences } from '../types';
import { useStorage } from '../hooks/useStorage';
import { useI18n } from '../hooks/useI18n';

const OptionsApp: React.FC = () => {
  const [currentTab, setCurrentTab] = useState('dashboard');
  const { data: userPreferences, updateData: updatePreferences } = useStorage<UserPreferences>('userPreferences');
  const { data: savedRecipes } = useStorage<Recipe[]>('savedRecipes', []);
  const { t, currentLanguage, changeLanguage } = useI18n();

  const tabs = [
    { id: 'dashboard', name: t('tabs.dashboard'), icon: '📊' },
    { id: 'recipes', name: t('tabs.recipes'), icon: '📚' },
    { id: 'generate', name: t('tabs.generate'), icon: '✨' },
    { id: 'search', name: t('tabs.search'), icon: '🔍' },
    { id: 'trending', name: t('tabs.trending'), icon: '🔥' },
    { id: 'settings', name: t('tabs.settings'), icon: '⚙️' }
  ];

  useEffect(() => {
    // Handle URL hash navigation
    const hash = window.location.hash.substring(1);
    if (hash && tabs.some(tab => tab.id === hash)) {
      setCurrentTab(hash);
    }
  }, []);

  const handleTabChange = (tabId: string) => {
    setCurrentTab(tabId);
    window.location.hash = tabId;
  };

  return (
    <div className="options-container">
      {/* Header */}
      <header className="options-header glass-container">
        <div className="header-content">
          <div className="logo-section">
            <div className="app-logo">🍳</div>
            <div>
              <h1 className="app-title">{t('app.title')}</h1>
              <p className="app-subtitle">{t('app.subtitle')}</p>
            </div>
          </div>
          
          <div className="header-actions">
            <select 
              className="glass-input language-selector"
              value={currentLanguage}
              onChange={(e) => changeLanguage(e.target.value)}
            >
              <option value="en">🇺🇸 English</option>
              <option value="ar">🇸🇦 العربية</option>
              <option value="fr">🇫🇷 Français</option>
              <option value="es">🇪🇸 Español</option>
            </select>
          </div>
        </div>
      </header>

      {/* Navigation */}
      <nav className="options-nav glass-container">
        <div className="nav-tabs">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              className={`nav-tab ${currentTab === tab.id ? 'active' : ''}`}
              onClick={() => handleTabChange(tab.id)}
            >
              <span className="tab-icon">{tab.icon}</span>
              <span className="tab-name">{tab.name}</span>
            </button>
          ))}
        </div>
      </nav>

      {/* Main Content */}
      <main className="options-main">
        {currentTab === 'dashboard' && (
          <div className="dashboard-content">
            <div className="glass-card">
              <h2>{t('dashboard.welcome')}</h2>
              <p>{t('dashboard.description')}</p>
              
              <div className="stats-grid">
                <div className="stat-card glass-card-sm">
                  <div className="stat-icon">📚</div>
                  <div className="stat-info">
                    <div className="stat-number">{savedRecipes?.length || 0}</div>
                    <div className="stat-label">{t('dashboard.saved_recipes')}</div>
                  </div>
                </div>
                
                <div className="stat-card glass-card-sm">
                  <div className="stat-icon">✨</div>
                  <div className="stat-info">
                    <div className="stat-number">0</div>
                    <div className="stat-label">{t('dashboard.generated_recipes')}</div>
                  </div>
                </div>
                
                <div className="stat-card glass-card-sm">
                  <div className="stat-icon">🔍</div>
                  <div className="stat-info">
                    <div className="stat-number">0</div>
                    <div className="stat-label">{t('dashboard.searches_performed')}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {currentTab === 'recipes' && (
          <div className="recipes-content">
            <div className="glass-card">
              <h2>{t('recipes.saved_recipes')}</h2>
              {savedRecipes && savedRecipes.length > 0 ? (
                <div className="recipes-grid">
                  {savedRecipes.map((recipe) => (
                    <div key={recipe.id} className="recipe-card glass-card-sm">
                      <h3>{recipe.title}</h3>
                      <p>{recipe.description}</p>
                      <div className="recipe-meta">
                        <span>⏱️ {recipe.prepTime + recipe.cookTime}min</span>
                        <span>👥 {recipe.servings}</span>
                        <span>📊 {recipe.difficulty}</span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="empty-state">
                  <div className="empty-icon">📚</div>
                  <h3>{t('recipes.no_saved_recipes')}</h3>
                  <p>{t('recipes.no_saved_recipes_desc')}</p>
                  <button 
                    className="glass-button-primary"
                    onClick={() => handleTabChange('generate')}
                  >
                    {t('actions.generate_first_recipe')}
                  </button>
                </div>
              )}
            </div>
          </div>
        )}

        {currentTab === 'generate' && (
          <div className="generate-content">
            <div className="glass-card">
              <h2>{t('generate.title')}</h2>
              <p>{t('generate.description')}</p>
              <div className="coming-soon">
                <div className="coming-soon-icon">🚧</div>
                <h3>{t('messages.coming_soon')}</h3>
                <p>{t('generate.coming_soon_desc')}</p>
              </div>
            </div>
          </div>
        )}

        {currentTab === 'search' && (
          <div className="search-content">
            <div className="glass-card">
              <h2>{t('search.title')}</h2>
              <p>{t('search.description')}</p>
              <div className="coming-soon">
                <div className="coming-soon-icon">🔍</div>
                <h3>{t('messages.coming_soon')}</h3>
                <p>{t('search.coming_soon_desc')}</p>
              </div>
            </div>
          </div>
        )}

        {currentTab === 'trending' && (
          <div className="trending-content">
            <div className="glass-card">
              <h2>{t('trending.title')}</h2>
              <p>{t('trending.description')}</p>
              <div className="coming-soon">
                <div className="coming-soon-icon">🔥</div>
                <h3>{t('messages.coming_soon')}</h3>
                <p>{t('trending.coming_soon_desc')}</p>
              </div>
            </div>
          </div>
        )}

        {currentTab === 'settings' && (
          <div className="settings-content">
            <div className="glass-card">
              <h2>{t('settings.title')}</h2>
              <p>{t('settings.description')}</p>
              
              <div className="settings-sections">
                <div className="settings-section">
                  <h3>{t('settings.appearance')}</h3>
                  <div className="setting-item">
                    <label>{t('settings.theme')}</label>
                    <select className="glass-input">
                      <option value="auto">{t('settings.theme_auto')}</option>
                      <option value="light">{t('settings.theme_light')}</option>
                      <option value="dark">{t('settings.theme_dark')}</option>
                    </select>
                  </div>
                  
                  <div className="setting-item">
                    <label>{t('settings.glassmorphism_intensity')}</label>
                    <input type="range" min="0" max="100" defaultValue="50" className="glass-slider" />
                  </div>
                </div>

                <div className="settings-section">
                  <h3>{t('settings.ai_configuration')}</h3>
                  <div className="setting-item">
                    <label>{t('settings.openai_api_key')}</label>
                    <input 
                      type="password" 
                      className="glass-input" 
                      placeholder={t('settings.api_key_placeholder')}
                    />
                    <small>{t('settings.api_key_help')}</small>
                  </div>
                </div>

                <div className="settings-section">
                  <h3>{t('settings.privacy')}</h3>
                  <div className="setting-item">
                    <label className="checkbox-label">
                      <input type="checkbox" defaultChecked />
                      <span>{t('settings.allow_data_collection')}</span>
                    </label>
                  </div>
                  
                  <div className="setting-item">
                    <label className="checkbox-label">
                      <input type="checkbox" defaultChecked />
                      <span>{t('settings.share_anonymous_usage')}</span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
};

export default OptionsApp;
