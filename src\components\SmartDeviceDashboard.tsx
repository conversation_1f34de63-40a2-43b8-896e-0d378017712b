import React, { useState, useEffect } from 'react';
import { SmartDeviceService, SmartDevice, CookingAutomation, DeviceType } from '../services/SmartDeviceService';
import { TimerService, CookingTimer, CookingReminder } from '../services/TimerService';
import { useI18n } from '../hooks/useI18n';

interface SmartDeviceDashboardProps {
  onDeviceAction?: (deviceId: string, action: string) => void;
}

export const SmartDeviceDashboard: React.FC<SmartDeviceDashboardProps> = ({ onDeviceAction }) => {
  const { t } = useI18n();
  const [devices, setDevices] = useState<SmartDevice[]>([]);
  const [automations, setAutomations] = useState<CookingAutomation[]>([]);
  const [timers, setTimers] = useState<CookingTimer[]>([]);
  const [reminders, setReminders] = useState<CookingReminder[]>([]);
  const [isDiscovering, setIsDiscovering] = useState(false);
  const [activeTab, setActiveTab] = useState<'devices' | 'automations' | 'timers' | 'reminders'>('devices');

  const deviceService = SmartDeviceService.getInstance();
  const timerService = TimerService.getInstance();

  useEffect(() => {
    loadDashboardData();
    setupEventListeners();
    
    return () => {
      cleanupEventListeners();
    };
  }, []);

  const loadDashboardData = async () => {
    try {
      const [devicesData, automationsData, timersData, remindersData] = await Promise.all([
        deviceService.getDevices(),
        deviceService.getAutomations(),
        timerService.getTimers(),
        timerService.getReminders()
      ]);

      setDevices(devicesData);
      setAutomations(automationsData);
      setTimers(timersData);
      setReminders(remindersData);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    }
  };

  const setupEventListeners = () => {
    // Timer event listeners
    timerService.addTimerListener(handleTimerEvent);
    timerService.addReminderListener(handleReminderEvent);
  };

  const cleanupEventListeners = () => {
    timerService.removeTimerListener(handleTimerEvent);
    timerService.removeReminderListener(handleReminderEvent);
  };

  const handleTimerEvent = (event: string, timer: CookingTimer) => {
    setTimers(prev => {
      const updated = [...prev];
      const index = updated.findIndex(t => t.id === timer.id);
      if (index >= 0) {
        updated[index] = timer;
      } else {
        updated.push(timer);
      }
      return updated;
    });
  };

  const handleReminderEvent = (event: string, reminder: CookingReminder) => {
    setReminders(prev => {
      const updated = [...prev];
      const index = updated.findIndex(r => r.id === reminder.id);
      if (index >= 0) {
        updated[index] = reminder;
      } else {
        updated.push(reminder);
      }
      return updated;
    });
  };

  const handleDiscoverDevices = async () => {
    setIsDiscovering(true);
    try {
      const discoveredDevices = await deviceService.discoverDevices();
      setDevices(discoveredDevices);
    } catch (error) {
      console.error('Device discovery failed:', error);
    } finally {
      setIsDiscovering(false);
    }
  };

  const handleDeviceConnect = async (deviceId: string) => {
    try {
      const success = await deviceService.connectDevice(deviceId);
      if (success) {
        await loadDashboardData();
        onDeviceAction?.(deviceId, 'connect');
      }
    } catch (error) {
      console.error('Failed to connect device:', error);
    }
  };

  const handleDeviceDisconnect = async (deviceId: string) => {
    try {
      const success = await deviceService.disconnectDevice(deviceId);
      if (success) {
        await loadDashboardData();
        onDeviceAction?.(deviceId, 'disconnect');
      }
    } catch (error) {
      console.error('Failed to disconnect device:', error);
    }
  };

  const handleTimerAction = async (timerId: string, action: 'start' | 'pause' | 'stop' | 'reset') => {
    try {
      switch (action) {
        case 'start':
          await timerService.startTimer(timerId);
          break;
        case 'pause':
          await timerService.pauseTimer(timerId);
          break;
        case 'stop':
          await timerService.stopTimer(timerId);
          break;
        case 'reset':
          await timerService.resetTimer(timerId);
          break;
      }
    } catch (error) {
      console.error(`Failed to ${action} timer:`, error);
    }
  };

  const getDeviceIcon = (type: DeviceType): string => {
    const icons = {
      oven: '🔥',
      stovetop: '🍳',
      microwave: '📡',
      air_fryer: '💨',
      slow_cooker: '🍲',
      pressure_cooker: '⚡',
      sous_vide: '🌡️',
      grill: '🔥',
      thermometer: '🌡️',
      scale: '⚖️',
      timer: '⏰',
      refrigerator: '❄️',
      dishwasher: '🧽',
      coffee_maker: '☕',
      blender: '🌪️',
      food_processor: '⚙️'
    };
    return icons[type] || '📱';
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'connected': return '#4CAF50';
      case 'busy': return '#FF9800';
      case 'idle': return '#2196F3';
      case 'disconnected': return '#9E9E9E';
      case 'error': return '#F44336';
      default: return '#9E9E9E';
    }
  };

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const activeTimers = timers.filter(timer => timer.status === 'running' || timer.status === 'paused');
  const upcomingReminders = reminders.filter(reminder => 
    reminder.status === 'scheduled' && 
    reminder.scheduledTime > new Date() &&
    reminder.scheduledTime <= new Date(Date.now() + 24 * 60 * 60 * 1000)
  );

  return (
    <div className="smart-device-dashboard">
      <div className="dashboard-header glass-card">
        <h2>{t('smart_devices.dashboard_title')}</h2>
        <p>{t('smart_devices.dashboard_description')}</p>
        
        {/* Quick Stats */}
        <div className="quick-stats">
          <div className="stat-item">
            <span className="stat-value">{devices.filter(d => d.status === 'connected').length}</span>
            <span className="stat-label">{t('smart_devices.connected_devices')}</span>
          </div>
          <div className="stat-item">
            <span className="stat-value">{activeTimers.length}</span>
            <span className="stat-label">{t('smart_devices.active_timers')}</span>
          </div>
          <div className="stat-item">
            <span className="stat-value">{automations.filter(a => a.isActive).length}</span>
            <span className="stat-label">{t('smart_devices.active_automations')}</span>
          </div>
          <div className="stat-item">
            <span className="stat-value">{upcomingReminders.length}</span>
            <span className="stat-label">{t('smart_devices.upcoming_reminders')}</span>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="dashboard-tabs">
        {[
          { id: 'devices', name: t('smart_devices.devices'), icon: '📱' },
          { id: 'automations', name: t('smart_devices.automations'), icon: '🤖' },
          { id: 'timers', name: t('smart_devices.timers'), icon: '⏰' },
          { id: 'reminders', name: t('smart_devices.reminders'), icon: '🔔' }
        ].map((tab) => (
          <button
            key={tab.id}
            className={`dashboard-tab ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => setActiveTab(tab.id as any)}
          >
            <span className="tab-icon">{tab.icon}</span>
            <span className="tab-name">{tab.name}</span>
          </button>
        ))}
      </div>

      {/* Devices Tab */}
      {activeTab === 'devices' && (
        <div className="devices-section">
          <div className="section-header">
            <h3>{t('smart_devices.my_devices')}</h3>
            <button 
              className="discover-btn glass-button"
              onClick={handleDiscoverDevices}
              disabled={isDiscovering}
            >
              {isDiscovering ? t('smart_devices.discovering') : t('smart_devices.discover_devices')}
            </button>
          </div>

          <div className="devices-grid">
            {devices.map((device) => (
              <div key={device.id} className="device-card glass-card">
                <div className="device-header">
                  <div className="device-icon">{getDeviceIcon(device.type)}</div>
                  <div className="device-info">
                    <h4>{device.name}</h4>
                    <p>{device.brand} {device.model}</p>
                  </div>
                  <div 
                    className="device-status"
                    style={{ backgroundColor: getStatusColor(device.status) }}
                  >
                    {t(`smart_devices.status_${device.status}`)}
                  </div>
                </div>

                <div className="device-details">
                  <div className="device-capabilities">
                    {device.capabilities.slice(0, 3).map((capability) => (
                      <span key={capability} className="capability-tag">
                        {t(`smart_devices.capability_${capability}`)}
                      </span>
                    ))}
                    {device.capabilities.length > 3 && (
                      <span className="capability-tag">+{device.capabilities.length - 3}</span>
                    )}
                  </div>

                  <div className="device-meta">
                    <span className="connection-type">{device.connectionType.toUpperCase()}</span>
                    {device.batteryLevel && (
                      <span className="battery-level">🔋 {device.batteryLevel}%</span>
                    )}
                  </div>
                </div>

                <div className="device-actions">
                  {device.status === 'connected' ? (
                    <button 
                      className="device-action-btn disconnect glass-button-secondary"
                      onClick={() => handleDeviceDisconnect(device.id)}
                    >
                      {t('smart_devices.disconnect')}
                    </button>
                  ) : (
                    <button 
                      className="device-action-btn connect glass-button"
                      onClick={() => handleDeviceConnect(device.id)}
                    >
                      {t('smart_devices.connect')}
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>

          {devices.length === 0 && (
            <div className="empty-state glass-card">
              <h3>{t('smart_devices.no_devices')}</h3>
              <p>{t('smart_devices.no_devices_description')}</p>
              <button className="glass-button" onClick={handleDiscoverDevices}>
                {t('smart_devices.start_discovery')}
              </button>
            </div>
          )}
        </div>
      )}

      {/* Timers Tab */}
      {activeTab === 'timers' && (
        <div className="timers-section">
          <div className="section-header">
            <h3>{t('smart_devices.cooking_timers')}</h3>
          </div>

          <div className="timers-grid">
            {timers.map((timer) => (
              <div key={timer.id} className={`timer-card glass-card status-${timer.status}`}>
                <div className="timer-header">
                  <h4>{timer.name}</h4>
                  <span className={`timer-status ${timer.status}`}>
                    {t(`smart_devices.timer_${timer.status}`)}
                  </span>
                </div>

                <div className="timer-display">
                  <div className="timer-time">
                    {formatTime(timer.remainingTime)}
                  </div>
                  <div className="timer-progress">
                    <div 
                      className="timer-progress-bar"
                      style={{ 
                        width: `${((timer.duration - timer.remainingTime) / timer.duration) * 100}%` 
                      }}
                    ></div>
                  </div>
                </div>

                <div className="timer-meta">
                  <span className="timer-type">{t(`smart_devices.timer_type_${timer.type}`)}</span>
                  <span className="timer-duration">{formatTime(timer.duration)}</span>
                </div>

                <div className="timer-actions">
                  {timer.status === 'created' || timer.status === 'paused' ? (
                    <button 
                      className="timer-action-btn start glass-button"
                      onClick={() => handleTimerAction(timer.id, 'start')}
                    >
                      {timer.status === 'paused' ? t('smart_devices.resume') : t('smart_devices.start')}
                    </button>
                  ) : timer.status === 'running' ? (
                    <button 
                      className="timer-action-btn pause glass-button-secondary"
                      onClick={() => handleTimerAction(timer.id, 'pause')}
                    >
                      {t('smart_devices.pause')}
                    </button>
                  ) : null}
                  
                  {timer.status !== 'completed' && (
                    <button 
                      className="timer-action-btn stop glass-button-secondary"
                      onClick={() => handleTimerAction(timer.id, 'stop')}
                    >
                      {t('smart_devices.stop')}
                    </button>
                  )}
                  
                  <button 
                    className="timer-action-btn reset glass-button-secondary"
                    onClick={() => handleTimerAction(timer.id, 'reset')}
                  >
                    {t('smart_devices.reset')}
                  </button>
                </div>
              </div>
            ))}
          </div>

          {timers.length === 0 && (
            <div className="empty-state glass-card">
              <h3>{t('smart_devices.no_timers')}</h3>
              <p>{t('smart_devices.no_timers_description')}</p>
            </div>
          )}
        </div>
      )}

      {/* Automations Tab */}
      {activeTab === 'automations' && (
        <div className="automations-section">
          <div className="section-header">
            <h3>{t('smart_devices.cooking_automations')}</h3>
          </div>

          <div className="automations-grid">
            {automations.map((automation) => (
              <div key={automation.id} className="automation-card glass-card">
                <div className="automation-header">
                  <h4>{automation.name}</h4>
                  <div className="automation-toggle">
                    <input 
                      type="checkbox" 
                      checked={automation.isActive}
                      onChange={(e) => {
                        deviceService.updateAutomation(automation.id, { isActive: e.target.checked });
                        loadDashboardData();
                      }}
                    />
                    <span>{automation.isActive ? t('smart_devices.active') : t('smart_devices.inactive')}</span>
                  </div>
                </div>

                <div className="automation-details">
                  <div className="automation-devices">
                    <strong>{t('smart_devices.devices')}:</strong>
                    <div className="device-list">
                      {automation.devices.map(deviceId => {
                        const device = devices.find(d => d.id === deviceId);
                        return device ? (
                          <span key={deviceId} className="device-tag">
                            {getDeviceIcon(device.type)} {device.name}
                          </span>
                        ) : null;
                      })}
                    </div>
                  </div>

                  <div className="automation-steps">
                    <strong>{t('smart_devices.steps')}:</strong> {automation.steps.length}
                  </div>

                  <div className="automation-triggers">
                    <strong>{t('smart_devices.triggers')}:</strong>
                    {automation.triggers.map((trigger, index) => (
                      <span key={index} className="trigger-tag">
                        {t(`smart_devices.trigger_${trigger.type}`)}
                      </span>
                    ))}
                  </div>
                </div>

                {automation.lastExecuted && (
                  <div className="automation-last-run">
                    {t('smart_devices.last_executed')}: {automation.lastExecuted.toLocaleString()}
                  </div>
                )}
              </div>
            ))}
          </div>

          {automations.length === 0 && (
            <div className="empty-state glass-card">
              <h3>{t('smart_devices.no_automations')}</h3>
              <p>{t('smart_devices.no_automations_description')}</p>
            </div>
          )}
        </div>
      )}

      {/* Reminders Tab */}
      {activeTab === 'reminders' && (
        <div className="reminders-section">
          <div className="section-header">
            <h3>{t('smart_devices.cooking_reminders')}</h3>
          </div>

          <div className="reminders-list">
            {reminders.map((reminder) => (
              <div key={reminder.id} className={`reminder-card glass-card status-${reminder.status}`}>
                <div className="reminder-header">
                  <h4>{reminder.title}</h4>
                  <span className={`reminder-status ${reminder.status}`}>
                    {t(`smart_devices.reminder_${reminder.status}`)}
                  </span>
                </div>

                <div className="reminder-content">
                  <p>{reminder.message}</p>
                  <div className="reminder-meta">
                    <span className="reminder-time">
                      📅 {reminder.scheduledTime.toLocaleString()}
                    </span>
                    <span className="reminder-type">
                      {t(`smart_devices.reminder_type_${reminder.type}`)}
                    </span>
                    {reminder.isRecurring && (
                      <span className="reminder-recurring">
                        🔄 {t('smart_devices.recurring')}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {reminders.length === 0 && (
            <div className="empty-state glass-card">
              <h3>{t('smart_devices.no_reminders')}</h3>
              <p>{t('smart_devices.no_reminders_description')}</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};
